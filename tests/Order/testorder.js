/**
 * testorder.js - Unified Test Framework for Order API
 *
 * This script provides a flexible testing framework for the Order API with support for:
 * - Performance benchmarking between v1 and v3 APIs
 * - Concurrency testing to verify locking mechanisms
 * - Idempotency key testing
 * - Inventory validation testing
 *
 * Usage:
 * 1. Configure the test parameters in the config object below
 * 2. Run with: node testorder.js [test-type]
 *    Where [test-type] is one of:
 *    - benchmark (default): Compare performance between v1 and v3 APIs
 *    - concurrency: Test concurrent order creation to verify locking
 *    - idempotency: Test idempotency key handling
 *    - inventory: Test inventory validation with locking
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');

// Load order templates
let orderTemplates = [];
try {
    // Get all JSON files from the orderjson directory
    const orderJsonDir = path.join(__dirname, 'orderjson');
    orderTemplates = fs.readdirSync(orderJsonDir)
        .filter(file => file.endsWith('.json'))
        .map(file => require(path.join(orderJsonDir, file)));

    console.log(`Loaded ${orderTemplates.length} order templates`);
} catch (error) {
    console.warn(`Warning: Could not load order templates from orderjson directory: ${error.message}`);
    console.warn('Using default order template instead.');

    // Close testing
    process.exit(1);
}

// =====================================================================
// CONFIGURATION - Modify these settings to customize the test
// =====================================================================

const config = {
    // API Configuration
    api: {
        // basePath: 'http://localhost:8000',
        basePath: 'https://testapi.queuebuster.co/API/public',
    },

    // Test Configuration
    test: {
        // Test Type
        scenario: {
            cooldownMs: 5,         // Delay between requests (ms)
            timeoutMs: 3 * 60 * 1000, // Request timeout (ms)
            saveResults: true,       // Save results to file
            resultsDir: './scenario_results',
            apiVersion: 'v3',        // API version to test (v1 or v3)
            validateResponse: true   // Validate response structure
        },
        // Benchmark settings
        benchmark: {
            // iterations: 5,           // Number of requests per API version || moved as input param
            cooldownMs: 5,         // Delay between requests (ms)
            timeoutMs: 3 * 60 * 1000, // Request timeout (ms)
            saveResults: true,       // Save results to file
            resultsDir: './benchmark_results'
        },

        // Concurrency test settings
        concurrency: {
            // concurrentRequests: 2,  // Number of concurrent requests || moved as input param
            apiVersion: 'v3',        // API version to test (v1 or v3)
            timeoutMs: 3 * 60 * 1000     // Request timeout (ms)
        },

        // Idempotency test settings
        idempotency: {
            repeats: 3,              // Number of times to repeat the same request
            apiVersion: 'v3',        // API version to test (v1 or v3)
            timeoutMs: 3 * 60 * 1000 // Request timeout (ms)
        },

        // Inventory validation test settings
        inventory: {
            // Set to true to test with insufficient inventory
            testInsufficientInventory: false,
            // Product to test (should be inventory managed)
            productID: 1,
            variantID: 1,
            // Quantity to order (set higher than available inventory for insufficient test)
            quantity: 999,
            apiVersion: 'v3',        // API version to test (v1 or v3)
            timeoutMs: 3 * 60 * 1000 // Request timeout (ms)
        }
    }
};

// =====================================================================
// HELPER FUNCTIONS
// =====================================================================

// Format time in ms to a readable string
function formatTime(ms) {
    if (ms < 1000) return `${ms.toFixed(2)}ms`;
    return `${(ms/1000).toFixed(2)}s`;
}

/**
 * Create a random order for testing
 *
 * @param {Object} options - Options for creating the order
 * @param {Object} options.inventory - Optional inventory settings for testing inventory validation
 * @param {number} options.templateIndex - Optional specific template index to use (for consistent benchmark testing)
 * @returns {Object} The order data and metadata
 */
function createRandomOrder(options = {}) {
    // Get order template - either use provided template index or select random
    const templateIndex = options.templateIndex !== undefined ? 
        options.templateIndex : 
        Math.floor(Math.random() * orderTemplates.length);
    const template = orderTemplates[templateIndex];
    
    if (options.templateIndex === undefined) {
        console.log(`Using template: ${templateIndex}`);
    }

    // Clone the template to avoid modifying the original
    const orderData = JSON.parse(JSON.stringify(template));

    // Generate a unique order ID
    const random = Math.floor(Math.random() * 10000);
    const randomDeviceID = (Math.floor(Math.random() * 10000) + parseInt(orderData.deviceID)) + "" + options.version ?? '';
    const jsonOrderID = orderData.orderID;
    const prefix = jsonOrderID.split('-').length === 2 ? 'OR' : jsonOrderID.split('-')[0];
    const orderID = `${prefix}-${randomDeviceID}-${random}`;
    console.log(jsonOrderID + " <> " + orderID);
    // Generate a unique invoice number
    const invoiceNumber = `${random}`;

    // Update order data with current values
    orderData.orderID = orderID;
    orderData.invoiceNumber = invoiceNumber;
    orderData.storeID = orderData.storeID.toString();
    orderData.deviceID = orderData.deviceID.toString();

    // Set current timestamps
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0];
    const timeStr = now.toISOString().replace('T', ' ').slice(0, 19);

    if (orderData.orderDate) orderData.orderDate = dateStr;
    if (orderData.orderTime) orderData.orderTime = now.toTimeString().slice(0, 8);
    if (orderData.billSettledTimeLocal) orderData.billSettledTimeLocal = timeStr;
    if (orderData.orderCreationTimeLocal) orderData.orderCreationTimeLocal = timeStr;
    if (orderData.lastModifiedTimeLocal) orderData.lastModifiedTimeLocal = timeStr;
    if (orderData.posDate) orderData.posDate = dateStr;

    // If testing inventory validation, modify the product data
    if (options.inventory) {
        // Make sure productsList exists
        if (!orderData.productsList || !Array.isArray(orderData.productsList)) {
            orderData.productsList = [];
        }

        // Add or update the test product
        const testProduct = {
            productID: options.inventory.productID,
            variantID: options.inventory.variantID,
            productName: "Test Inventory Product",
            productActualPrice: 50.00,
            productDiscountedPrice: 45.00,
            quantityOrdered: options.inventory.quantity,
            isInventoryManaged: 1,
            enableProductGroup: options.inventory.enableProductGroup || 0,
            taxPercentage: 0.00,
            taxAmount: 0.00
        };

        // Replace existing product or add new one
        const existingIndex = orderData.productsList.findIndex(p =>
            p.productID === testProduct.productID && p.variantID === testProduct.variantID);

        if (existingIndex >= 0) {
            orderData.productsList[existingIndex] = testProduct;
        } else {
            orderData.productsList.push(testProduct);
        }

        // Update order totals
        const totalAmount = testProduct.productDiscountedPrice * testProduct.quantityOrdered;
        orderData.grossBill = totalAmount;
        orderData.netBill = totalAmount;

        // Update payment if it exists
        if (orderData.paymentList && Array.isArray(orderData.paymentList) && orderData.paymentList.length > 0) {
            orderData.paymentList[0].paymentAmount = totalAmount;
        }
    }

    return {
        orderData,
        orderID,
        invoiceNumber
    };
}

/**
 * Get API endpoint URL based on version
 *
 * @param {string} version - API version (v1 or v3)
 * @param {number} storeID - Store ID
 * @returns {string} The API endpoint URL
 */
function getApiEndpoint(version, storeID) {
    return `${config.api.basePath}/${version}/merchant/${storeID}/order`;
}

// Removed unused createRequestConfig function

// =====================================================================
// TEST IMPLEMENTATIONS
// =====================================================================

/**
 * Send a single order to the API
 *
 * @param {Object} options - Request options
 * @param {string} options.version - API version (v1 or v3)
 * @param {Object} options.orderData - Order data to send
 * @param {number} options.timeout - Request timeout in ms
 * @returns {Object} Response data and metrics
 */
async function sendOrder(options) {
    const { version, orderData } = options;
    const orderID = options.orderID || orderData.orderID || 'unknown';
    const storeID = orderData.storeID || orderData.deviceID || 0;
    const chainID = orderData.chainID || 0;
    const deviceID = orderData.deviceID || 0;
    const userID = orderData.userID || -1;

    // Log key information about the order being tested
    console.log(`Sending order ${orderID} to ${version} API...`);
    console.log(`Chain ID: ${chainID}, Store ID: ${storeID}, Device ID: ${deviceID}`);

    // Create a copy of the order data to avoid modifying the original
    const orderDataCopy = JSON.parse(JSON.stringify(orderData));
    
    // In a real test environment, we might need to adjust some fields
    // to ensure the order is valid for the current test environment
    // This is a placeholder for such adjustments
    
    // Create request configuration
    const requestConfig = {
        method: 'post',
        url: getApiEndpoint(version, storeID),
        maxBodyLength: Infinity,
        timeout: options.timeout || 60000,
        headers: {
            'Content-Type': 'application/json'
        },
        data: orderDataCopy
    };

    // Add authorization header if token is available
    if (orderData.token) {
        requestConfig.headers['Authorization'] = `Bearer ${orderData.token}`;
    }
    
    // Add required headers if available
    if (storeID) requestConfig.headers['storeID'] = storeID.toString();
    if (chainID) requestConfig.headers['chainID'] = chainID.toString();
    if (userID !== undefined) requestConfig.headers['userID'] = userID.toString();
    if (deviceID) requestConfig.headers['deviceID'] = deviceID.toString();

    // Start timing
    const startTime = performance.now();
    let metrics = { totalTime: 0, networkTime: 0, processingTime: 0 };

    try {
        // Send request
        const response = await axios(requestConfig);

        // Calculate duration
        const endTime = performance.now();
        const duration = endTime - startTime;
        metrics.totalTime = duration;

        // Determine status based on API version
        const status = (response.data.orderCreationReport?.status || 
                        response.data.status || 
                        'Created');

        // Get performance metrics for v3 API
        const queryCount = version === 'v3' && response.data.performance
            ? response.data.performance.queryCount
            : null;

        console.log(`Order ${orderID}: ${formatTime(metrics.totalTime)} - ${status}${
            queryCount ? ` - Queries: ${queryCount}` : ''
        }`);

        return {
            success: true,
            duration,
            metrics,
            orderID,
            status,
            queryCount,
            data: response.data,
            response: response.data
        };
    } catch (error) {
        // Calculate duration even for failed requests
        const endTime = performance.now();
        const duration = endTime - startTime;
        metrics.totalTime = duration;

        console.error(`Order ${orderID} FAILED: ${formatTime(metrics.totalTime)} - ${error.message}`);
        
        // Log more detailed error information if available
        if (error.response) {
            console.error(`Status: ${error.response.status}`);
            if (error.response.data) {
                const errorData = typeof error.response.data === 'string' 
                    ? error.response.data 
                    : JSON.stringify(error.response.data, null, 2);
                console.error(`Response data: ${errorData.substring(0, 500)}${errorData.length > 500 ? '...' : ''}`);
            }
        }

        return {
            success: false,
            duration,
            metrics,
            orderID,
            error: error.message,
            response: error.response?.data || null
        };
    }
}

/**
 * Run performance benchmark comparing v1 and v3 APIs
 */
async function runBenchmark(iterations) {
    console.log('\n========== PERFORMANCE BENCHMARK ==========');
    console.log(`Comparing v1 and v3 APIs`);
    console.log(`Iterations: ${iterations}`);
    console.log(`Cooldown: ${config.test.benchmark.cooldownMs}ms`);
    console.log('------------------------------------------');

    // Prepare results directory
    if (config.test.benchmark.saveResults && !fs.existsSync(config.test.benchmark.resultsDir)) {
        fs.mkdirSync(config.test.benchmark.resultsDir, { recursive: true });
    }

    // Initialize results
    const results = {
        v1: {
            name: 'Current API (v1)',
            successes: 0,
            failures: 0,
            totalTime: 0,
            minTime: Number.MAX_VALUE,
            maxTime: 0,
            avgTime: 0,
            orders: [],
            errors: [],
            startTime: null,
            endTime: null
        },
        v3: {
            name: 'New API (v3)',
            successes: 0,
            failures: 0,
            totalTime: 0,
            minTime: Number.MAX_VALUE,
            maxTime: 0,
            avgTime: 0,
            orders: [],
            errors: [],
            startTime: null,
            endTime: null
        }
    };

    // Run iterations
    for (let i = 0; i < iterations; i++) {
        // Select the same random template index for both API versions
        const templateIndex = Math.floor(Math.random() * orderTemplates.length);
        console.log(`\nIteration ${i+1}/${iterations} - Using template index: ${templateIndex}`);
        
        // Test each API version with the same template
        for (const version of ['v1', 'v3']) {
            if (i === 0) {
                console.log(`\nTesting ${version} API...`);
                results[version].startTime = new Date().toISOString();
            }
            
            // Create an order using the same template for both versions
            const { orderData, orderID } = createRandomOrder({ version, templateIndex });

            // Send order
            const response = await sendOrder({
                version,
                orderData,
                orderID,
                timeout: config.test.benchmark.timeoutMs
            });

            // Store result
            if (response.success) {
                results[version].successes++;
                results[version].totalTime += response.duration;
                results[version].minTime = Math.min(results[version].minTime, response.duration);
                results[version].maxTime = Math.max(results[version].maxTime, response.duration);
                results[version].orders.push({
                    orderID: response.orderID,
                    duration: response.duration,
                    status: response.status,
                    queryCount: response.queryCount
                });
            } else {
                results[version].failures++;
                results[version].errors.push({
                    orderID: response.orderID,
                    duration: response.duration,
                    error: response.error,
                    response: response.response
                });
            }
            
            // Add cooldown between requests (except for the last one)
            if (!(i === iterations - 1 && version === 'v3')) {
                await new Promise(resolve => setTimeout(resolve, config.test.benchmark.cooldownMs));
            }
        }
    }
    
    // Process results for each version after all iterations are complete
    for (const version of ['v1', 'v3']) {
        results[version].endTime = new Date().toISOString();

        // Calculate average time
        if (results[version].successes > 0) {
            results[version].avgTime = results[version].totalTime / results[version].successes;
        }

        // Print summary
        console.log(`\n----- ${version} API Results -----`);
        console.log(`Total Requests: ${iterations}`);
        console.log(`Successful: ${results[version].successes}`);
        console.log(`Failed: ${results[version].failures}`);

        if (results[version].successes > 0) {
            console.log(`Average Time: ${formatTime(results[version].avgTime)}`);
            console.log(`Min Time: ${formatTime(results[version].minTime)}`);
            console.log(`Max Time: ${formatTime(results[version].maxTime)}`);

            // Add query count information for v3 API
            if (version === 'v3') {
                const queryCounts = results[version].orders
                    .map(order => order.queryCount)
                    .filter(count => count !== null);

                if (queryCounts.length > 0) {
                    const avgQueryCount = queryCounts.reduce((sum, count) => sum + count, 0) / queryCounts.length;
                    console.log(`Average Query Count: ${avgQueryCount.toFixed(2)}`);
                }
            }
        }

        // Save results to file
        if (config.test.benchmark.saveResults) {
            const filename = path.join(
                config.test.benchmark.resultsDir,
                `benchmark_${version}_${new Date().toISOString().replace(/:/g, '-')}.json`
            );
            fs.writeFileSync(filename, JSON.stringify(results[version], null, 2));
            console.log(`Results saved to ${filename}`);
        }
    }

    // Print comparison
    console.log('\n========== BENCHMARK COMPARISON ==========');
    if (results.v1.successes > 0 && results.v3.successes > 0) {
        const improvement = ((results.v1.avgTime - results.v3.avgTime) / results.v1.avgTime) * 100;
        console.log(`v1 API Average: ${formatTime(results.v1.avgTime)}`);
        console.log(`v3 API Average: ${formatTime(results.v3.avgTime)}`);
        console.log(`Improvement: ${improvement.toFixed(2)}%`);
    } else {
        console.log('Cannot compare results - not enough successful requests');
    }
}

/**
 * Run concurrency test to verify locking mechanisms
 */
async function runConcurrencyTest(iterations) {
    console.log('\n========== CONCURRENCY TEST ==========');
    console.log(`Testing ${config.test.concurrency.apiVersion} API with ${iterations} concurrent requests`);
    console.log('--------------------------------------');

    const version = config.test.concurrency.apiVersion;
    const concurrentRequests = iterations;

    // Create an array of promises and request configs
    const requestConfigs = [];
    const orders = [];

    // Prepare all order data first
    for (let i = 0; i < concurrentRequests; i++) {
        // Create a random order with different product IDs to test inventory locking
        const { orderData, orderID } = createRandomOrder({
            version,
            // inventory: {
            //     productID: i + 1, // Use different product IDs
            //     variantID: 1,
            //     quantity: 1,
            //     enableProductGroup: i % 2 // Alternate between 0 and 1
            // }
        });

        // Store order info
        orders.push({ orderID });

        // Create request config
        requestConfigs.push({
            method: 'post',
            url: getApiEndpoint(version, orderData.storeID),
            maxBodyLength: Infinity,
            timeout: config.test.concurrency.timeoutMs,
            headers: {
                'Authorization': `Bearer ${orderData.token}`,
                'Content-Type': 'application/json',
                'storeID': orderData.storeID.toString(),
                'chainID': orderData.chainID.toString(),
                'userID': orderData.userID.toString(),
                'deviceID': orderData.deviceID.toString()
            },
            data: orderData,
            metadata: { orderID } // Store metadata for later reference
        });
    }

    console.log(`Prepared ${concurrentRequests} orders for concurrent testing`);

    // Create timestamp for measuring request duration
    const startTime = performance.now();

    // Log when requests are about to be sent
    console.log(`Sending ${concurrentRequests} concurrent requests at ${new Date().toISOString()}...`);

    // Create all request promises using direct axios calls
    const promises = requestConfigs.map(config => {
        const { metadata, ...axiosConfig } = config;

        return axios(axiosConfig)
            .then(response => {
                const endTime = performance.now();
                const duration = endTime - startTime;
                console.log(response.data);
                const status = (response.data.orderCreationReport?.status || 'Unknown');
                const queryCount = response.data.performance ? response.data.performance.queryCount : null;

                console.log(`Order ${metadata.orderID}: ${formatTime(duration)} - ${status}${
                    queryCount ? ` - Queries: ${queryCount}` : ''
                }`);

                return {
                    success: true,
                    duration,
                    orderID: metadata.orderID,
                    status,
                    queryCount,
                    data: response.data
                };
            })
            .catch(error => {
                const endTime = performance.now();
                const duration = endTime - startTime;

                console.error(`Order ${metadata.orderID} FAILED: ${formatTime(duration)} - ${error.message}`);

                return {
                    success: false,
                    duration,
                    orderID: metadata.orderID,
                    error: error.message,
                    response: error.response?.data || null
                };
            });
    });

    // Wait for all promises to resolve
    const results = await Promise.all(promises);

    // Calculate statistics
    const successCount = results.filter(r => r.success).length;
    const failCount = results.length - successCount;
    const avgTime = results.reduce((sum, r) => sum + r.duration, 0) / results.length;

    // Print results
    console.log('\n----- Concurrency Test Results -----');
    console.log(`Success: ${successCount}, Failed: ${failCount}, Avg Time: ${formatTime(avgTime)}`);

    // Check for specific errors related to locking
    const lockErrors = results.filter(r =>
        !r.success &&
        (r.error.includes('lock') ||
         r.error.includes('deadlock') ||
         (r.response && r.response.message &&
          (r.response.message.includes('lock') ||
           r.response.message.includes('deadlock'))))
    );

    if (lockErrors.length > 0) {
        console.log(`\nLock-related errors: ${lockErrors.length}`);
        lockErrors.forEach((error, index) => {
            console.log(`${index + 1}. Order ${error.orderID}: ${error.error}`);
        });
    } else {
        console.log('\nNo lock-related errors detected.');
    }

    // Check for duplicate order IDs in successful responses
    const successfulOrderIDs = results
        .filter(r => r.success)
        .map(r => r.orderID);

    const uniqueOrderIDs = new Set(successfulOrderIDs);

    if (uniqueOrderIDs.size !== successfulOrderIDs.length) {
        console.log('\nWARNING: Duplicate order IDs detected in successful responses!');
        console.log('This indicates a potential issue with the locking mechanism.');
    } else {
        console.log('\nAll successful orders have unique order IDs.');
    }
}

/**
 * Run idempotency key test
 */
async function runIdempotencyTest() {
    console.log('\n========== IDEMPOTENCY TEST ==========');
    console.log(`Testing ${config.test.idempotency.apiVersion} API with ${config.test.idempotency.repeats} repeated requests`);
    console.log('-------------------------------------');

    const version = config.test.idempotency.apiVersion;
    const repeats = config.test.idempotency.repeats;

    // Only run this test for v3 API
    if (version !== 'v3') {
        console.log('Idempotency test is only applicable for v3 API');
        return;
    }

    // Create a random order with a fixed idempotency key
    const { orderData, orderID } = createRandomOrder({ version });
    console.log(`Using order ID: ${orderID}`);

    // Send the same request multiple times
    const results = [];

    for (let i = 0; i < repeats; i++) {
        console.log(`\nSending request ${i + 1}/${repeats}...`);

        const result = await sendOrder({
            version,
            orderData,
            orderID,
            timeout: config.test.idempotency.timeoutMs
        });

        results.push(result);
    }

    // Check if all responses are identical
    let allIdentical = true;
    let allSuccessful = true;

    for (let i = 1; i < results.length; i++) {
        if (!results[i].success || !results[0].success) {
            allSuccessful = false;
        }

        if (results[i].success && results[0].success) {
            const dataStr1 = JSON.stringify(results[0].data);
            const dataStr2 = JSON.stringify(results[i].data);

            if (dataStr1 !== dataStr2) {
                allIdentical = false;
                break;
            }
        }
    }

    // Print results
    console.log('\n----- Idempotency Test Results -----');
    console.log(`All requests successful: ${allSuccessful}`);
    console.log(`All responses identical: ${allIdentical}`);

    if (!allSuccessful) {
        console.log('\nFailed requests:');
        results.forEach((result, index) => {
            if (!result.success) {
                console.log(`Request ${index + 1}: ${result.error}`);
            }
        });
    }

    if (!allIdentical && allSuccessful) {
        console.log('\nWARNING: Responses are not identical despite using the same idempotency key!');
        console.log('This indicates a potential issue with the idempotency key handling.');
    } else if (allIdentical && allSuccessful) {
        console.log('\nIdempotency key handling is working correctly.');
    }
}

/**
 * Run inventory validation test
 */
async function runInventoryTest() {
    console.log('\n========== INVENTORY VALIDATION TEST ==========');
    console.log(`Testing ${config.test.inventory.apiVersion} API with ${config.test.inventory.testInsufficientInventory ? 'insufficient' : 'sufficient'} inventory`);
    console.log('----------------------------------------------');

    const version = config.test.inventory.apiVersion;
    const testInsufficientInventory = config.test.inventory.testInsufficientInventory;

    // Create an order with specific inventory settings
    const { orderData, orderID } = createRandomOrder({
        inventory: {
            productID: config.test.inventory.productID,
            variantID: config.test.inventory.variantID,
            quantity: config.test.inventory.quantity,
            enableProductGroup: 1 // Test with product variant
        },
        version
    });

    console.log(`Testing product ID: ${config.test.inventory.productID}, variant ID: ${config.test.inventory.variantID}`);
    console.log(`Quantity: ${config.test.inventory.quantity}`);
    console.log(`Expected result: ${testInsufficientInventory ? 'Insufficient inventory error' : 'Success'}`);

    // Send the order
    const result = await sendOrder({
        version,
        orderData,
        orderID,
        timeout: config.test.inventory.timeoutMs
    });

    // Check if the result matches the expected outcome
    let testPassed = false;

    if (testInsufficientInventory) {
        // If testing insufficient inventory, we expect an error
        testPassed = !result.success &&
                    (result.response &&
                     (result.response.errorCode === 'INSUFFICIENT_STOCK' ||
                      (result.response.message && result.response.message.includes('inventory'))));
    } else {
        // If testing sufficient inventory, we expect success
        testPassed = result.success;
    }

    // Print results
    console.log('\n----- Inventory Test Results -----');
    console.log(`Test passed: ${testPassed}`);
    console.log(`Request ${result.success ? 'succeeded' : 'failed'}`);

    if (!result.success) {
        console.log(`Error: ${result.error}`);
        if (result.response) {
            console.log(`Response: ${JSON.stringify(result.response, null, 2)}`);
        }
    }
}

/**
 * Run scenario test for each JSON file in the orderjson folder
 */
async function runScenarioTest(tests) {
    console.log('\n========== SCENARIO TEST ==========');
    console.log(`Testing all order templates with ${config.test.scenario.apiVersion} API`);
    console.log(`Tests: ${tests}`);
    console.log('---------------------------------------');

    const version = config.test.scenario.apiVersion;
    const results = [];
    const successCount = { total: 0, count: 0 };
    const failureCount = { total: 0, count: 0 };
    
    // Create results directory if it doesn't exist
    if (config.test.scenario.saveResults) {
        if (!fs.existsSync(config.test.scenario.resultsDir)) {
            fs.mkdirSync(config.test.scenario.resultsDir, { recursive: true });
        }
    }

    console.log(`Found ${orderTemplates.length} order templates to test`);

    // if tests to run are not provided, run all tests
    // else only run the number of tests provided
    if (tests && tests) {
        orderTemplates = orderTemplates.slice(0, tests);
    }

    // Run test for each order template
    for (let i = 0; i < orderTemplates.length; i++) {
        const template = orderTemplates[i];
        const templateIndex = i + 1;
        console.log(`\nTesting template ${templateIndex}/${orderTemplates.length}: ${template.orderID || 'Unknown ID'}`);

        // Create a random order with a fixed idempotency key
        const { orderData } = createRandomOrder({ version });

        // Log key characteristics of this order template
        console.log(`Order type: ${orderData.orderType}`);
        console.log(`Payment methods: ${orderData.paymentList?.map(p => p.printName).join(', ') || 'None'}`);
        console.log(`Gross amount: ${orderData.grossBill}`);
        console.log(`Payment status: ${orderData.paymentStatus}`);
        
        // Send the order using the template
        const result = await sendOrder({
            version,
            orderData,
            timeout: config.test.scenario.timeoutMs
        });

        // Update counters
        if (result.success && result.metrics) {
            successCount.count++;
            successCount.total += result.metrics.totalTime || 0;
        } else {
            failureCount.count++;
            failureCount.total += (result.metrics ? result.metrics.totalTime : 0);
        }

        results.push({
            templateIndex,
            orderID: template.orderID,
            success: result.success,
            time: result.metrics ? result.metrics.totalTime : 0,
            error: result.error,
            response: result.response
        });

        // Validate the response structure if required
        if (config.test.scenario.validateResponse && result.success) {
            console.log('Validating response structure...');
            const validationResult = validateResponseStructure(result.response);
            console.log(`Response validation: ${validationResult.valid ? 'PASSED' : 'FAILED'}`);
            
            if (!validationResult.valid) {
                console.log(`Validation errors: ${validationResult.errors.join('\n')}`);
            }
            
            // Add validation result to the results
            results[results.length - 1].validation = validationResult;
        }

        // Wait for cooldown before next request
        if (i < orderTemplates.length - 1) {
            console.log(`Waiting ${config.test.scenario.cooldownMs}ms before next test...`);
            await new Promise(resolve => setTimeout(resolve, config.test.scenario.cooldownMs));
        }
    }

    // Calculate statistics
    const avgSuccessTime = successCount.count > 0 ? successCount.total / successCount.count : 0;
    const avgFailureTime = failureCount.count > 0 ? failureCount.total / failureCount.count : 0;

    // Print summary
    console.log('\n----- Scenario Test Summary -----');
    console.log(`Total templates tested: ${orderTemplates.length}`);
    console.log(`Successful: ${successCount.count} (${((successCount.count / orderTemplates.length) * 100).toFixed(1)}%)`);
    console.log(`Failed: ${failureCount.count} (${((failureCount.count / orderTemplates.length) * 100).toFixed(1)}%)`);
    console.log(`Average response time (success): ${formatTime(avgSuccessTime)}`);
    console.log(`Average response time (failure): ${formatTime(avgFailureTime)}`);

    // Save results to file if enabled
    if (config.test.scenario.saveResults) {
        const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\./g, '-');
        const fileName = `${config.test.scenario.resultsDir}/scenario_test_${timestamp}.json`;
        
        const summaryResults = {
            timestamp: new Date().toISOString(),
            apiVersion: version,
            totalTemplates: orderTemplates.length,
            successCount: successCount.count,
            failureCount: failureCount.count,
            avgSuccessTime,
            avgFailureTime,
            results
        };
        
        fs.writeFileSync(fileName, JSON.stringify(summaryResults, null, 2));
        console.log(`\nResults saved to ${fileName}`);
    }

    return {
        success: failureCount.count === 0,
        totalTemplates: orderTemplates.length,
        successCount: successCount.count,
        failureCount: failureCount.count
    };
}

/**
 * Validate the response structure of a successful order request
 * @param {Object} response - The response to validate
 * @returns {Object} validation result with valid flag and errors
 */
function validateResponseStructure(response) {
    const result = { valid: true, errors: [] };
    
    // Check if response exists
    if (!response) {
        result.valid = false;
        result.errors.push('Response is undefined or null');
        return result;
    }
    
    // Check if status indicates success
    if (response.orderCreationReport.status != "Created") {
        result.valid = false;
        result.errors.push(`Unexpected status value: ${response.orderCreationReport.status}`);
        console.error('Response: ' + JSON.stringify(response));
    }
    
    // Additional validations can be added based on specific requirements
    
    return result;
}

// =====================================================================
// MAIN EXECUTION
// =====================================================================

/**
 * Main function to run the selected test
 */
async function main() {
    // Determine which test to run based on command line argument
    const testType = process.argv[2] || 'benchmark';
    let iterations = process.argv[3] || 5;

    if (testType === 'scenario') {
        // move default iterations to 100000
        iterations = process.argv[3] || 100000;
    }

    console.log(`Starting Order API Test: ${testType}`);
    console.log(`Time: ${new Date().toISOString()}`);

    try {
        switch (testType) {
            case 'benchmark':
                await runBenchmark(iterations);
                break;
            case 'concurrency':
                await runConcurrencyTest(iterations);
                break;
            case 'idempotency':
                await runIdempotencyTest();
                break;
            case 'inventory':
                await runInventoryTest();
                break;
            case 'scenario':
                await runScenarioTest(iterations);
                break;
            default:
                console.error(`Unknown test type: ${testType}`);
                console.log('Available test types: benchmark, concurrency, idempotency, inventory, scenario');
                process.exit(1);
        }

        console.log(`\nTest completed at ${new Date().toISOString()}`);
    } catch (error) {
        console.error('Test failed:', error);
    }
}

// Run the main function
main();