<?php

namespace Tests\Unit;

use App\v3\OrderSummaryBuilder;
use App\v3\Order;
use App\Services\DatabaseManager;
use App\TableRegistry;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;
use Mockery;
use Mo<PERSON>y\MockInterface;

class OrderSummaryBuilderTest extends TestCase
{
    /**
     * @var MockInterface|DatabaseManager
     */
    private $mockDbManager;

    /**
     * @var MockInterface
     */
    private $mockDB;

    /**
     * @var OrderSummaryBuilder
     */
    private $builder;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the DB facade
        $this->mockDB = Mockery::mock('alias:Illuminate\\Support\\Facades\\DB');

        // Mock the DatabaseManager
        $this->mockDbManager = Mockery::mock(DatabaseManager::class);
        
        // Create the builder with our mocked DB manager
        $this->builder = new OrderSummaryBuilder($this->mockDbManager);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test that the batch size can be set and retrieved
     */
    public function testCanSetBatchSize()
    {
        $reflection = new \ReflectionClass($this->builder);
        $property = $reflection->getProperty('batchSize');
        $property->setAccessible(true);

        // Default value should be 1000
        $this->assertEquals(1000, $property->getValue($this->builder));

        // Set a new value
        $this->builder->setBatchSize(500);
        $this->assertEquals(500, $property->getValue($this->builder));
    }

    /**
     * Test adding a store order summary
     */
    public function testAddStoreOrderSummary()
    {
        $storeID = 1;
        $tableName = 'store_1_orderSummary';
        
        // Mock TableRegistry
        $mockTableRegistry = Mockery::mock('alias:App\\TableRegistry');
        $mockTableRegistry->shouldReceive('getStoreTable')
            ->with($storeID, 'orderSummary')
            ->once()
            ->andReturn($tableName);

        $summary = ['orderID' => 123, 'total' => 100.00];
        
        $this->builder->addStoreOrderSummary($storeID, $summary);
        
        $reflection = new \ReflectionClass($this->builder);
        
        $storeTablesProperty = $reflection->getProperty('storeTables');
        $storeTablesProperty->setAccessible(true);
        $this->assertEquals([$storeID => $tableName], $storeTablesProperty->getValue($this->builder));
        
        $storeOrderSummariesProperty = $reflection->getProperty('storeOrderSummaries');
        $storeOrderSummariesProperty->setAccessible(true);
        $this->assertEquals([$storeID => [123 => $summary]], $storeOrderSummariesProperty->getValue($this->builder));
    }

    /**
     * Test adding a chain order summary
     */
    public function testAddChainOrderSummary()
    {
        $chainID = 1;
        $tableName = 'chain_1_orderSummary';
        
        // Mock TableRegistry
        $mockTableRegistry = Mockery::mock('alias:App\\TableRegistry');
        $mockTableRegistry->shouldReceive('getChainTable')
            ->with($chainID, 'orderSummary')
            ->once()
            ->andReturn($tableName);

        $summary = ['orderID' => 123, 'total' => 100.00];
        
        $this->builder->addChainOrderSummary($chainID, $summary);
        
        $reflection = new \ReflectionClass($this->builder);
        
        $chainTablesProperty = $reflection->getProperty('chainTables');
        $chainTablesProperty->setAccessible(true);
        $this->assertEquals([$chainID => $tableName], $chainTablesProperty->getValue($this->builder));
        
        $chainOrderSummariesProperty = $reflection->getProperty('chainOrderSummaries');
        $chainOrderSummariesProperty->setAccessible(true);
        $this->assertEquals([$chainID => [123 => $summary]], $chainOrderSummariesProperty->getValue($this->builder));
    }

    /**
     * Test executing batch inserts for store order summaries
     */
    public function testExecuteForStoreOrderSummaries()
    {
        $storeID = 1;
        $tableName = 'store_1_orderSummary';
        
        // Mock TableRegistry
        $mockTableRegistry = Mockery::mock('alias:App\\TableRegistry');
        $mockTableRegistry->shouldReceive('getStoreTable')
            ->with($storeID, 'orderSummary')
            ->once()
            ->andReturn($tableName);

        // Prepare test data
        $summaries = [
            ['orderID' => 123, 'total' => 100.00],
            ['orderID' => 124, 'total' => 200.00],
        ];
        
        foreach ($summaries as $summary) {
            $this->builder->addStoreOrderSummary($storeID, $summary);
        }

        // Mock the DB facade table chain
        $mockQueryBuilder = Mockery::mock('\Illuminate\Database\Query\Builder');
        $mockQueryBuilder->shouldReceive('insert')->once()->andReturn(true);

        $this->mockDB->shouldReceive('table')->with($tableName)->once()->andReturn($mockQueryBuilder);

        // Mock DatabaseManager to execute with retry
        $this->mockDbManager->shouldReceive('executeWithRetry')
            ->once()
            ->andReturnUsing(function ($callback) {
                return $callback();
            });

        // Execute and check results
        $result = $this->builder->execute();
        
        $this->assertTrue($result['status']);
        $this->assertEquals(2, $result['storeInserts']);
        $this->assertEquals(0, $result['chainInserts']);
        $this->assertEmpty($result['errors']);
    }

    /**
     * Test executing batch inserts for chain order summaries
     */
    public function testExecuteForChainOrderSummaries()
    {
        $chainID = 1;
        $tableName = 'chain_1_orderSummary';
        
        // Mock TableRegistry
        $mockTableRegistry = Mockery::mock('alias:App\\TableRegistry');
        $mockTableRegistry->shouldReceive('getChainTable')
            ->with($chainID, 'orderSummary')
            ->once()
            ->andReturn($tableName);

        // Prepare test data
        $summaries = [
            ['orderID' => 123, 'total' => 100.00],
            ['orderID' => 124, 'total' => 200.00],
        ];
        
        foreach ($summaries as $summary) {
            $this->builder->addChainOrderSummary($chainID, $summary);
        }

        // Mock the DB facade table chain
        $mockQueryBuilder = Mockery::mock('\Illuminate\Database\Query\Builder');
        $mockQueryBuilder->shouldReceive('insert')->once()->andReturn(true);

        $this->mockDB->shouldReceive('table')->with($tableName)->once()->andReturn($mockQueryBuilder);

        // Mock DatabaseManager to execute with retry
        $this->mockDbManager->shouldReceive('executeWithRetry')
            ->once()
            ->andReturnUsing(function ($callback) {
                return $callback();
            });

        // Execute and check results
        $result = $this->builder->execute();
        
        $this->assertTrue($result['status']);
        $this->assertEquals(0, $result['storeInserts']);
        $this->assertEquals(2, $result['chainInserts']);
        $this->assertEmpty($result['errors']);
    }

    /**
     * Test processing large batches split into multiple inserts
     */
    public function testExecuteWithMultipleBatches()
    {
        $storeID = 1;
        $tableName = 'store_1_orderSummary';
        
        // Set a small batch size to force multiple batches
        $this->builder->setBatchSize(2);
        
        // Mock TableRegistry
        $mockTableRegistry = Mockery::mock('alias:App\\TableRegistry');
        $mockTableRegistry->shouldReceive('getStoreTable')
            ->with($storeID, 'orderSummary')
            ->once()
            ->andReturn($tableName);

        // Prepare test data - 5 summaries should create 3 batches (2, 2, 1)
        $summaries = [
            ['orderID' => 121, 'total' => 100.00],
            ['orderID' => 122, 'total' => 200.00],
            ['orderID' => 123, 'total' => 300.00],
            ['orderID' => 124, 'total' => 400.00],
            ['orderID' => 125, 'total' => 500.00],
        ];
        
        foreach ($summaries as $summary) {
            $this->builder->addStoreOrderSummary($storeID, $summary);
        }

        // Mock the DB facade table chain - should be called 3 times (once per batch)
        $mockQueryBuilder = Mockery::mock('\Illuminate\Database\Query\Builder');
        $mockQueryBuilder->shouldReceive('insert')->times(3)->andReturn(true);

        $this->mockDB->shouldReceive('table')->with($tableName)->times(3)->andReturn($mockQueryBuilder);

        // Mock DatabaseManager to execute with retry - should be called 3 times
        $this->mockDbManager->shouldReceive('executeWithRetry')
            ->times(3)
            ->andReturnUsing(function ($callback) {
                return $callback();
            });

        // Execute and check results
        $result = $this->builder->execute();
        
        $this->assertTrue($result['status']);
        $this->assertEquals(5, $result['storeInserts']);
        $this->assertEquals(0, $result['chainInserts']);
        $this->assertEmpty($result['errors']);
    }

    /**
     * Test error handling when insertion fails
     */
    public function testExecuteHandlesErrors()
    {
        $storeID = 1;
        $tableName = 'store_1_orderSummary';
        
        // Mock TableRegistry
        $mockTableRegistry = Mockery::mock('alias:App\\TableRegistry');
        $mockTableRegistry->shouldReceive('getStoreTable')
            ->with($storeID, 'orderSummary')
            ->once()
            ->andReturn($tableName);

        // Add a test summary
        $this->builder->addStoreOrderSummary($storeID, ['orderID' => 123, 'total' => 100.00]);

        // Mock DatabaseManager to throw an exception
        $this->mockDbManager->shouldReceive('executeWithRetry')
            ->once()
            ->andThrow(new \Exception('Database error'));

        // Execute and check results
        $result = $this->builder->execute();
        
        $this->assertFalse($result['status']);
        $this->assertEquals(0, $result['storeInserts']);
        $this->assertEquals(0, $result['chainInserts']);
        $this->assertCount(1, $result['errors']);
        $this->assertEquals('Database error', $result['errors'][0]);
    }

    /**
     * Test that createOrderSummary works with optimized logic
     */
    public function testCreateOrderSummaryOptimizedLogic()
    {
        $storeID = 1;
        $chainID = 2;
        $dataArray = [
            'orderID' => 'TEST_ORDER_123',
            'currency' => 'INR',
            'timezone' => 'Asia/Kolkata',
            'userName' => 'testuser',
            'deviceID' => 'DEVICE_001',
            'netBill' => 100.00,
            'grossBill' => 118.00,
            'taxes' => 18.00,
            'rounding' => 0.00,
            'orderCreationTimeLocal' => '2023-05-15 14:30:00'
        ];
        $tables = ['orderSummaryTable' => 'store1_orderSummary'];
        $settings = [
            'chain' => ['enableOrderReturnWithoutInvoice' => 0],
            'store' => ['customGSTOrderPrefix' => 'TEST']
        ];

        $result = OrderSummaryBuilder::createOrderSummary($storeID, $chainID, $dataArray, $tables, $settings);

        // Verify basic fields are set
        $this->assertEquals('TEST_ORDER_123', $result['orderID']);
        $this->assertEquals('INR', $result['currencyCode']);
        $this->assertEquals('Asia/Kolkata', $result['timezone']);
        $this->assertEquals('testuser', $result['userName']);
        $this->assertEquals('DEVICE_001', $result['deviceID']);
        $this->assertEquals(100.00, $result['netBill']);
        $this->assertEquals(118.00, $result['grossBill']);

        // Verify datetime fields are processed
        $this->assertNotNull($result['orderLogTimeLocal']);
        $this->assertNotNull($result['orderLogTimeUTC']);
        $this->assertEquals('2023-05-15 14:30:00', $result['orderCreationTimeLocal']);
        $this->assertNotNull($result['orderCreationTimeUTC']);
    }

    /**
     * Test createAndInsertOrderSummary method for single order optimization
     */
    public function testCreateAndInsertOrderSummary()
    {
        $storeID = 1;
        $chainID = 2;
        $dataArray = [
            'orderID' => 'TEST_ORDER_456',
            'currency' => 'INR',
            'timezone' => 'Asia/Kolkata',
            'userName' => 'testuser',
            'deviceID' => 'DEVICE_002',
            'netBill' => 200.00,
            'grossBill' => 236.00,
            'taxes' => 36.00,
            'rounding' => 0.00
        ];
        $tables = [
            'orderSummaryTable' => 'store1_orderSummary',
            'chainOrderSummaryTable' => 'chain2_orderSummary'
        ];
        $settings = [
            'chain' => [],
            'store' => []
        ];

        // Mock DB facade
        $mockDB = Mockery::mock('alias:Illuminate\\Support\\Facades\\DB');
        $mockTable = Mockery::mock();

        $mockDB->shouldReceive('table')
            ->with('store1_orderSummary')
            ->once()
            ->andReturn($mockTable);

        $mockTable->shouldReceive('insert')
            ->once()
            ->andReturn(true);

        $mockDB->shouldReceive('table')
            ->with('chain2_orderSummary')
            ->once()
            ->andReturn($mockTable);

        $mockTable->shouldReceive('insert')
            ->once()
            ->andReturn(true);

        $result = OrderSummaryBuilder::createAndInsertOrderSummary($storeID, $chainID, $dataArray, $tables, $settings);

        $this->assertTrue($result['status']);
        $this->assertEquals(2, $result['queries_executed']);
        $this->assertArrayHasKey('orderSummary', $result);
        $this->assertArrayHasKey('performance_improvement', $result);
        $this->assertEquals('single_order_optimized_preparation', $result['performance_improvement']['optimization_type']);
    }

    /**
     * Test batch processing with multiple order summaries
     */
    public function testBatchProcessingMultipleOrders()
    {
        $storeID = 1;
        $chainID = 2;

        // Create multiple order summaries
        $orderSummaries = [
            ['orderID' => 'BATCH_001', 'total' => 100.00, 'storeID' => $storeID],
            ['orderID' => 'BATCH_002', 'total' => 200.00, 'storeID' => $storeID],
            ['orderID' => 'BATCH_003', 'total' => 300.00, 'storeID' => $storeID],
        ];

        // Mock TableRegistry
        $mockTableRegistry = Mockery::mock('alias:App\\TableRegistry');
        $mockTableRegistry->shouldReceive('getStoreTable')
            ->with($storeID, 'orderSummary')
            ->once()
            ->andReturn('store_1_orderSummary');

        $mockTableRegistry->shouldReceive('getChainTable')
            ->with($chainID, 'orderSummary')
            ->once()
            ->andReturn('chain_2_orderSummary');

        // Mock DB facade
        $mockDB = Mockery::mock('alias:Illuminate\\Support\\Facades\\DB');
        $mockTable = Mockery::mock();

        $mockDB->shouldReceive('table')
            ->with('store_1_orderSummary')
            ->once()
            ->andReturn($mockTable);

        $mockTable->shouldReceive('insert')
            ->with($orderSummaries)
            ->once()
            ->andReturn(true);

        $mockDB->shouldReceive('table')
            ->with('chain_2_orderSummary')
            ->once()
            ->andReturn($mockTable);

        $mockTable->shouldReceive('insert')
            ->with($orderSummaries)
            ->once()
            ->andReturn(true);

        // Add summaries to builder
        foreach ($orderSummaries as $summary) {
            $this->builder->addStoreOrderSummary($storeID, $summary);
            $this->builder->addChainOrderSummary($chainID, $summary);
        }

        // Execute batch
        $result = $this->builder->execute();

        $this->assertTrue($result['status']);
        $this->assertEquals(3, $result['summary']['store_records']);
        $this->assertEquals(3, $result['summary']['chain_records']);
        $this->assertEquals(6, $result['summary']['total_records']);
        $this->assertEquals(2, $result['summary']['queries_executed']); // 1 for store + 1 for chain
    }

    /**
     * Test batch statistics calculation
     */
    public function testBatchStatistics()
    {
        $storeID = 1;
        $chainID = 2;

        // Add some test data
        $this->builder->addStoreOrderSummary($storeID, ['orderID' => 'STAT_001']);
        $this->builder->addStoreOrderSummary($storeID, ['orderID' => 'STAT_002']);
        $this->builder->addChainOrderSummary($chainID, ['orderID' => 'STAT_003']);

        $stats = $this->builder->getBatchStatistics();

        $this->assertEquals(2, $stats['store_summaries']);
        $this->assertEquals(1, $stats['chain_summaries']);
        $this->assertEquals(3, $stats['total_summaries']);
        $this->assertEquals(100, $stats['batch_size']); // Default batch size
        $this->assertEquals(2, $stats['estimated_queries']); // ceil(2/100) + ceil(1/100) = 1 + 1 = 2
    }

    /**
     * Test batch size configuration
     */
    public function testBatchSizeConfiguration()
    {
        // Test setting batch size
        $this->builder->setBatchSize(50);

        $storeID = 1;
        for ($i = 0; $i < 150; $i++) {
            $this->builder->addStoreOrderSummary($storeID, ['orderID' => "BATCH_SIZE_$i"]);
        }

        $stats = $this->builder->getBatchStatistics();

        $this->assertEquals(50, $stats['batch_size']);
        $this->assertEquals(150, $stats['store_summaries']);
        $this->assertEquals(3, $stats['estimated_queries']); // ceil(150/50) = 3
    }

    /**
     * Test error handling in batch processing
     */
    public function testBatchProcessingErrorHandling()
    {
        $storeID = 1;

        // Mock TableRegistry
        $mockTableRegistry = Mockery::mock('alias:App\\TableRegistry');
        $mockTableRegistry->shouldReceive('getStoreTable')
            ->with($storeID, 'orderSummary')
            ->once()
            ->andReturn('store_1_orderSummary');

        // Mock DB facade to throw exception
        $mockDB = Mockery::mock('alias:Illuminate\\Support\\Facades\\DB');
        $mockTable = Mockery::mock();

        $mockDB->shouldReceive('table')
            ->with('store_1_orderSummary')
            ->once()
            ->andReturn($mockTable);

        $mockTable->shouldReceive('insert')
            ->once()
            ->andThrow(new \Exception('Database connection failed'));

        // Add test data
        $this->builder->addStoreOrderSummary($storeID, ['orderID' => 'ERROR_TEST']);

        // Execute and expect error handling
        $result = $this->builder->execute();

        $this->assertFalse($result['status']);
        $this->assertArrayHasKey('error', $result);
        $this->assertEquals('Database connection failed', $result['error']);
    }

    /**
     * Test getting counts of pending store and chain orders
     */
    public function testGetPendingCounts()
    {
        // Mock TableRegistry
        $mockTableRegistry = Mockery::mock('alias:App\\TableRegistry');
        $mockTableRegistry->shouldReceive('getStoreTable')->andReturn('store_table');
        $mockTableRegistry->shouldReceive('getChainTable')->andReturn('chain_table');

        // Add some store summaries
        $this->builder->addStoreOrderSummary(1, ['orderID' => 101]);
        $this->builder->addStoreOrderSummary(1, ['orderID' => 102]);
        $this->builder->addStoreOrderSummary(2, ['orderID' => 201]);
        
        // Add some chain summaries
        $this->builder->addChainOrderSummary(1, ['orderID' => 301]);
        $this->builder->addChainOrderSummary(2, ['orderID' => 401]);
        $this->builder->addChainOrderSummary(2, ['orderID' => 402]);
        $this->builder->addChainOrderSummary(2, ['orderID' => 403]);
        
        // Check counts
        $this->assertEquals(3, $this->builder->getPendingStoreOrdersCount());
        $this->assertEquals(4, $this->builder->getPendingChainOrdersCount());
        
        // Clear and verify counts are reset
        $this->builder->clear();
        $this->assertEquals(0, $this->builder->getPendingStoreOrdersCount());
        $this->assertEquals(0, $this->builder->getPendingChainOrdersCount());
    }
}
