<?php

namespace Tests\Unit;

use App\v3\OrderSummaryBuilder;
use App\v3\Order;
use App\Services\DatabaseManager;
use App\TableRegistry;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;
use Mockery;
use Mo<PERSON>y\MockInterface;

class OrderSummaryBuilderTest extends TestCase
{
    /**
     * @var MockInterface|DatabaseManager
     */
    private $mockDbManager;

    /**
     * @var MockInterface
     */
    private $mockDB;

    /**
     * @var OrderSummaryBuilder
     */
    private $builder;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the DB facade
        $this->mockDB = Mockery::mock('alias:Illuminate\\Support\\Facades\\DB');

        // Mock the DatabaseManager
        $this->mockDbManager = Mockery::mock(DatabaseManager::class);
        
        // Create the builder with our mocked DB manager
        $this->builder = new OrderSummaryBuilder($this->mockDbManager);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test that the batch size can be set and retrieved
     */
    public function testCanSetBatchSize()
    {
        $reflection = new \ReflectionClass($this->builder);
        $property = $reflection->getProperty('batchSize');
        $property->setAccessible(true);

        // Default value should be 1000
        $this->assertEquals(1000, $property->getValue($this->builder));

        // Set a new value
        $this->builder->setBatchSize(500);
        $this->assertEquals(500, $property->getValue($this->builder));
    }

    /**
     * Test adding a store order summary
     */
    public function testAddStoreOrderSummary()
    {
        $storeID = 1;
        $tableName = 'store_1_orderSummary';
        
        // Mock TableRegistry
        $mockTableRegistry = Mockery::mock('alias:App\\TableRegistry');
        $mockTableRegistry->shouldReceive('getStoreTable')
            ->with($storeID, 'orderSummary')
            ->once()
            ->andReturn($tableName);

        $summary = ['orderID' => 123, 'total' => 100.00];
        
        $this->builder->addStoreOrderSummary($storeID, $summary);
        
        $reflection = new \ReflectionClass($this->builder);
        
        $storeTablesProperty = $reflection->getProperty('storeTables');
        $storeTablesProperty->setAccessible(true);
        $this->assertEquals([$storeID => $tableName], $storeTablesProperty->getValue($this->builder));
        
        $storeOrderSummariesProperty = $reflection->getProperty('storeOrderSummaries');
        $storeOrderSummariesProperty->setAccessible(true);
        $this->assertEquals([$storeID => [123 => $summary]], $storeOrderSummariesProperty->getValue($this->builder));
    }

    /**
     * Test adding a chain order summary
     */
    public function testAddChainOrderSummary()
    {
        $chainID = 1;
        $tableName = 'chain_1_orderSummary';
        
        // Mock TableRegistry
        $mockTableRegistry = Mockery::mock('alias:App\\TableRegistry');
        $mockTableRegistry->shouldReceive('getChainTable')
            ->with($chainID, 'orderSummary')
            ->once()
            ->andReturn($tableName);

        $summary = ['orderID' => 123, 'total' => 100.00];
        
        $this->builder->addChainOrderSummary($chainID, $summary);
        
        $reflection = new \ReflectionClass($this->builder);
        
        $chainTablesProperty = $reflection->getProperty('chainTables');
        $chainTablesProperty->setAccessible(true);
        $this->assertEquals([$chainID => $tableName], $chainTablesProperty->getValue($this->builder));
        
        $chainOrderSummariesProperty = $reflection->getProperty('chainOrderSummaries');
        $chainOrderSummariesProperty->setAccessible(true);
        $this->assertEquals([$chainID => [123 => $summary]], $chainOrderSummariesProperty->getValue($this->builder));
    }

    /**
     * Test executing batch inserts for store order summaries
     */
    public function testExecuteForStoreOrderSummaries()
    {
        $storeID = 1;
        $tableName = 'store_1_orderSummary';
        
        // Mock TableRegistry
        $mockTableRegistry = Mockery::mock('alias:App\\TableRegistry');
        $mockTableRegistry->shouldReceive('getStoreTable')
            ->with($storeID, 'orderSummary')
            ->once()
            ->andReturn($tableName);

        // Prepare test data
        $summaries = [
            ['orderID' => 123, 'total' => 100.00],
            ['orderID' => 124, 'total' => 200.00],
        ];
        
        foreach ($summaries as $summary) {
            $this->builder->addStoreOrderSummary($storeID, $summary);
        }

        // Mock the DB facade table chain
        $mockQueryBuilder = Mockery::mock('\Illuminate\Database\Query\Builder');
        $mockQueryBuilder->shouldReceive('insert')->once()->andReturn(true);

        $this->mockDB->shouldReceive('table')->with($tableName)->once()->andReturn($mockQueryBuilder);

        // Mock DatabaseManager to execute with retry
        $this->mockDbManager->shouldReceive('executeWithRetry')
            ->once()
            ->andReturnUsing(function ($callback) {
                return $callback();
            });

        // Execute and check results
        $result = $this->builder->execute();
        
        $this->assertTrue($result['status']);
        $this->assertEquals(2, $result['storeInserts']);
        $this->assertEquals(0, $result['chainInserts']);
        $this->assertEmpty($result['errors']);
    }

    /**
     * Test executing batch inserts for chain order summaries
     */
    public function testExecuteForChainOrderSummaries()
    {
        $chainID = 1;
        $tableName = 'chain_1_orderSummary';
        
        // Mock TableRegistry
        $mockTableRegistry = Mockery::mock('alias:App\\TableRegistry');
        $mockTableRegistry->shouldReceive('getChainTable')
            ->with($chainID, 'orderSummary')
            ->once()
            ->andReturn($tableName);

        // Prepare test data
        $summaries = [
            ['orderID' => 123, 'total' => 100.00],
            ['orderID' => 124, 'total' => 200.00],
        ];
        
        foreach ($summaries as $summary) {
            $this->builder->addChainOrderSummary($chainID, $summary);
        }

        // Mock the DB facade table chain
        $mockQueryBuilder = Mockery::mock('\Illuminate\Database\Query\Builder');
        $mockQueryBuilder->shouldReceive('insert')->once()->andReturn(true);

        $this->mockDB->shouldReceive('table')->with($tableName)->once()->andReturn($mockQueryBuilder);

        // Mock DatabaseManager to execute with retry
        $this->mockDbManager->shouldReceive('executeWithRetry')
            ->once()
            ->andReturnUsing(function ($callback) {
                return $callback();
            });

        // Execute and check results
        $result = $this->builder->execute();
        
        $this->assertTrue($result['status']);
        $this->assertEquals(0, $result['storeInserts']);
        $this->assertEquals(2, $result['chainInserts']);
        $this->assertEmpty($result['errors']);
    }

    /**
     * Test processing large batches split into multiple inserts
     */
    public function testExecuteWithMultipleBatches()
    {
        $storeID = 1;
        $tableName = 'store_1_orderSummary';
        
        // Set a small batch size to force multiple batches
        $this->builder->setBatchSize(2);
        
        // Mock TableRegistry
        $mockTableRegistry = Mockery::mock('alias:App\\TableRegistry');
        $mockTableRegistry->shouldReceive('getStoreTable')
            ->with($storeID, 'orderSummary')
            ->once()
            ->andReturn($tableName);

        // Prepare test data - 5 summaries should create 3 batches (2, 2, 1)
        $summaries = [
            ['orderID' => 121, 'total' => 100.00],
            ['orderID' => 122, 'total' => 200.00],
            ['orderID' => 123, 'total' => 300.00],
            ['orderID' => 124, 'total' => 400.00],
            ['orderID' => 125, 'total' => 500.00],
        ];
        
        foreach ($summaries as $summary) {
            $this->builder->addStoreOrderSummary($storeID, $summary);
        }

        // Mock the DB facade table chain - should be called 3 times (once per batch)
        $mockQueryBuilder = Mockery::mock('\Illuminate\Database\Query\Builder');
        $mockQueryBuilder->shouldReceive('insert')->times(3)->andReturn(true);

        $this->mockDB->shouldReceive('table')->with($tableName)->times(3)->andReturn($mockQueryBuilder);

        // Mock DatabaseManager to execute with retry - should be called 3 times
        $this->mockDbManager->shouldReceive('executeWithRetry')
            ->times(3)
            ->andReturnUsing(function ($callback) {
                return $callback();
            });

        // Execute and check results
        $result = $this->builder->execute();
        
        $this->assertTrue($result['status']);
        $this->assertEquals(5, $result['storeInserts']);
        $this->assertEquals(0, $result['chainInserts']);
        $this->assertEmpty($result['errors']);
    }

    /**
     * Test error handling when insertion fails
     */
    public function testExecuteHandlesErrors()
    {
        $storeID = 1;
        $tableName = 'store_1_orderSummary';
        
        // Mock TableRegistry
        $mockTableRegistry = Mockery::mock('alias:App\\TableRegistry');
        $mockTableRegistry->shouldReceive('getStoreTable')
            ->with($storeID, 'orderSummary')
            ->once()
            ->andReturn($tableName);

        // Add a test summary
        $this->builder->addStoreOrderSummary($storeID, ['orderID' => 123, 'total' => 100.00]);

        // Mock DatabaseManager to throw an exception
        $this->mockDbManager->shouldReceive('executeWithRetry')
            ->once()
            ->andThrow(new \Exception('Database error'));

        // Execute and check results
        $result = $this->builder->execute();
        
        $this->assertFalse($result['status']);
        $this->assertEquals(0, $result['storeInserts']);
        $this->assertEquals(0, $result['chainInserts']);
        $this->assertCount(1, $result['errors']);
        $this->assertEquals('Database error', $result['errors'][0]);
    }

    /**
     * Test that createOrderSummary delegates to Order::createOrderSummary
     */
    public function testCreateOrderSummaryDelegatesToOrderClass()
    {
        $storeID = 1;
        $chainID = 2;
        $dataArray = ['orderID' => 123];
        $tables = ['table1' => 'value1'];
        $settings = ['setting1' => 'value1'];
        $expectedSummary = ['orderID' => 123, 'processed' => true];
        
        // Mock the Order class
        $mockOrder = Mockery::mock('alias:App\\v3\\Order');
        $mockOrder->shouldReceive('createOrderSummary')
            ->with($storeID, $chainID, $dataArray, $tables, $settings)
            ->once()
            ->andReturn($expectedSummary);
        
        $result = OrderSummaryBuilder::createOrderSummary($storeID, $chainID, $dataArray, $tables, $settings);
        
        $this->assertEquals($expectedSummary, $result);
    }

    /**
     * Test getting counts of pending store and chain orders
     */
    public function testGetPendingCounts()
    {
        // Mock TableRegistry
        $mockTableRegistry = Mockery::mock('alias:App\\TableRegistry');
        $mockTableRegistry->shouldReceive('getStoreTable')->andReturn('store_table');
        $mockTableRegistry->shouldReceive('getChainTable')->andReturn('chain_table');

        // Add some store summaries
        $this->builder->addStoreOrderSummary(1, ['orderID' => 101]);
        $this->builder->addStoreOrderSummary(1, ['orderID' => 102]);
        $this->builder->addStoreOrderSummary(2, ['orderID' => 201]);
        
        // Add some chain summaries
        $this->builder->addChainOrderSummary(1, ['orderID' => 301]);
        $this->builder->addChainOrderSummary(2, ['orderID' => 401]);
        $this->builder->addChainOrderSummary(2, ['orderID' => 402]);
        $this->builder->addChainOrderSummary(2, ['orderID' => 403]);
        
        // Check counts
        $this->assertEquals(3, $this->builder->getPendingStoreOrdersCount());
        $this->assertEquals(4, $this->builder->getPendingChainOrdersCount());
        
        // Clear and verify counts are reset
        $this->builder->clear();
        $this->assertEquals(0, $this->builder->getPendingStoreOrdersCount());
        $this->assertEquals(0, $this->builder->getPendingChainOrdersCount());
    }
}
