<?php

namespace Tests\Unit;

use App\v3\OrderSummaryBuilder;
use Tests\TestCase;
use Mockery;

class OrderSummaryBuilderTest extends TestCase
{
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test that createOrderSummary works with optimized logic
     */
    public function testCreateOrderSummaryOptimizedLogic()
    {
        $storeID = 1;
        $chainID = 2;
        $dataArray = [
            'orderID' => 'TEST_ORDER_123',
            'currency' => 'INR',
            'timezone' => 'Asia/Kolkata',
            'userName' => 'testuser',
            'deviceID' => 'DEVICE_001',
            'netBill' => 100.00,
            'grossBill' => 118.00,
            'taxes' => 18.00,
            'rounding' => 0.00,
            'orderCreationTimeLocal' => '2023-05-15 14:30:00'
        ];
        $tables = ['orderSummaryTable' => 'store1_orderSummary'];
        $settings = [
            'chain' => ['enableOrderReturnWithoutInvoice' => 0],
            'store' => ['customGSTOrderPrefix' => 'TEST']
        ];

        $result = OrderSummaryBuilder::createOrderSummary($storeID, $chainID, $dataArray, $tables, $settings);

        // Verify basic fields are set
        $this->assertEquals('TEST_ORDER_123', $result['orderID']);
        $this->assertEquals('INR', $result['currencyCode']);
        $this->assertEquals('Asia/Kolkata', $result['timezone']);
        $this->assertEquals('testuser', $result['userName']);
        $this->assertEquals('DEVICE_001', $result['deviceID']);
        $this->assertEquals(100.00, $result['netBill']);
        $this->assertEquals(118.00, $result['grossBill']);

        // Verify datetime fields are processed
        $this->assertNotNull($result['orderLogTimeLocal']);
        $this->assertNotNull($result['orderLogTimeUTC']);
        $this->assertEquals('2023-05-15 14:30:00', $result['orderCreationTimeLocal']);
        $this->assertNotNull($result['orderCreationTimeUTC']);
    }

    /**
     * Test optimized createOrderSummary method performance with settings caching
     */
    public function testOptimizedCreateOrderSummaryPerformance()
    {
        $storeID = 1;
        $chainID = 2;
        $dataArray = [
            'orderID' => 'PERF_TEST_456',
            'currency' => 'INR',
            'timezone' => 'Asia/Kolkata',
            'userName' => 'testuser',
            'deviceID' => 'DEVICE_002',
            'netBill' => 200.00,
            'grossBill' => 236.00,
            'taxes' => 36.00,
            'rounding' => 0.00,
            'finalCustomerID' => 123
        ];
        $tables = [
            'orderSummaryTable' => 'store1_orderSummary',
            'chainOrderSummaryTable' => 'chain2_orderSummary'
        ];
        $settings = [
            'chain' => [
                'enableOrderReturnWithoutInvoice' => 1,
                'enableSourceTax' => 0,
                'enableSalesOrder' => 1,
                'purchaseAmount' => 100,
                'minimumOrderValueToEarnPoints' => 50
            ],
            'store' => [
                'customGSTOrderPrefix' => 'OPT',
                'enableMunicipalIntegration' => 0
            ]
        ];

        // Test that settings are cached and reused
        $result1 = OrderSummaryBuilder::createOrderSummary($storeID, $chainID, $dataArray, $tables, $settings);
        $result2 = OrderSummaryBuilder::createOrderSummary($storeID, $chainID, $dataArray, $tables, $settings);

        // Verify basic functionality
        $this->assertEquals('PERF_TEST_456', $result1['orderID']);
        $this->assertEquals('PERF_TEST_456', $result2['orderID']);
        $this->assertEquals('INR', $result1['currencyCode']);
        $this->assertEquals('INR', $result2['currencyCode']);

        // Verify that both calls produce identical results (settings caching works)
        $this->assertEquals($result1, $result2);
    }

    /**
     * Test settings caching optimization
     */
    public function testSettingsCachingOptimization()
    {
        $storeID = 1;
        $chainID = 2;
        $dataArray = [
            'orderID' => 'CACHE_TEST_001',
            'currency' => 'INR',
            'timezone' => 'Asia/Kolkata',
            'userName' => 'testuser',
            'netBill' => 100.00,
            'grossBill' => 118.00
        ];
        $tables = ['orderSummaryTable' => 'store1_orderSummary'];
        $settings = [
            'chain' => [
                'enableOrderReturnWithoutInvoice' => 1,
                'enableSourceTax' => 1,
                'enableSalesOrder' => 0,
                'purchaseAmount' => 100,
                'minimumOrderValueToEarnPoints' => 50
            ],
            'store' => [
                'customGSTOrderPrefix' => 'CACHE',
                'enableMunicipalIntegration' => 1,
                'municipalType' => 'PUNJAB'
            ]
        ];

        // Call multiple times to test caching
        $result1 = OrderSummaryBuilder::createOrderSummary($storeID, $chainID, $dataArray, $tables, $settings);
        $result2 = OrderSummaryBuilder::createOrderSummary($storeID, $chainID, $dataArray, $tables, $settings);
        $result3 = OrderSummaryBuilder::createOrderSummary($storeID, $chainID, $dataArray, $tables, $settings);

        // All results should be identical (proving caching works)
        $this->assertEquals($result1, $result2);
        $this->assertEquals($result2, $result3);

        // Verify specific optimized fields
        $this->assertEquals('CACHE_TEST_001', $result1['orderID']);
        $this->assertEquals('INR', $result1['currencyCode']);
        $this->assertEquals(1, $result1['isNoInvoiceSale']); // enableOrderReturnWithoutInvoice = 1
    }
}
