# Task 2.12 Prompt – Optimise Order Summary

**Phase:** 2 – Code Refactor & DB Optimisation  
**Task ID:** 2.12  
**Assigned Agent Role:** Implementation Agent  

## Objective
Implement optimisations for order summary generation.

## Context & Inputs
- Order summary schema in `cloud_schema/chains.sql`.
- Builder function in `OrderDetailBuilder.php`
- Builder function in `OrderAccountingService.php`
- Current implementation in `v3/Order.php`

## Deliverables
1. `OrderSummaryBuilder.php` with batch insertion logic.
2. Unit tests for order summary batches.
3. Pull request with diff and description.
4. Append summary to `Task_2.12_Log.md`.

## Acceptance Criteria
- Order summary inserted in a single batch.
- ≥10 query reduction per order.
- Tests cover multiple order summary scenarios.

## Timebox
6 work hours
