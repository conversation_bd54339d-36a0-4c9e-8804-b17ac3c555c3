<?php

namespace App\Services;

use App\TableRegistry;
use DB;
use Illuminate\Support\Facades\Log;
use Cache;

/**
 * SettingsManager - Centralized management of chain and store settings with caching
 * 
 * This class provides methods to get chain and store settings with caching
 * to reduce repeated database queries.
 */
class SettingsManager
{
    /**
     * Cache TTL in seconds (1 hour)
     */
    const CACHE_TTL = 3600;
    
    /**
     * Database manager instance
     */
    private $dbManager;
    
    /**
     * Constructor
     */
    public function __construct(DatabaseManager $dbManager = null)
    {
        $this->dbManager = $dbManager ?? new DatabaseManager();
    }
    
    /**
     * Get chain settings
     * 
     * @param int $chainID The chain ID
     * @param array $columns The columns to retrieve (null for all)
     * @param bool $useCache Whether to use cache
     * @return array The chain settings
     */
    public function getChainSettings($chainID, $columns = null, $useCache = true)
    {
        $cacheKey = "chain_settings_{$chainID}_" . ($columns ? md5(json_encode($columns)) : 'all');
        
        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }
        
        try {
            $chainSettingsTable = TableRegistry::getGlobalTable('chainSettings');
            $listOfChainsTable = TableRegistry::getGlobalTable('listOfChains');
            $chainModuleMapTable = TableRegistry::getGlobalTable('chainModuleMap');
            $crmSettingsTable = TableRegistry::getGlobalTable('crmSettings');

            // Build the select clause
            $selectClause = $columns ? implode(', ', array_map(function($col) {
                return strpos($col, '.') === false ? $col : $col;
            }, $columns)) : '*';

            // Construct the raw SQL query
            $sql = "SELECT {$selectClause} FROM {$listOfChainsTable} LOC
                    LEFT JOIN {$chainSettingsTable} CS ON LOC.chainID = CS.chainRefID 
                    LEFT JOIN {$chainModuleMapTable} CMM ON LOC.chainID = CMM.chainID
                    LEFT JOIN {$crmSettingsTable} CRM ON LOC.chainID = CRM.chainID AND CRM.isActive = 1
                    WHERE LOC.chainID = ?";

            // Use DatabaseManager's retry logic with raw query
            $settings = $this->dbManager->executeWithRetry(function() use ($sql, $chainID) {
                return DB::selectOne($sql, [$chainID]);
            });

            $result = $settings ? (array)$settings : [];

            if ($useCache) {
                Cache::put($cacheKey, $result, self::CACHE_TTL);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error("Error getting chain settings: " . $e->getMessage(), [
                'chainID' => $chainID,
                'columns' => $columns,
                'trace' => $e->getTraceAsString(),
            ]);
            
            return [];
        }
    }
    
    /**
     * Get store settings
     * 
     * @param int $storeID The store ID
     * @param array $columns The columns to retrieve (null for all)
     * @param bool $useCache Whether to use cache
     * @return array The store settings
     */
    public function getStoreSettings($storeID, $columns = null, $useCache = true)
    {
        $cacheKey = "store_settings_{$storeID}_" . ($columns ? md5(json_encode($columns)) : 'all');
        
        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }
        
        try {
            $listOfStoresTable = TableRegistry::getGlobalTable('listOfStores');
            
            // Build the select clause
            $selectClause = $columns ? implode(', ', array_map(function($col) {
                return strpos($col, '.') === false ? $col : $col;
            }, $columns)) : '*';
            
            // Construct the raw SQL query
            $sql = "SELECT {$selectClause} FROM {$listOfStoresTable} WHERE storeID = ?";
            
            // Use DatabaseManager's retry logic with raw query
            $settings = $this->dbManager->executeWithRetry(function() use ($sql, $storeID) {
                return DB::selectOne($sql, [$storeID]);
            });
            
            $result = $settings ? (array)$settings : [];
            
            if ($useCache) {
                Cache::put($cacheKey, $result, self::CACHE_TTL);
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error("Error getting store settings: " . $e->getMessage(), [
                'storeID' => $storeID,
                'columns' => $columns,
                'trace' => $e->getTraceAsString(),
            ]);
            
            return [];
        }
    }
    
    /**
     * Get chain and store settings in a single call
     * 
     * @param int $chainID The chain ID
     * @param int $storeID The store ID
     * @param array $chainColumns The chain columns to retrieve (null for all)
     * @param array $storeColumns The store columns to retrieve (null for all)
     * @param bool $useCache Whether to use cache
     * @return array The combined settings
     */
    public function getChainAndStoreSettings($chainID, $storeID, $chainColumns = null, $storeColumns = null, $useCache = true)
    {
        $cacheKey = "chain_store_settings_{$chainID}_{$storeID}_" . 
                   ($chainColumns ? md5(json_encode($chainColumns)) : 'all') . '_' .
                   ($storeColumns ? md5(json_encode($storeColumns)) : 'all');
        
        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }
        
        $chainSettings = $this->getChainSettings($chainID, $chainColumns, $useCache);
        $storeSettings = $this->getStoreSettings($storeID, $storeColumns, $useCache);
        
        $result = [
            'chain' => $chainSettings,
            'store' => $storeSettings
        ];
        
        if ($useCache) {
            Cache::put($cacheKey, $result, self::CACHE_TTL);
        }
        
        return $result;
    }
    
    /**
     * Clear settings cache for a chain
     * 
     * @param int $chainID The chain ID
     * @return void
     */
    public function clearChainSettingsCache($chainID)
    {
        Cache::forget("chain_settings_{$chainID}_all");
    }
    
    /**
     * Clear settings cache for a store
     * 
     * @param int $storeID The store ID
     * @return void
     */
    public function clearStoreSettingsCache($storeID)
    {
        Cache::forget("store_settings_{$storeID}_all");
    }
    
    /**
     * Clear all settings cache
     * 
     * @return void
     */
    public function clearAllSettingsCache()
    {
        // This is a simplified approach - in a real implementation,
        // you might want to use cache tags or a more targeted approach
        Cache::flush();
    }
}
