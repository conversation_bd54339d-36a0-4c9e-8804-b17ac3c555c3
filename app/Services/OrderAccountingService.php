<?php

namespace App\Services;

use App\Services\BulkInsertService;
use App\Services\DatabaseManager;
use App\Account;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderAccountingService
{
    private $dbManager;
    private $bulkInsertService;

    public function __construct(DatabaseManager $dbManager = null)
    {
        $this->dbManager = $dbManager ?: new DatabaseManager();
        $this->bulkInsertService = new BulkInsertService($this->dbManager);
    }

    // @deprecated
    // Split into generate and insert.
    /**
     * Generate and insert order accounting records using bulk operations
     * Combines Account::orderInsert logic with bulk insertion for optimal performance
     *
     * @param int $storeID Store ID for table naming
     * @param array $orderArray Order data array
     * @param array $tables Array containing table names
     * @param int $globalChainAccounting Flag indicating if chain accounting is enabled
     * @param array $getChainSettings Chain settings array
     * @param array $additionalData Additional data for processing (deviceID, batchID, etc.)
     * @return array Results of the bulk insertion operation
     */
    public function generateAndInsertOrderAccounting($storeID, $orderArray, $tables, $globalChainAccounting, $getChainSettings, $additionalData = [])
    {
        try {
            // Generate accounting records using optimized logic
            $accountingArray = $this->generateOrderAccountingRecords($storeID, $orderArray, $getChainSettings);

            // Normalize and add additional fields required by Order.php
            $accountingArray = $this->normalizeAndAddProcessingFields($accountingArray, $additionalData);

            // Perform bulk insertion
            return $this->insertOrderAccountingBulk($accountingArray, $storeID, $tables, $globalChainAccounting, $additionalData, $getChainSettings);

        } catch (\Exception $e) {
            Log::error("Order accounting generation and insertion failed: " . $e->getMessage(), [
                'orderID' => $orderArray['orderID'] ?? 'unknown',
                'storeID' => $storeID,
                'error' => $e->getMessage()
            ]);

            throw new \Exception("Order accounting processing failed: " . $e->getMessage());
        }
    }

    /**
     * Generate order accounting records
     * Optimises Account::orderInsert logic with bulk insertion for optimal performance
     *
     * @param int $storeID Store ID for table naming
     * @param array $orderArray Order data array
     * @param array $tables Array containing table names
     * @param int $globalChainAccounting Flag indicating if chain accounting is enabled
     * @param array $getChainSettings Chain settings array
     * @param array $additionalData Additional data for processing (deviceID, batchID, etc.)
     * @return array Results of the bulk insertion operation
     */
    public function generateOrderAccounting($storeID, $orderArray, $tables, $globalChainAccounting, $getChainSettings, $additionalData = [])
    {
        try {
            // Generate accounting records using optimized logic
            $accountingArray = $this->generateOrderAccountingRecords($storeID, $orderArray, $getChainSettings);

            // Normalize and add additional fields required by Order.php
            $accountingArray = $this->normalizeAndAddProcessingFields($accountingArray, $additionalData);

            // Return generated accounting records
            return $accountingArray;

        } catch (\Exception $e) {
            Log::error("Order accounting generation and insertion failed: " . $e->getMessage(), [
                'orderID' => $orderArray['orderID'] ?? 'unknown',
                'storeID' => $storeID,
                'error' => $e->getMessage()
            ]);

            throw new \Exception("Order accounting processing failed: " . $e->getMessage());
        }
    }

    /**
     * Generate order accounting records (optimized version of Account::orderInsert)
     *
     * @param int $storeID Store ID
     * @param array $orderArray Order data
     * @param array $getChainSettings Chain settings
     * @return array Generated accounting records
     */
    private function generateOrderAccountingRecords($storeID, $orderArray, $getChainSettings)
    {
        $accountingArray = array();
        $timezone = $orderArray["timezone"];

        // Set timezone and generate timestamps
        date_default_timezone_set('UTC');
        $accountLogTimeUTC = date("Y-m-d H:i:s");
        date_default_timezone_set($timezone);
        $accountLogTimeLocal = date('Y-m-d H:i:s');

        $enableSourceTax = $getChainSettings[0]['enableSourceTax'] ?? 0;
        $enableInventoryAssetManagement = $getChainSettings[0]['enableInventoryAssetManagement'] ?? 0;
        $accountCount = 0;

        // Generate payments accounting
        $temp = Account::createPayments($storeID, $orderArray, "Order Placed", $getChainSettings);
        $accountingArray = $this->mergeAccountingRecords($accountingArray, $temp, $accountCount);

        // Generate item sales accounting
        $temp = Account::createItemSales($storeID, $orderArray);
        $accountingArray = $this->mergeAccountingRecords($accountingArray, $temp, $accountCount);

        // Generate taxes accounting
        $temp = Account::createTaxes($storeID, $orderArray);
        $accountingArray = $this->mergeAccountingRecords($accountingArray, $temp, $accountCount);

        // Generate discounts accounting
        $temp = Account::createDiscounts($storeID, $orderArray);
        $accountingArray = $this->mergeAccountingRecords($accountingArray, $temp, $accountCount);

        // Generate additional charges accounting
        $temp = Account::createAdditionalCharges($storeID, $orderArray);
        $accountingArray = $this->mergeAccountingRecords($accountingArray, $temp, $accountCount);

        // Generate rounding off accounting
        $temp = Account::createRoundingOff($storeID, $orderArray);
        $accountingArray = $this->mergeAccountingRecords($accountingArray, $temp, $accountCount);

        // Generate open bill item sales accounting
        $temp = Account::createOpenBillItemSales($storeID, $orderArray);
        $accountingArray = $this->mergeAccountingRecords($accountingArray, $temp, $accountCount);

        // Generate open bill taxes accounting
        $temp = Account::createOpenBillTaxes($storeID, $orderArray);
        $accountingArray = $this->mergeAccountingRecords($accountingArray, $temp, $accountCount);

        // Generate TDS accounting if enabled
        if ($enableSourceTax == 1 && isset($orderArray['TDSValue']) && $orderArray['TDSValue'] > 0) {
            $temp = Account::createTDS($storeID, $orderArray);
            $accountingArray = $this->mergeAccountingRecords($accountingArray, $temp, $accountCount);
        }

        // Generate TCS accounting if enabled
        if ($enableSourceTax == 1 && isset($orderArray['TCSValue']) && $orderArray['TCSValue'] > 0) {
            $temp = Account::createTCS($storeID, $orderArray);
            $accountingArray = $this->mergeAccountingRecords($accountingArray, $temp, $accountCount);
        }

        // Generate security deposit accounting
        if ($enableInventoryAssetManagement == 1) {
            $temp = Account::createSecurityDeposit($storeID, $orderArray);
            $accountingArray = $this->mergeAccountingRecords($accountingArray, $temp, $accountCount);
        }

        // Add common fields to all records
        $totalRecords = count($accountingArray);
        for ($i = 0; $i < $totalRecords; $i++) {
            $accountingArray[$i]["accountLogTimeLocal"] = $accountLogTimeLocal;
            $accountingArray[$i]["accountLogTimeUTC"] = $accountLogTimeUTC;
            $accountingArray[$i]["transactionUser"] = $orderArray['billingUsername'];
        }

        return $accountingArray;
    }

    /**
     * Merge accounting records efficiently
     *
     * @param array $accountingArray Main accounting array
     * @param array $temp Temporary records to merge
     * @param int &$accountCount Reference to account count
     * @return array Merged accounting array
     */
    private function mergeAccountingRecords($accountingArray, $temp, &$accountCount)
    {
        $tempCount = count($temp);
        for ($i = 0; $i < $tempCount; $i++) {
            $accountingArray[$accountCount] = $temp[$i];
            $accountCount++;
        }
        return $accountingArray;
    }

    /**
     * Normalize accounting records and add additional processing fields
     * Combines normalization and additional field processing in a single iteration
     * This fixes inconsistencies between different Account.php methods and adds required fields
     *
     * @param array $accountingArray Accounting records to normalize
     * @param array $additionalData Additional data (deviceID, batchID, etc.)
     * @return array Normalized accounting records with additional fields
     */
    private function normalizeAndAddProcessingFields($accountingArray, $additionalData = [])
    {
        // Define all possible fields that should be present in accounting records
        $standardFields = [
            'accountDeviceID' => 0,
            'orderID' => null,
            'orderSubID' => -1,
            'timezone' => 'Asia/Kolkata',
            'accountType' => null,
            'posDate' => null,
            'accountID' => null,
            'accountName' => null,
            'debit' => 0.0000,
            'credit' => 0.0000,
            'exchange' => 0.0000,
            'transactionTypeID' => null,
            'transactionTimeLocal' => null,
            'transactionTimeUTC' => null,
            'transactionUser' => null,
            'accountLogTimeLocal' => null,
            'accountLogTimeUTC' => null,
            'transactionQuantity' => 0.0000,
            'transactionPaymentType' => null,
            'transactionCardType' => null,
            'batchID' => null,
            'refundedOrderID' => null,
            'isActive' => 1,
            'dummy1' => null,
            'dummy2' => null,
            'dummy3' => null,
            'dummy4' => null,
            'dummy5' => null,
            'dummy6' => null
        ];

        // Normalize each record and add additional fields in single iteration
        $rowColsCount = [];
        $countOf = count($accountingArray);

        for ($i = 0; $i < $countOf; $i++) {
            // First, set default values for missing fields
            foreach ($standardFields as $field => $defaultValue) {
                if (!isset($accountingArray[$i][$field])) {
                    $accountingArray[$i][$field] = $defaultValue;
                }
            }

            // Then, override with additional data if provided
            if (isset($additionalData['deviceID'])) {
                $accountingArray[$i]["accountDeviceID"] = $additionalData['deviceID'];
            }

            if (isset($additionalData['batchID']) && !empty($additionalData['batchID'])) {
                $accountingArray[$i]["batchID"] = $additionalData['batchID'];
            }

            $rowColsCount[] = count($accountingArray[$i]);
        }

        // Validate consistent column count
        $uniqueCounts = array_unique($rowColsCount);
        if (count($uniqueCounts) > 1) {
            Log::error("Error in normalizing accounting records. Inconsistent column count found.", [
                'unique_counts' => $uniqueCounts,
                'row_counts' => $rowColsCount,
                'accounting_array' => $accountingArray
            ]);
            throw new \Exception("Inconsistent column count in accounting records: " . implode(', ', $uniqueCounts));
        }

        return $accountingArray;
    }

    /**
     * Process and insert order accounting records using bulk operations
     *
     * @param array $orderAccounting Array of accounting records to insert
     * @param int $storeID Store ID for table naming
     * @param array $tables Array containing table names
     * @param int $globalChainAccounting Flag indicating if chain accounting is enabled
     * @param array $dataArray Original order data for additional fields
     * @param array $getChainSettings Chain settings array
     * @return array Results of the bulk insertion operation
     */
    public function insertOrderAccountingBulk($orderAccounting, $storeID, $tables, $globalChainAccounting, $dataArray = [], $getChainSettings = [])
    {
        $orderAccountingTable = $tables['orderAccountingTable'];
        $chainOrderAccountingTable = $tables['chainOrderAccountingTable'] ?? null;

        // Validate input parameters
        if (empty($orderAccounting)) {
            return [
                'status' => true,
                'message' => 'No accounting records to insert',
                'results' => [],
                'summary' => [
                    'store_accounting' => 0,
                    'chain_accounting' => 0,
                    'total_records' => 0
                ]
            ];
        }

        if (empty($orderAccountingTable)) {
            throw new \Exception("Store accounting table name is required");
        }

        if ($globalChainAccounting == 1 && empty($chainOrderAccountingTable)) {
            throw new \Exception("Chain accounting table name is required when chain accounting is enabled");
        }

        try {
            // Prepare data for bulk insertion
            $data = $this->prepareAccountingData(
                $orderAccounting,
                $storeID,
                $globalChainAccounting,
                $dataArray,
                $getChainSettings
            );

            $storeAccountingData = $data['storeData'];
            $chainAccountingData = $data['chainData'];

            return $this->dbManager->transaction(function() use (
                $storeAccountingData,
                $chainAccountingData,
                $orderAccountingTable,
                $chainOrderAccountingTable,
                $globalChainAccounting,
                $dataArray,
                $storeID
            ) {
                $results = [];

                // Use savepoint for accounting insertion to allow partial rollback
                DB::statement('SAVEPOINT order_accounting_insert');

                try {
                    // Insert store level accounting records
                    $results['accounting']['store'] = $this->bulkInsertService->bulkInsert(
                        $orderAccountingTable,
                        $storeAccountingData,
                        500 // Use smaller batch size for better error handling
                    );

                    if (!$results['accounting']['store']['status']) {
                        DB::statement('ROLLBACK TO SAVEPOINT order_accounting_insert');
                        throw new \Exception("Store accounting insertion failed: " . $results['accounting']['store']['error']);
                    }

                } catch (\Exception $e) {
                    DB::statement('ROLLBACK TO SAVEPOINT order_accounting_insert');
                    Log::error("Exception during store accounting insertion: " . $e->getMessage());
                    throw new \Exception("Store accounting insertion exception: " . $e->getMessage());
                }

                // Insert chain level accounting records if enabled
                if ($globalChainAccounting == 1 && !empty($chainAccountingData) && $chainOrderAccountingTable) {
                    try {
                        $results['accounting']['chain'] = $this->bulkInsertService->bulkInsert(
                            $chainOrderAccountingTable,
                            $chainAccountingData,
                            500 // Use smaller batch size for better error handling
                        );

                        if (!$results['accounting']['chain']['status']) {
                            DB::statement('ROLLBACK TO SAVEPOINT order_accounting_insert');
                            throw new \Exception("Chain accounting insertion failed: " . $results['accounting']['chain']['error']);
                        }

                    } catch (\Exception $e) {
                        DB::statement('ROLLBACK TO SAVEPOINT order_accounting_insert');
                        Log::error("Exception during chain accounting insertion: " . $e->getMessage());
                        throw new \Exception("Chain accounting insertion exception: " . $e->getMessage());
                    }
                } else {
                    // Chain accounting not enabled or no data
                    $results['accounting']['chain'] = [
                        'status' => true,
                        'inserted' => 0,
                        'message' => 'Chain accounting not enabled or no data to insert'
                    ];
                }

                DB::statement('RELEASE SAVEPOINT order_accounting_insert');

                // Prepare success response
                return [
                    'status' => true,
                    'message' => 'Order accounting records inserted successfully',
                    'results' => $results,
                    'summary' => [
                        'store_accounting' => $results['accounting']['store']['inserted'] ?? 0,
                        'chain_accounting' => $results['accounting']['chain']['inserted'] ?? 0,
                        'total_records' => ($results['accounting']['store']['inserted'] ?? 0) + ($results['accounting']['chain']['inserted'] ?? 0)
                    ]
                ];
            });
        } catch (\Exception $e) {
            Log::error("Order accounting bulk insertion failed: " . $e->getMessage(), [
                'orderID' => $dataArray['orderID'] ?? 'unknown',
                'storeID' => $storeID,
                'error' => $e->getMessage()
            ]);

            throw new \Exception("Bulk accounting insertion failed: " . $e->getMessage());
        }
    }

    /**
     * Prepare accounting data for bulk insertion
     *
     * @param array $orderAccounting Original accounting records
     * @param int $storeID Store ID
     * @param int $globalChainAccounting Flag for chain accounting
     * @param array $dataArray Original order data
     * @param array $getChainSettings Chain settings array
     * @return array Prepared data for store and chain tables
     */
    private function prepareAccountingData($orderAccounting, $storeID, $globalChainAccounting, $dataArray, $getChainSettings)
    {
        $storeData = [];
        $chainData = [];

        $enableSalesOrder = $getChainSettings[0]['enableSalesOrder'] ?? 0;
        $enableChannelManagement = $getChainSettings[0]['enableChannelManagement'] ?? 0;
        $enableCounterManagement = $getChainSettings[0]['enableCounterManagement'] ?? 0;

        foreach ($orderAccounting as $index => $record) {
            // Prepare store-level accounting record
            $storeRecord = $this->prepareStoreAccountingRecord($record, $storeID, $dataArray);
            $storeData[] = $storeRecord;

            // Prepare chain-level accounting record if enabled
            if ($globalChainAccounting == 1) {
                $chainRecord = $this->prepareChainAccountingRecord($record, $storeID, $dataArray, $enableSalesOrder, $enableChannelManagement, $enableCounterManagement);
                $chainData[] = $chainRecord;
            }
        }

        return [
            'storeData' => $storeData,
            'chainData' => $chainData
        ];
    }

    /**
     * Prepare store-level accounting record
     *
     * @param array $record Original accounting record
     * @param int $storeID Store ID
     * @param array $dataArray Original order data
     * @return array Prepared store accounting record
     */
    private function prepareStoreAccountingRecord($record, $storeID, $dataArray)
    {
        // Remove storeID if it exists (as per original logic)
        $storeRecord = $record;
        unset($storeRecord['storeID']);

        return $storeRecord;
    }

    /**
     * Prepare chain-level accounting record
     *
     * @param array $record Original accounting record
     * @param int $storeID Store ID
     * @param array $dataArray Original order data
     * @param int $enableSalesOrder Flag indicating if sales order is enabled
     * @param int $enableChannelManagement Flag indicating if channel management is enabled
     * @param int $enableCounterManagement Flag indicating if counter management is enabled
     * @return array Prepared chain accounting record
     */
    private function prepareChainAccountingRecord($record, $storeID, $dataArray, $enableSalesOrder, $enableChannelManagement, $enableCounterManagement)
    {
        $chainRecord = $record;

        // Handle special logic for storeID based on original implementation
        $keepExistingStoreIDFlag = $dataArray['keepExistingStoreIDFlag'] ?? 0;

        if (!$keepExistingStoreIDFlag) {
            $chainRecord['storeID'] = $storeID;
        } else {
            // Special logic for sales orders - use different storeID based on debit/credit
            if (isset($record['debit']) && $record['debit'] > 0) {
                $chainRecord['storeID'] = $dataArray['checkSO'][0]['storeID'] ?? $storeID;
            } else {
                $chainRecord['storeID'] = $storeID;
            }
        }

        // Add required fields for chain accounting
        if ($enableChannelManagement == 1) {
            $chainRecord['channelID'] = $dataArray['channelId'] ?? null;
        }
        if ($enableCounterManagement == 1) {
            $chainRecord['counterID'] = $dataArray['counterID'] ?? null;
        }

        return $chainRecord;
    }

    /**
     * Get performance metrics from the bulk insert service
     *
     * @return array Performance metrics
     */
    public function getPerformanceMetrics()
    {
        return $this->bulkInsertService->getPerformanceMetrics();
    }

    /**
     * Calculate expected performance improvement
     *
     * @param int $recordCount Number of accounting records
     * @return array Performance improvement estimates
     */
    public function calculatePerformanceImprovement($recordCount)
    {
        // Original approach: 1-2 queries per record (store + optional chain)
        $originalQueries = $recordCount * 2; // Assuming both store and chain

        // Bulk approach: 2 queries total (1 for store, 1 for chain)
        $bulkQueries = 2;

        $queryReduction = $originalQueries - $bulkQueries;
        $percentageReduction = ($queryReduction / $originalQueries) * 100;

        return [
            'original_queries' => $originalQueries,
            'bulk_queries' => $bulkQueries,
            'query_reduction' => $queryReduction,
            'percentage_reduction' => round($percentageReduction, 2),
            'estimated_time_savings' => round($queryReduction * 0.5, 2) . 'ms' // Estimate 0.5ms per query saved
        ];
    }
}
