<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Facades\Log AS Log;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that should not be reported.
     *
     * @var array
     */
    protected $dontReport = [
        HttpException::class,
        ModelNotFoundException::class,
    ];

    /**
     * Report or log an exception.
     *
     * This is a great spot to send exceptions to Sentry, Bugsnag, etc.
     *
     * @param  \Exception  $e
     * @return void
     */
    public function report(Exception $e)
    {
        

    if ($this->shouldReport($e) && env('APP_ENV') == 'production') {
        app('sentry')->captureException($e);
    }
        parent::report($e);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Exception  $e
     * @return \Illuminate\Http\Response
     */
    public function render($request, Exception $e)
    {
        //Log::error("New error found", ["exception" => $e]);
        $errorArray = array();
        $errorArray["message"] = $e->getMessage();
        $errorArray["errorFile"] = $e->getFile();
        $errorArray["errorLine"] = $e->getLine();
        //$errorArray["errorTrace"] = $e->getTrace();
        $request->getError = $errorArray;
        
        //Log::error("check request", ["request" => $request->header('Authorization')]);
        $request->exceptionData = $e->getMessage();
        if ($e instanceof ModelNotFoundException) {
            $e = new NotFoundHttpException($e->getMessage(), $e);
        }elseif ($e instanceof \Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException) {
            $response = array();
            $response['status'] = false;
            $response['message'] ='Too many requests';
        return response()->json($response,201,[],JSON_NUMERIC_CHECK);
    }
    // elseif ($e instanceof \Symfony\Component\Debug\Exception\FatalErrorException) {
    //     $e = new HttpException($e->getMessage(), $e);
    //     $request->exceptionData = $e->getMessage();
    //     $response = array();
    //     $response['exceptionData'] = $e->getMessage();
    //     return response()->json($response,500,[],JSON_NUMERIC_CHECK);
    // }


        return parent::render($request, $e);
    }
}
