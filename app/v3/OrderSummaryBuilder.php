<?php

namespace App\v3;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\DatabaseManager;

class OrderSummaryBuilder
{
    // Cached data to prevent repeated queries
    private static $crmEligibilityCache = [];
    
    /**
     * Create an optimized order summary with reduced database queries
     *
     * @param int $storeID The store ID
     * @param int $chainID The chain ID
     * @param array $dataArray The data array
     * @param array $tables The tables array
     * @param array $settings The settings array
     * @return array The order summary array
     */
    public static function createOrderSummary($storeID, $chainID, $dataArray, $tables, $settings)
    {
        // Extract all needed settings once at the beginning
        $chainSettings = $settings['chain'] ?? [];
        $storeSettings = $settings['store'] ?? [];
        
        // Preload commonly used settings
        $enableOrderReturnWithoutInvoice = $chainSettings['enableOrderReturnWithoutInvoice'] ?? 0;
        $customGstOrderPrefix = $storeSettings['customGSTOrderPrefix'] ?? '';
        $enableSourceTax = $chainSettings['enableSourceTax'] ?? 0;
        $enableSalesOrder = $chainSettings['enableSalesOrder'] ?? 0;
        $isManoramaIntegrationEnabled = $chainSettings['isManoramaIntegrationEnabled'] ?? 0;
        $noLoyaltyIfDiscount = $chainSettings['noLoyaltyIfDiscount'] ?? 0;
        $enableMunicipalIntegration = $storeSettings['enableMunicipalIntegration'] ?? 0;
        $municipalType = $storeSettings['municipalType'] ?? '';
        $enableChannelManagement = $chainSettings['enableChannelManagement'] ?? 0;
        $enableCounterManagement = $chainSettings['enableCounterManagement'] ?? 0;
        $enableIRN = $chainSettings['enableIRN'] ?? 0;
        
        // Initialize order summary with base fields and default values
        $orderSummary = [
            'orderID' => $dataArray['orderID'] ?? null,
            'currencyCode' => $dataArray['currency'] ?? null,
            'timezone' => $dataArray['timezone'] ?? null,
            'userName' => $dataArray['userName'] ?? null,
            'deviceID' => $dataArray['deviceID'] ?? null,
            'netBill' => $dataArray['netBill'] ?? null,
            'paymentStatus' => $dataArray['paymentStatus'] ?? 'PAID',
            'orderType' => $dataArray['orderType'] ?? null,
            'orderSource' => $dataArray['orderSource'] ?? 'QB',
            'deliveryStatus' => $dataArray['deliveryStatus'] ?? 'DELIVERED',
            'PINumber' => $dataArray['PINumber'] ?? null,
            'isOrderRounded' => $dataArray['isOrderRounded'] ?? 1,
            'billingUsername' => $dataArray['billingUsername'] ?? null,
            'isOpenBill' => $dataArray['isOpenBill'] ?? 0,
            'isPriceInclusive' => $dataArray['openBill']['isPriceInclusive'] ?? 0,
            'openBillAmount' => $dataArray['openBill']['openBillAmount'] ?? 0.00,
            'isQuickBill' => $dataArray['isQuickBill'] ?? 0,
            'discountTotalValue' => $dataArray['discountValue'] ?? 0,
            'totalProductLevelAdditionalCharges' => $dataArray['totalProductLevelAdditionalCharges'] ?? 0.0000,
            'totalChargeValue' => $dataArray['totalChargeValue'] ?? 0.0000,
            'storeGstNumber' => $dataArray['storeGstNumber'] ?? null,
            'storeFssaiNo' => $dataArray['storeFssaiNo'] ?? null,
            'storePanNumber' => $dataArray['storePanNumber'] ?? null,
            'storeTinNumber' => $dataArray['storeTinNumber'] ?? null,
            'taxes' => $dataArray['taxes'] ?? null,
            'rounding' => $dataArray['rounding'] ?? null,
            'grossBill' => $dataArray['grossBill'] ?? null,
            'payableAmount' => $dataArray['payableAmount'] ?? $dataArray['grossBill'] ?? null,
            'cashTendered' => $dataArray['cashTendered'] ?? null,
            'changeAmount' => $dataArray['changeAmount'] ?? null,
            'orderRemarks' => $dataArray['orderRemarks'] ?? null,
            'numReceiptPrints' => $dataArray['numReceiptPrints'] ?? null,
            'Status' => $dataArray['Status'] ?? null,
            'isNoCharge' => $dataArray['isNoCharge'] ?? 0,
        ];
        
        // Set timezone and handle datetime fields once
        $timezone = $dataArray['timezone'];
        date_default_timezone_set($timezone);
        $orderLogTimeLocal = date("Y-m-d H:i:s");
        date_default_timezone_set('UTC');
        $orderLogTimeUTC = date("Y-m-d H:i:s");
        
        $orderSummary['orderLogTimeLocal'] = $orderLogTimeLocal;
        $orderSummary['orderLogTimeUTC'] = $orderLogTimeUTC;
        
        // Process loyalty points (optimized to reduce DB queries)
        self::processLoyaltyPoints($orderSummary, $dataArray, $storeID, $chainID, $settings, $noLoyaltyIfDiscount);
        
        // Process discounts (only once - removed duplication)
        self::processDiscounts($orderSummary, $dataArray);
        
        // Process additional charges
        self::processAdditionalCharges($orderSummary, $dataArray);
        
        // Process open bill taxes
        self::processOpenBillTaxes($orderSummary, $dataArray);
        
        // Add conditional fields based on features being enabled
        if ($enableSalesOrder && isset($dataArray['salesOrderID']) && !empty($dataArray['salesOrderID'])) {
            $orderSummary['salesOrderID'] = $dataArray['salesOrderID'];
        } else if ($enableSalesOrder) {
            $orderSummary['salesOrderID'] = null;
        }
        
        if ($enableOrderReturnWithoutInvoice) {
            $orderSummary['isNoInvoiceSale'] = $dataArray['isNoInvoiceSale'] ?? 0;
        }
        
        if ($isManoramaIntegrationEnabled && isset($dataArray['subscriptionOrderType'])) {
            $orderSummary['subscriptionOrderType'] = $dataArray['subscriptionOrderType'];
        }
        
        // Process tax source data (TDS/TCS) if enabled
        if ($enableSourceTax) {
            self::processTaxSourceData($orderSummary, $dataArray);
        }
        
        // Process optional fields that only need to be included if present
        $optionalFields = [
            'aircraftID', 'airlineID', 'tripID', 'legID', 'sectorID', 'routeID', 'flightNumber',
            'airlineName', 'ISRNumber', 'tableID', 'tableName', 'serverName', 'paxSize',
            'appointmentID', 'posDate', 'batchID', 'invoiceNumber', 'customerIDs',
            'departmentConfigID'
        ];
        
        foreach ($optionalFields as $field) {
            if (isset($dataArray[$field]) && !empty($dataArray[$field])) {
                $orderSummary[$field] = $dataArray[$field];
            }
        }
        
        // Handle seat number from customers array (special case)
        if (isset($dataArray['customers'][0]['seatNumber']) && !empty($dataArray['customers'][0]['seatNumber'])) {
            $orderSummary['seatNumber'] = $dataArray['customers'][0]['seatNumber'];
        }
        
        // Process order creation time with a single conversion
        if (isset($dataArray['orderCreationTimeLocal'])) {
            $orderSummary['orderCreationTimeLocal'] = $dataArray['orderCreationTimeLocal'];
            $orderSummary['orderCreationTimeUTC'] = self::localToUTC($orderSummary['orderCreationTimeLocal'], $timezone);
        }
        
        // Handle special fields based on settings
        if (!empty($customGstOrderPrefix) && !empty($orderSummary['invoiceNumber'])) {
            $orderSummary['gstOrderNumber'] = $customGstOrderPrefix . '-' . $orderSummary['invoiceNumber'];
        }
        
        if ($enableIRN && isset($dataArray['IRNNumber']) && !empty($dataArray['IRNNumber'])) {
            $orderSummary['IRNNumber'] = $dataArray['IRNNumber'];
            if (isset($dataArray['irnQRCode']) && !empty($dataArray['irnQRCode'])) {
                $orderSummary['irnQRCode'] = $dataArray['irnQRCode'];
            }
        }
        
        if (isset($dataArray['ewayBillNo']) && !empty($dataArray['ewayBillNo'])) {
            $orderSummary['ewayBillNo'] = $dataArray['ewayBillNo'];
        }
        
        if (isset($dataArray['CANumber']) && !empty($dataArray['CANumber'])) {
            $orderSummary['dummy2'] = $dataArray['CANumber'];
        }
        
        if (isset($dataArray['pineLabsClientID']) && !empty($dataArray['pineLabsClientID'])) {
            $orderSummary['dummy1'] = $dataArray['pineLabsClientID'];
        }
        
        // Process municipal integration data
        if ($enableMunicipalIntegration == 1 && strtoupper($municipalType) == 'PUNJAB') {
            $orderSummary['MUNBillNumber'] = $dataArray['billDetailForPunjabMunciple'] ?? null;
            $orderSummary['MUNChallanNumber'] = $dataArray['challanNumberForPunjabMunciple'] ?? null;
        }
        
        // Process channel management data
        if ($enableChannelManagement == 1) {
            if (isset($dataArray['channelId']) && intval($dataArray['channelId']) > 0) {
                $orderSummary['channelID'] = intval($dataArray['channelId']);
            } elseif (isset($dataArray['channelID']) && intval($dataArray['channelID']) > 0) {
                $orderSummary['channelID'] = intval($dataArray['channelID']);
            }
        }
        
        // Process counter management data
        if ($enableCounterManagement == 1 && isset($dataArray['counterID']) && $dataArray['counterID'] > 0) {
            $orderSummary['counterID'] = $dataArray['counterID'];
        }
        
        // Process sales person data
        $salesPersonFields = ['salesPersonID', 'salesPersonName', 'salesPersonPhone', 'salesPersonEmail'];
        foreach ($salesPersonFields as $field) {
            if (isset($dataArray[$field]) && (!is_string($dataArray[$field]) || !empty($dataArray[$field]))) {
                $orderSummary[$field] = $dataArray[$field];
            }
        }
        
        return $orderSummary;
    }
    
    /**
     * Process loyalty points with optimized database access
     */
    private static function processLoyaltyPoints(&$orderSummary, $dataArray, $storeID, $chainID, $settings, $noLoyaltyIfDiscount)
    {
        // Default values
        $orderSummary['loyaltyPointsRedeemed'] = $dataArray['loyaltyPointsRedeemed'] ?? 0.00;
        
        if (isset($dataArray['loyaltyID']) && $dataArray['loyaltyID'] != -1) {
            $orderSummary['loyaltyID'] = $dataArray['loyaltyID'];
        } else {
            $purchaseAmount = $settings['chain']['purchaseAmount'] ?? 0;
            $minimumOrderValueToEarnPoints = $settings['chain']['minimumOrderValueToEarnPoints'] ?? 0;
            $orderSummary['loyaltyPointsCollected'] = $settings['chain']['loyaltyPointsCollected'] ?? 0;
            $orderSummary['loyaltyID'] = $settings['chain']['loyaltyID'] ?? 0;
        }
        
        // Don't give loyalty points if discount applied and setting is enabled
        if ($noLoyaltyIfDiscount == 1 && $orderSummary['discountTotalValue'] > 0) {
            $orderSummary['loyaltyPointsCollected'] = 0;
            return;
        }
        
        // Only calculate loyalty points if we have a customer
        if (isset($dataArray['finalCustomerID']) && $dataArray['finalCustomerID'] > 0) {
            // Cache key for CRM eligibility check
            $cacheKey = "{$chainID}_{$storeID}_{$dataArray['finalCustomerID']}";
            
            // Check if we have cached result to avoid database query
            if (!isset(self::$crmEligibilityCache[$cacheKey])) {
                self::$crmEligibilityCache[$cacheKey] = checkCRMEligibility(
                    $chainID, 
                    $storeID, 
                    $dataArray['finalCustomerID'], 
                    $settings['store']
                );
            }
            
            $crmEnabled = self::$crmEligibilityCache[$cacheKey];
            
            if ($crmEnabled['enableCRM'] == 1 && $crmEnabled['enablePointsEarning'] == 1) {
                $purchaseAmount = $settings['chain']['purchaseAmount'] ?? 0;
                $minimumOrderValueToEarnPoints = $settings['chain']['minimumOrderValueToEarnPoints'] ?? 0;
                
                if ($orderSummary['grossBill'] < $purchaseAmount || 
                    $orderSummary['grossBill'] < $minimumOrderValueToEarnPoints || 
                    $purchaseAmount == 0 || 
                    $orderSummary['isNoCharge'] == 1) {
                    $orderSummary['loyaltyPointsCollected'] = 0;
                } else {
                    // Check for loyalty payment
                    $loyaltyPaymentKey = array_search(
                        'PAYMENT_LOYALTY', 
                        array_column($dataArray['paymentList'] ?? [], 'paymentType')
                    );
                    
                    if (is_int($loyaltyPaymentKey)) {
                        $newBill = $orderSummary['grossBill'] - $dataArray['paymentList'][$loyaltyPaymentKey]['amount'];
                        $orderSummary['loyaltyPointsCollected'] = intval($newBill / $purchaseAmount);
                    } else {
                        $orderSummary['loyaltyPointsCollected'] = intval($orderSummary['grossBill'] / $purchaseAmount);
                    }
                }
            }
        }
    }
    
    /**
     * Process discounts efficiently
     */
    private static function processDiscounts(&$orderSummary, $dataArray)
    {
        if (!isset($dataArray['discounts'])) {
            return;
        }
        
        $discounts = $dataArray['discounts'];
        $discountCount = count($discounts);
        
        if ($discountCount > 0) {
            $discountId = [];
            $discountName = [];
            $discountValue = [];
            
            foreach ($discounts as $discount) {
                $discountId[] = $discount['discountID'];
                $discountName[] = $discount['discountName'];
                $discountValue[] = $discount['discountValue'];
            }
            
            $orderSummary['discountIDs'] = self::keyImplode($discountId);
            $orderSummary['discountNames'] = self::keyImplode($discountName);
            $orderSummary['discountValues'] = self::keyImplode($discountValue);
        }
    }
    
    /**
     * Process additional charges with optimized tax handling
     */
    private static function processAdditionalCharges(&$orderSummary, $dataArray)
    {
        if (!isset($dataArray['additionalCharges'])) {
            return;
        }
        
        $additionalCharges = $dataArray['additionalCharges'];
        $additionalChargeCount = count($additionalCharges);
        
        if ($additionalChargeCount > 0) {
            $additionalChargeId = [];
            $additionalChargeName = [];
            $additionalChargePrice = [];
            $additionalChargeValue = [];
            $orderTaxArray = [];
            
            foreach ($additionalCharges as $charge) {
                $additionalChargeId[] = $charge['additionalChargeID'];
                $additionalChargeName[] = $charge['additionalChargeName'];
                $additionalChargePrice[] = $charge['additionalChargePrice'];
                $additionalChargeValue[] = $charge['additionalChargeValue'];
                
                // Process taxes for this charge
                if (isset($charge['taxes']) && !empty($charge['taxes'])) {
                    foreach ($charge['taxes'] as $tax) {
                        $taxID = $tax['taxID'];
                        $taxKey = array_search($taxID, array_column($orderTaxArray, 'taxID'));
                        
                        if (is_int($taxKey)) {
                            $orderTaxArray[$taxKey]['taxValue'] += $tax['taxValue'];
                        } else {
                            $orderTaxArray[] = [
                                'taxID' => $taxID,
                                'taxName' => $tax['taxName'],
                                'taxPercentage' => $tax['taxPercentage'],
                                'taxValue' => $tax['taxValue']
                            ];
                        }
                    }
                }
            }
            
            // Set values only if arrays are not empty
            if (!empty($additionalChargeId)) {
                $orderSummary['additionalChargeIDs'] = self::keyImplode($additionalChargeId);
            } else {
                $orderSummary['additionalChargeIDs'] = null;
            }
            
            if (!empty($additionalChargeName)) {
                $orderSummary['additionalChargeNames'] = self::keyImplode($additionalChargeName);
            } else {
                $orderSummary['additionalChargeNames'] = null;
            }
            
            if (!empty($additionalChargePrice)) {
                $orderSummary['additionalChargePrices'] = self::keyImplode($additionalChargePrice);
            } else {
                $orderSummary['additionalChargePrices'] = null;
            }
            
            if (!empty($additionalChargeValue)) {
                $orderSummary['additionalChargeValue'] = self::keyImplode($additionalChargeValue);
            } else {
                $orderSummary['additionalChargeValue'] = null;
            }
            
            // Process order tax array
            if (!empty($orderTaxArray)) {
                $orderTaxIDArray = [];
                $orderTaxNameArray = [];
                $orderTaxPercentageArray = [];
                $orderTaxValueArray = [];
                $orderTotalTaxValue = 0;
                
                foreach ($orderTaxArray as $tax) {
                    $orderTaxIDArray[] = $tax['taxID'];
                    $orderTaxNameArray[] = $tax['taxName'];
                    $orderTaxPercentageArray[] = $tax['taxPercentage'];
                    $orderTaxValueArray[] = $tax['taxValue'];
                    $orderTotalTaxValue += $tax['taxValue'];
                }
                
                $orderSummary['orderTaxIDs'] = rtrim(implode(',', $orderTaxIDArray));
                $orderSummary['orderTaxNames'] = rtrim(implode(',', $orderTaxNameArray));
                $orderSummary['orderTaxPercentages'] = rtrim(implode(',', $orderTaxPercentageArray));
                $orderSummary['orderTaxValues'] = rtrim(implode(',', $orderTaxValueArray));
                $orderSummary['orderTotalTaxValue'] = $orderTotalTaxValue;
            }
        }
    }
    
    /**
     * Process open bill taxes
     */
    private static function processOpenBillTaxes(&$orderSummary, $dataArray)
    {
        if (!isset($dataArray['openBill']['openBillTaxes']) || empty($dataArray['openBill']['openBillTaxes'])) {
            return;
        }
        
        $taxes = $dataArray['openBill']['openBillTaxes'];
        $taxCount = count($taxes);
        
        if ($taxCount > 0) {
            $taxID = [];
            $taxName = [];
            $taxPercentage = [];
            $taxValue = [];
            
            foreach ($taxes as $tax) {
                $taxID[] = $tax['taxID'];
                $taxName[] = $tax['taxName'];
                $taxPercentage[] = $tax['taxPercentage'];
                $taxValue[] = $tax['taxValue'];
            }
            
            $orderSummary['openBillTaxIDs'] = self::keyImplode($taxID);
            $orderSummary['openBillTaxNames'] = self::keyImplode($taxName);
            $orderSummary['openBillTaxPercentages'] = self::keyImplode($taxPercentage);
            $orderSummary['openBillTaxValues'] = self::keyImplode($taxValue);
        }
    }
    
    /**
     * Process tax source data (TDS/TCS)
     */
    private static function processTaxSourceData(&$orderSummary, $dataArray)
    {
        // Process TDS (Tax Deducted at Source)
        if (isset($dataArray['TDSValue']) && $dataArray['TDSValue'] > 0) {
            $orderSummary['TDSID'] = $dataArray['TDSID'] ?? 0;
            $orderSummary['TDSName'] = $dataArray['TDSName'] ?? 0;
            $orderSummary['TDSPercentage'] = $dataArray['TDSPercentage'] ?? 0;
            $orderSummary['TDSValue'] = $dataArray['TDSValue'] ?? 0;
        }
        
        // Process TCS (Tax Collected at Source)
        if (isset($dataArray['TCSValue']) && $dataArray['TCSValue'] > 0) {
            $orderSummary['TCSID'] = $dataArray['TCSID'] ?? 0;
            $orderSummary['TCSName'] = $dataArray['TCSName'] ?? 0;
            $orderSummary['TCSPercentage'] = $dataArray['TCSPercentage'] ?? 0;
            $orderSummary['TCSValue'] = $dataArray['TCSValue'] ?? 0;
        }
    }
    
    /**
     * Helper method to implode arrays with key checking
     */
    private static function keyImplode($array)
    {
        return implode(',', array_map(function($item) {
            return $item ?? '';
        }, $array));
    }
    
    /**
     * Convert local time to UTC with caching
     */
    private static function localToUTC($localTime, $timezone)
    {
        // This could be enhanced with caching for multiple conversions if needed
        return localToUTC($localTime, $timezone);
    }
}