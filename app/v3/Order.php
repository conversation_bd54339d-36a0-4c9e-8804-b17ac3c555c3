<?php

namespace App\v3;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\DatabaseManager;
use App\Services\SettingsManager;
use App\Services\BulkInsertService;
use App\TableRegistry;
use App\Customers as Customers;
use App\Municipal as Municipal;
use App\AyodhyaMunicipal as AyodhyaMunicipal;
use App\farrukhabadMunicipal as farrukhabadMunicipal;
use App\Rwanda as Rwanda;
use App\GST as GST;
use App\Avis as Avis;
use App\Account as Accounts;
use App\Admin as Admin;
use App\Message;
use App\hindiDateConvert as hindiDateConvert;
use App\Agriculture AS Agriculture;
use App\productOpticals AS ProductOpticals;
use App\TataMotors AS TataMotors;
use App\StockLedger AS StockLedger;
use App\SalesOrder;
use App\QBWallet as QBWallet;
use App\SalesControl;
use App\TaxFiling AS TaxFiling;
use App\PartyMasterLedger AS PartyMasterLedger;
use App\CashManagement AS Cash;
use App\Jobs\RecordGeneralLedgerJob;
use App\Jobs\CostaCoffeeJob;
use App\Order as OldOrder;
use DateTime;
use Carbon\Carbon;

class Order extends Model
{
    // Static properties for order processing
    private static $finalCreditAmount = 0.00;
    private static $finalCustCredit = array();
    private static $finalChainCreditAmount = 0.00;
    private static $finalChainCustCredit = array();
    private static $checkLoyalty = 0;
    private static $finalCustArray = array();
    private static $checkExchange = 1;
    private static $exchangeValue = 0;
    private static $finalSettleLedgerID = 0;
    private static $finalReservationDetails = [];
    private static $finalMembershipGUID = [];
    private static $finalCreditNoteID = 0;
    private static $createDLFGiftVoucherJobb = [];
    private static $yieldTransactionID = 0;
    private static $creditNotesGenerated = [];
    private static $finalMemTransID = 0;
    private static $finalInvoiceNumber = NULL;
    private static $finalSettledAmount = 0.00;

    /**
     * Database manager instance
     */
    protected $dbManager;

    /**
     * Settings manager instance
     */
    protected $settingsManager;

    /**
     * Create a new model instance.
     *
     * @param DatabaseManager $dbManager
     * @param SettingsManager $settingsManager
     * @return void
     */
    public function __construct(
        DatabaseManager $dbManager = null,
        SettingsManager $settingsManager = null
    ) {
        parent::__construct();
        $this->dbManager = $dbManager ?? new DatabaseManager();
        $this->settingsManager = $settingsManager ?? new SettingsManager($this->dbManager);
    }

    /**
     * Process an order
     *
     * @param int $storeID The store ID
     * @param array $dataArray The order data
     * @return array The order processing result
     */
    public static function postOrder($storeID, $dataArray, $settings) {
        //try {
        $chainID = $dataArray["chainID"];
		$enableLogging = $settings['store']['enableErrorLog'] ?? 0;

        // Get all table names from our local method
        $tables = self::getOrderTables($storeID, $chainID);

        // Extract tables into individual variables for backward compatibility
        extract($tables);

		if ($enableLogging) {
			Log::info("Order processing - chain and timezone setup: " . json_encode([
				'chainID' => $chainID,
				'storeID' => $storeID,
				'orderID' => $dataArray["orderID"],
			]));
		}

        // Function to mail when there is a discrepancy
        $status           = array();
        $status["status"] = true;
        $timezone = $settings['chain']['timezone'];
        date_default_timezone_set("UTC");
        $currentTimeUTC = date("Y-m-d H:i:s");
        date_default_timezone_set($timezone);
        $currentTimeLocal = date("Y-m-d H:i:s");
        $today = date("Y-m-d");
        $cisDate = date('d-m-Y H:i:s');
        $salesOrderProductsList = $dataArray['productsList'];

		$getChainSettings = array();
		$getChainSettings[0] = $settings['chain'];

		$storeDetail[0] = $settings['store'];

        $enableModifier = $getChainSettings[0]['enableModifier'];
        $enableDMS = $getChainSettings[0]['enableDMS'];

        $merchantIntegrationModule = $getChainSettings[0]['merchantIntegrationModule'];

        $generateInvoiceNumberFromServer = $getChainSettings[0]['generateInvoiceNumberFromServer'];
        $enableOnlineBillingOnly = $getChainSettings[0]['enableOnlineBillingOnly'];
        $enableSalesControl = $getChainSettings[0]['enableSalesControl'];
        $creditNoteExpiryDays = isset($getChainSettings[0]['creditNoteExpiryDays']) ? $getChainSettings[0]['creditNoteExpiryDays'] : 0;
        $enableCreditNoteExpiry = isset($getChainSettings[0]['enableCreditNoteExpiry']) ? $getChainSettings[0]['enableCreditNoteExpiry'] : 0;
        $enableQBWalletManagement = isset($getChainSettings[0]['enableQBWalletManagement']) ? $getChainSettings[0]['enableQBWalletManagement'] : 0;
        $enableSalesOrder = $getChainSettings[0]['enableSalesOrder'];
        $enableRMSIntegration = $getChainSettings[0]['enableRMSIntegration'];
        $enableCrossStoreCreditNoteRedemption = $getChainSettings[0]['enableCrossStoreCreditNoteRedemption'];
        $enablePartialRedemptionOfCreditNote = $getChainSettings[0]['enablePartialRedemptionOfCreditNote'];
        $recordChainLevelSalesInvoice = $getChainSettings[0]['recordChainLevelSalesInvoice'];
        $enableTokenGeneration = $getChainSettings[0]['enableTokenGeneration'];
        $enableStockLedger = $getChainSettings[0]['enableStockLedger'];
        $enableTaxFiling = $getChainSettings[0]['enableTaxFiling'];
        $enableSourceTax = $getChainSettings[0]['enableSourceTax'];
        $enableChannelManagement = $getChainSettings[0]['enableChannelManagement'];
        $enableStockLedger = $getChainSettings[0]['enableStockLedger'];
        $enableAccounting = $getChainSettings[0]['enableAccounting'];
        $enableBillMeIntegration = $getChainSettings[0]['enableBillMeIntegration'];
        if($enableCrossStoreCreditNoteRedemption != 1) {
            $chainCustomerAccountTable = NULL;
        }

        $status = ['status' => true];

        $reservationDetail = array();

        $previousStatus = $status;

        $getChainIndustry = $getChainSettings;
        $enableCurrencyConversion = $getChainIndustry[0]['enableCurrencyConversion'];
        $industry = $getChainIndustry[0]["industry"];
        $enableIRN = $getChainIndustry[0]['enableIRN'];
        $enableGST = $getChainIndustry[0]['enableGST'];
        $recordCustomerInfoOnOrder = $getChainIndustry[0]['recordCustomerInfoOnOrder'];
        $status["posDate"] = $dataArray["posDate"];
        $globalChainAccounting = $getChainIndustry[0]['enableChainOrderAccounting'];
        $enableEcomReservation = $getChainIndustry[0]['enableEcomReservation'];
        $enableProductBundle = $getChainIndustry[0]['enableProductBundle'];
        $brandName = $getChainIndustry[0]['brandName'];
        $automateProductGroupCreation = $getChainIndustry[0]['automateProductGroupCreation'];
        $enableRRAIntegration = $getChainIndustry[0]['enableRRAIntegration'];
        $enableOnlineBillingOnly = $getChainIndustry[0]['enableOnlineBillingOnly'];
        $enableGeneralLedger = $getChainIndustry[0]['enableGeneralLedger'];
        $enablePartyMaster = $getChainSettings[0]['enablePartyMaster'];
        //Reject invoiced PIs if online billing is enabled
        if($enableOnlineBillingOnly == 1)
        {
            $PINumber = $dataArray['PINumber'];

            //sales order
            $fulfillmentID = $dataArray['fulfillmentID'];

        }

        // call the rwanada push sales data function if rraIntegration is enabled
        if($enableRRAIntegration == 1){
            $dataArray['storeID'] = $storeID;
            $RRAstatus = Rwanda::pushSalesData($dataArray, $chainID);
            unset($dataArray['storeID']);
            if($RRAstatus['status'] == true){
                $dataArray['rcptNo'] = $RRAstatus['RRAResponse']['rcptNo'];
                $dataArray['intrlData'] = $RRAstatus['RRAResponse']['intrlData'];
                $dataArray['rcptSign'] = $RRAstatus['RRAResponse']['rcptSign'];
                $dataArray['totRcptNo'] = $RRAstatus['RRAResponse']['totRcptNo'];
                $dataArray['cisDate'] = $cisDate;
                $dataArray['vsdcRcptPbctDate'] = $RRAstatus['RRAResponse']['vsdcRcptPbctDate'];
                $dataArray['sdcID'] = $RRAstatus['RRAResponse']['sdcID'];
                $dataArray['mrcNo'] = $RRAstatus['RRAResponse']['mrcNo'];
                $dataArray['webRecptNo'] = $RRAstatus['RRAResponse']['webRecptNo'];
                $dataArray['RRAResponseJSON'] = $RRAstatus['RRAResponse']['RRAResponseJSON'];
                $dataArray['RRAPurchaseCode'] = $RRAstatus['RRAResponse']['RRAPurchaseCode'];

                $rcptSign =  wordwrap($RRAstatus['RRAResponse']['rcptSign'], 4, '-',true);
                $intrlData =  wordwrap($RRAstatus['RRAResponse']['intrlData'], 4, '-',true);

                $RRAresponseArray['rcptNo'] = $RRAstatus['RRAResponse']['rcptNo'].' '.'NS';
                $RRAresponseArray['intrlData'] = $intrlData;
                $RRAresponseArray['rcptSign'] = $rcptSign;
                $RRAresponseArray['totRcptNo'] = $RRAstatus['RRAResponse']['totRcptNo'];
                $RRAresponseArray['cisDate'] = $cisDate;
                $RRAresponseArray['vsdcRcptPbctDate'] = date('d-m-Y H:i:',strtotime($RRAstatus['RRAResponse']['vsdcRcptPbctDate']));
                $RRAresponseArray['sdcID'] = $RRAstatus['RRAResponse']['sdcID'];
                $RRAresponseArray['mrcNo'] = $RRAstatus['RRAResponse']['mrcNo'];
                $RRAresponseArray['mrcNo'] = $RRAstatus['RRAResponse']['mrcNo'];
                $responseArray['RRAResponseJSON'] = $RRAstatus['RRAResponse']['AppRRAResponseJSON'];
                $responseArray['RRAInvoiceNo'] = $RRAstatus['RRAInvoiceNumber'];
            }elseif($RRAstatus['status'] == false){
                $status['status'] = false;
                $status['message'] = $RRAstatus['message'];
            }
        }

		if ($enableLogging) {
			Log::info("Order processing - municipal: " . json_encode([
				'chainID' => $chainID,
				'storeID' => $storeID,
				'orderID' => $dataArray["orderID"],
			]));
		}

        // Municipal integration start
        $municipalType = $storeDetail[0]['enableMunicipalIntegration'] == 1
            ? strtoupper($storeDetail[0]['municipalType'])
            : null;

        if ($municipalType === null) {
            // No municipal work to do → bail out immediately
            goto END_OF_MUNICIPAL_SECTION;
        }

        START_OF_MUNICIPAL_SECTION:

        Municipal::saveOrderJson($dataArray, $chainID);
        // check if Municipal enable, handle temp code for mapping farmer pan number to phone
        // $storeDetail = convertToArray(DB::table($listOfStoresTable)->SELECT('enableMunicipalIntegration','municipalType', 'storeGUID')->where('storeID',$storeID)->get());
        if(!empty($storeDetail) && $storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'UP_AGRICULTURE'){
            // verify the discount and attribute 3 and 4
            // Municipal::saveOrderJson($dataArray, $chainID);
            if(isset($dataArray['productsList']) && !empty($dataArray['productsList'])){
                $productListArray = $dataArray['productsList'];
                $attribute3Array = array_column($productListArray,'attribute3');
                $attribute4Array = array_column($productListArray,'attribute4');
                $attribute14Array = array_column($productListArray,'attribute14');
                $attributeTotal = array_sum($attribute3Array) + array_sum($attribute4Array) + array_sum($attribute14Array);
                $discountTotalValue = array_sum(array_column($productListArray,'discountValues'));
                $discountDiff = abs($attributeTotal) - abs($discountTotalValue);
                $absDiscountDiff = abs($discountDiff);
                if($absDiscountDiff > 1) {
                    $tempResponse = array();
                    $tempResponse['sumOfAttribute3'] = array_sum($attribute3Array);
                    $tempResponse['sumOfAttribute4'] = array_sum($attribute4Array);
                    $tempResponse['discountTotalValue'] = $discountTotalValue;
                    $tempDataArray = $dataArray;
                    $tempDataArray['storeID'] = $storeID;
                    Municipal::recordUPAgrilLogs($tempDataArray,$tempResponse,$chainID);
                    $status['status'] = false;
                    $status['message'] = "Computation Error: Please punch the order again or contact support if the issue persists";
                    return $status;
                }
                
                
            }

            if(isset($dataArray['customers']) && !empty($dataArray['customers'])){
                $phone = $dataArray['customers'][0]['phone'];
                if (!isset($dataArray['customers'][0]['panNumber']) || $dataArray['customers'][0]['panNumber'] == '' || $dataArray['customers'][0]['panNumber'] == NULL) {
                    return array("status" => false, "message" => "Registration number is mandatory.");
                }
                $panNumber = $dataArray['customers'][0]['panNumber'];
                if($phone == 0 || $phone =='' || $phone == NULL){
                    $dataArray['customers'][0]['phone'] = $panNumber;
                }
            }
            $dataArray['storeID'] = $storeID;
            $dispatchUPAgriSales = Agriculture::UPAgriDispatchSalesInvoice($dataArray, $chainID);
            // if($dispatchUPAgriSales['status'] == false){
            //     return $dispatchUPAgriSales;
            // }
        }

        if(!empty($storeDetail) && $storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'AURIONPRO'){
            // Municipal::saveOrderJson($dataArray, $chainID);
            // verify the discount and attribute 3 and 4
            
            if(isset($dataArray['productsList']) && !empty($dataArray['productsList'])){
                $productListArray = $dataArray['productsList'];
                $attribute3Array = array_column($productListArray,'attribute3');
                $attribute4Array = array_column($productListArray,'attribute4');
                $attribute14Array = array_column($productListArray,'attribute14');
                $attributeTotal = array_sum($attribute3Array) + array_sum($attribute4Array)  + array_sum($attribute14Array);
                $discountTotalValue = array_sum(array_column($productListArray,'discountValues'));
                $discountDiff = abs($attributeTotal) - abs($discountTotalValue);
                $absDiscountDiff = abs($discountDiff);
                if($absDiscountDiff > 1) {
                    $tempResponse = array();
                    $tempResponse['sumOfAttribute3'] = array_sum($attribute3Array);
                    $tempResponse['sumOfAttribute4'] = array_sum($attribute4Array);
                    $tempResponse['discountTotalValue'] = $discountTotalValue;
                    $tempDataArray = $dataArray;
                    $tempDataArray['storeID'] = $storeID;
                    Municipal::recordUPAgrilLogs($tempDataArray,$tempResponse,$chainID);
                    $status['status'] = false;
                    $status['message'] = "Computation Error: Please punch the order again or contact support if the issue persists";
                    return $status;
                }
                
                
            }

            if(isset($dataArray['customers']) && !empty($dataArray['customers'])){
                $phone = $dataArray['customers'][0]['phone'];
                if (!isset($dataArray['customers'][0]['panNumber']) || $dataArray['customers'][0]['panNumber'] == '' || $dataArray['customers'][0]['panNumber'] == NULL) {
                    return array("status" => false, "message" => "Registration number is mandatory.");
                }
                $panNumber = $dataArray['customers'][0]['panNumber'];
                if($phone == 0 || $phone =='' || $phone == NULL){
                    $dataArray['customers'][0]['phone'] = $panNumber;
                }
            }
            $dataArray['storeID'] = $storeID;
            $dispatchUPAgriSales = Agriculture::UPAgriDispatchSalesInvoice($dataArray, $chainID);
            // if($dispatchUPAgriSales['status'] == false){
            //     return $dispatchUPAgriSales;
            // }
        }
        
        // check if Municipal enable         
        if(!empty($storeDetail) && $storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'PUNJAB'){
            // Municipal::saveOrderJson($dataArray, $chainID);
            $PBMData = array();
            $PBMData['storeID'] = $storeID;
            $PBMData['challanNumber'] = $dataArray['challanNumberForPunjabMunciple'];
            $PBMData['billNumber'] = $dataArray['billDetailForPunjabMunciple'];
            $PBMData['payments'] = $dataArray['paymentList'];
            $PBMData['amount'] = $dataArray['grossBill'];
            $PBMData['userName'] = $dataArray['billingUsername'];
            $PBMData['customerName'] = $dataArray['customers'][0]['firstName'];
            $category = $dataArray['productsList'][0]['categoryName'];
            $phone = $dataArray['customers'][0]['customerPhone'];
            $PBMData['mobileNumber'] = $phone;
            $PBMData['categoryName'] = $category;
            $PBMData['token'] = $dataArray['PMCAcesstoken']; 
            $PBMPaymentDetail = Municipal::createPBMPayment($PBMData,$chainID);
            if($PBMPaymentDetail['status'] == false){
                $status['status'] = false;
                $status['message'] = $PBMPaymentDetail;
                return $status;
            }
            $transactionNumber = $PBMPaymentDetail['transactionNumber'];
            if($transactionNumber !=''){
                $dataArray['PBMTransactionNumber'] = $transactionNumber;
                $responseArray['PBMTransactionNumber'] = $transactionNumber;
            }
            $dataArray['municipalTransactionID'] = $PBMData['billNumber'];
        }
        
        if(!empty($storeDetail) && $storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'TAMIL_NADU'){
            // Municipal::saveOrderJson($dataArray, $chainID);
            $TMCData = array();
            if(isset($dataArray['service']) && $dataArray['service'] != ''){
                $service = strtolower($dataArray['service']);
            } else{
                $service = "property";
            }
            $deviceID = $dataArray['deviceID'];
            $paymentType = $dataArray['paymentList'][0]['paymentType'];
            list($paymentType1,$paymentType2) = explode('_',$paymentType);
            $phone = $dataArray['customers'][0]['phone'];
            $TMCData['deviceID'] = $deviceID;
            $TMCData['amount'] = $dataArray['grossBill'];
            $TMCData['userID'] = $dataArray['billingUsername'];
            $TMCData['instrumentType'] = ucfirst(strtolower($paymentType2));
            $TMCData['mobileNumber'] = $phone;
            $TMCData['bankName'] = $dataArray['bankName'];
            $TMCData['branch'] = $dataArray['branch'];
            $TMCData['chequeNo'] = $dataArray['chequeNo'];
            $TMCData['chequeDate '] = $dataArray['chequeDate '];
            $TMCData['approvalNo '] = $dataArray['approvalNo'];
            $TMCData['storeID'] = $storeID;
            $TMCData['token'] = $dataArray['TMCAccessToken'];
            $TMCData['payments'] = $dataArray['paymentList'];
            $TMCData['token'] = $dataArray['TMCAcesstoken'];
            $tmcDetail = convertToArray(json_decode($dataArray['tmcData'])); 
            $TMCData['TMCData'] = $tmcDetail;
            $TMCPayment = Municipal::TNMPayment($TMCData,$chainID);
            if($TMCPayment['status']==false){
                $status['status'] = false;
                $status['message'] = $TMCPayment['message'];
                return $status;
            }
            $receiptNumber = $TMCPayment['receiptNumber'];
            if($receiptNumber !=''){
                $dataArray['TMCReceiptNumber'] = $receiptNumber;
                $responseArray['TMCReceiptNumber'] = $receiptNumber;
            }
            if(isset($tmcDetail['assessmentNo']) && strlen($tmcDetail['assessmentNo']) > 0){
                $dataArray['municipalTransactionID'] = $tmcDetail['assessmentNo'];
            }elseif(isset($tmcDetail['oldAssessmentNo']) && strlen($tmcDetail['oldAssessmentNo']) == 0){
                $dataArray['municipalTransactionID'] = $dataArray['oldAssessmentNo'];
            }
            
        }

        if(!empty($storeDetail) && $storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'UPPCL'){
            // Municipal::saveOrderJson($dataArray, $chainID);
            $UPPCLData = array();
            $UPPCLData['storeID'] = $storeID;
            $UPPCLData['consumerNumber'] = $dataArray['consumerNumber'];
            $UPPCLData['timestamp'] = $dataArray['orderCreationTimeLocal'];
            $UPPCLData['orderID'] = $dataArray['orderID'];
            $UPPCLData['amount'] = $dataArray["paymentList"][0]['amount'];
            $UPPCLData['UPPCLBillID'] = $dataArray['UPPCLBillID'];
            $UPPCLData['connectionType'] = $dataArray['connectionType'];            
            $UPPCLData['paymentMode'] = $dataArray["paymentList"][0]['paymentType'];
            $UPPCLData['transactionID'] = $dataArray['transactionID'];
            $UPPCLPaymentDetail = Municipal::postUPPCLPayment($UPPCLData,$chainID);
            if($UPPCLPaymentDetail['status'] == false){
                $status['status'] = false;
                $status['message'] = $UPPCLPaymentDetail;
                return $status;
            }
            $dataArray['municipalTransactionID'] = "APOS".$dataArray['orderID'];
        }

        if(!empty($storeDetail) && $storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'BSES'){
            // Municipal::saveOrderJson($dataArray, $chainID);
            $BSESData = array();
            // record payment intent 
            list($paymentType1,$paymentType2) =  explode('_',$dataArray["paymentList"][0]['paymentType']);
            $paymentIntentArray = array();
            date_default_timezone_set('UTC');
            $paymentIntentArray['creationTimeUTC'] = date("Y-m-d H:i:s");
            date_default_timezone_set($timezone);
            $paymentIntentArray['creationTimeLocal'] = date("Y-m-d H:i:s");
            $paymentIntentArray['orderID'] = $dataArray['orderID'];
            $paymentIntentArray['transactionID'] = $dataArray['orderID']."-".$dataArray['invoiceNumber'];
            //$paymentIntentArray['transactionID'] = $dataArray['CANumber'];
            $paymentIntentArray['paymentStatus'] = "PENDING";
            $paymentIntentArray['paymentMode'] = $paymentType2;
            $paymentIntentArray['timezone'] = $timezone;
            $paymentIntentID = DB::table($thirdPartyPaymentIntentTable)->insertGetId($paymentIntentArray);
            $BSESData['storeID'] = $storeID;
            $BSESData['orderID'] = $dataArray['orderID'];
            $BSESData['billingUser'] = $dataArray['billingUsername'];
            $BSESData['CANumber'] = $dataArray['CANumber'];
            $BSESData['amount'] = $dataArray['netBill'];
            $BSESData['paymentMode'] = $paymentType2;
            $BSESData['amount'] = $dataArray["paymentList"][0]['amount'];
            if($paymentType2 == 'CHEQUE'){
                $BSESData['chequeNo'] = $dataArray["paymentList"][0]['transactionID'];
                $BSESData['chequeDate'] = date('d-m-Y',strtotime($dataArray["paymentList"][0]['posDate']));          
                $BSESData['bankName'] = $dataArray["paymentList"][0]['cardHolderName'];
                $BSESData['branchName'] = $dataArray["paymentList"][0]['approvalCode'];
            }
            if($paymentType2 == 'UPI'){
                $BSESData['chequeNo'] = $dataArray["paymentList"][0]['transactionID'];
                $BSESData['chequeDate'] = date('d-m-Y',strtotime($dataArray["paymentList"][0]['posDate']));           
                $BSESData['bankName'] = $dataArray["paymentList"][0]['lastFourDigits'];
                $BSESData['branchName'] = $paymentType2;
            }

            if($paymentType2 == 'CARD'){
                $BSESData['chequeNo'] = $dataArray["paymentList"][0]['transactionID'];
                $BSESData['chequeDate'] = date('d-m-Y',strtotime($dataArray["paymentList"][0]['posDate']));          
                $BSESData['bankName'] = "HDFC";
                $BSESData['branchName'] = $paymentType2;
            }
            if($paymentType2 == 'BHARATQR'){
                $BSESData['chequeNo'] = $dataArray["paymentList"][0]['transactionID'];
                $BSESData['chequeDate'] = date('d-m-Y',strtotime($dataArray["paymentList"][0]['posDate']));           
                $BSESData['bankName'] = $dataArray["paymentList"][0]['lastFourDigits'];
                $BSESData['branchName'] = $paymentType2;
            }
            $BSESData['posID'] = $dataArray['deviceID'];
            $BSESData['netBill'] = $dataArray['netBill'];
            $BSESData['dueDate'] = date('d-m-Y',strtotime($dataArray['dueDate']));
            $BSESData['company'] = "R";
            $BSESData['division'] = "W2TGN";
            $BSESData['transactionID'] = $dataArray['invoiceNumber'];
            /*$BSESPaymentDetail = Municipal::postBSESPayment($BSESData,$chainID);
            $responseArray['BSESPaymentResponse'] = $BSESPaymentDetail['BSSPaymentResponse'];
            $responseArray['transactionID'] = $BSESPaymentDetail['BSSPaymentResponse'];
            if($BSESPaymentDetail['status'] == true){
                $updatePaymentIntentArray = array();
                $updatePaymentIntentArray['paymentStatus'] = "APPROVED";
                $updatePaymentIntentArray['remarks'] = $BSESPaymentDetail["message"];
                date_default_timezone_set('UTC');
                $updatePaymentIntentArray['creationTimeUTC'] = date("Y-m-d H:i:s");
                date_default_timezone_set($timezone);
                $updatePaymentIntentArray['creationTimeLocal'] = date("Y-m-d H:i:s");
                DB::table($thirdPartyPaymentIntentTable)->where('ID',$paymentIntentID)->update($updatePaymentIntentArray);
            } else{
                $updatePaymentIntentArray = array();
                $updatePaymentIntentArray['paymentStatus'] = "DECLINED";
                $updatePaymentIntentArray['remarks'] = $BSESPaymentDetail["message"];
                date_default_timezone_set('UTC');
                $updatePaymentIntentArray['lastUpdatedAtUTC'] = date("Y-m-d H:i:s");
                date_default_timezone_set($timezone);
                $updatePaymentIntentArray['lastUpdatedAtLocal'] = date("Y-m-d H:i:s");
                DB::table($thirdPartyPaymentIntentTable)->where('ID',$paymentIntentID)->update($updatePaymentIntentArray);

            }
            $dataArray['municipalTransactionID'] = $dataArray['orderID']; */
        }
        // municiapl type AUM
    
        if(!empty($storeDetail) && $storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'AUM'){
            // Municipal::saveOrderJson($dataArray, $chainID);
            $AUMdata = array();
            $AUMdata['storeID'] = $storeID;
            $AUMdata['deviceID'] = $dataArray['deviceID'];
            $AUMdata['CANumber'] = $dataArray['CANumber'];
            $AUMdata['customerMobile'] = $dataArray['customers'][0]['phone'];
            $AUMdata['amount'] = floatval($dataArray['grossBill'])*100;
            $AUMdata['billNumber'] = $dataArray['billNumber'];
            $transactionID = substr(str_replace("-","",$dataArray['orderID']),2);
            $AUMdata['transactionID'] = $transactionID;
            $BSESData['amount'] = $dataArray["paymentList"][0]['amount'];
            list($paymentType1,$paymentType2) =  explode('_',$dataArray["paymentList"][0]['paymentType']);
            $AUMdata['paymentMode'] = $paymentType2;
            $AUMdata['service'] = $dataArray['productsList'][0]['sku'];
            // $cardInfo = array();
            // $chequeInfo = array();
            // $cashInfo = array();
            $cashInfo = null;
            
            $cardInfo['cardAmount'] = 0;
            $cardInfo['cardApprovalCode'] = 0;
            $cardInfo['cardMaskedNumber'] = 0;          
            $cardInfo['cardTid'] = 0;
            $cardInfo['cardAcquirer'] = 0;
            $cardInfo['cardAcquirerMid'] = 0;
            $cardInfo['cardHolderName'] = 0;

            $chequeInfo['chequeAmount'] = 0;
            $chequeInfo['chequeNumber'] = 0;         
            $chequeInfo['chequeMicr'] = 0;
            $chequeInfo['chequeDate'] = 0;
            $chequeInfo['chequeBank'] = 0;

            $upiInfo['qrAmount'] = 0;
            $upiInfo['qrTransactionId'] = 0;
            $upiInfo['qrHost'] = 0;

            if($paymentType2 == 'CASH'){
                $cashInfo['cashAmount'] = $dataArray["paymentList"][0]['amount'] * 100;
            }
            if($paymentType2 == 'CHEQUE'){
                $chequeInfo['chequeAmount'] = $dataArray["paymentList"][0]['amount'] * 100;
                $chequeInfo['chequeNumber'] = $dataArray["paymentList"][0]['transactionID'];         
                $chequeInfo['chequeMicr'] = $dataArray["paymentList"][0]['transactionID'];
                $chequeInfo['chequeDate'] = date('dmY',strtotime($dataArray["paymentList"][0]['posDate']));
                $chequeInfo['chequeBank'] = $dataArray["paymentList"][0]['cardHolderName'];
                
            }
            if($paymentType2 == 'CARD'){
                $cardInfo['cardAmount'] = $dataArray["paymentList"][0]['amount'] * 100;
                $cardInfo['cardApprovalCode'] = $dataArray["paymentList"][0]['transactionID'];
                $cardInfo['cardMaskedNumber'] = date('d-m-Y',strtotime($dataArray["paymentList"][0]['posDate']));          
                $cardInfo['cardTid'] = $dataArray["paymentList"][0]['approvalCode'];
                $cardInfo['cardAcquirer'] = $dataArray["paymentList"][0]['approvalCode'];
                $cardInfo['cardAcquirerMid'] = $dataArray["paymentList"][0]['approvalCode'];
                $cardInfo['cardHolderName'] = $dataArray["paymentList"][0]['approvalCode'];
                
            }

            if($paymentType2 == 'UPI'){
                $upiInfo['qrAmount'] = $dataArray["paymentList"][0]['amount'] * 100;
                $upiInfo['qrTransactionId'] = $dataArray["paymentList"][0]['transactionID'];
                $upiInfo['qrHost'] = $dataArray["paymentList"][0]['lastFourDigits'];
            }
            
            $AUMdata['cashInfo'] = $cashInfo;
            $AUMdata['cardInfo'] = $cardInfo;
            $AUMdata['chequeInfo'] = $chequeInfo;
            $AUMdata['upiInfo'] = $upiInfo;
            $AUMPaymentDetail = Municipal::postAUMBillPayment($AUMdata,$chainID);
            $responseArray['AUMPaymentRequest'] = $AUMdata;
            $responseArray['AUMPaymentResponse'] = $AUMPaymentDetail;
            $dataArray['municipalTransactionID'] = $AUMdata['transactionID'];
            
        }

        // radius payment

        if(!empty($storeDetail) && $storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'RADIUS'){
            // Municipal::saveOrderJson($dataArray, $chainID);
            $radiusData = array();
            $radiusInfo = convertToArray(json_decode($dataArray['radiusInfo']));
            $radiusData['storeID'] = $radiusInfo['storeID'];
            $radiusData['siteID'] = $radiusInfo['siteID'];
            $radiusData['customerID'] = $radiusInfo['customerID'];
            $radiusData['transactionID'] = $dataArray['orderID'];
            $radiusData['amount'] = $dataArray["paymentList"][0]['amount'];
            $radiusResponse = Municipal::postRadiusPayment($radiusData,$chainID);
            if($radiusResponse['status'] == false){
                return $radiusResponse;
            }
            $dataArray['municipalTransactionID'] = $radiusData['transactionID'];
            
        }

        // municiapl type PRYAGRAJ
    
        if(!empty($storeDetail) && $storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'PRYAGRAJ'){
            // Municipal::saveOrderJson($dataArray, $chainID);
            $pryagrajData = array();
            $posDate = date('Ymd',strtotime($dataArray['posDate']));
            $pryagrajData['storeID'] = $storeID;
            $pryagrajData['billIdentifier'] = $dataArray['billIdentifier'];
            $pryagrajData['customerMobile'] = $dataArray['customers'][0]['phone'];
            $pryagrajData['amount'] = floatval($dataArray['netBill'])*100;
            $deviceID = $dataArray['deviceID'];
            
            $uniqueString = $dataArray['orderID'] . $pryagrajData['billIdentifier'] . $pryagrajData['amount'];
            $hash = md5($uniqueString);
            $transactionID = intval(substr(preg_replace('/[^0-9]/', '', $hash), 0, 18));
            if ($transactionID > PHP_INT_MAX) {
                // fallback
                $transactionID = intval(preg_replace('/[^0-9]/', '', $pryagrajData['billIdentifier'] . $pryagrajData['amount']));
            }
            
            $pryagrajData['transactionID'] = $transactionID;
            $pryagrajData['amount'] = $dataArray["paymentList"][0]['amount'] * 100;
            list($paymentType1,$paymentType2) =  explode('_',$dataArray["paymentList"][0]['paymentType']);
            $pryagrajData['paymentMode'] = $paymentType2;
            $pryagrajData['service'] = $dataArray['productsList'][0]['sku'];
            $pryagrajData['serviceType'] = $dataArray['service'];
            // $cardInfo = array();
            // $chequeInfo = array();
            // $cashInfo = array();
            $cashInfo = null;
            $cardInfo = null;
            $chequeInfo = null;
            $upiInfo = null;
            $voucherModeInfo = null;
            $walletModeInfo = null;
            $sodexoModeInfo = null;


            if($paymentType2 == 'CASH'){
                $cashInfo['cashAmount'] = $dataArray["paymentList"][0]['amount'] * 100;
            }
            if($paymentType2 == 'CHEQUE'){
                $chequeInfo['chequeAmount'] = $dataArray["paymentList"][0]['amount'] * 100;
                $chequeInfo['chequeNumber'] = (int)$dataArray["paymentList"][0]['transactionID'];         
                $chequeInfo['chequeMicr'] = (int)$dataArray["paymentList"][0]['transactionID'];
                $chequeInfo['chequeDate'] = date('dmY',strtotime($dataArray["paymentList"][0]['posDate']));
                $chequeInfo['chequeBank'] = $dataArray["paymentList"][0]['cardHolderName'];
                
            }
            if($paymentType2 == 'CARD'){
                $cardInfo['cardAmount'] = $dataArray["paymentList"][0]['amount'] * 100;
                $cardInfo['cardApprovalCode'] = $dataArray["paymentList"][0]['transactionID'];
                $cardInfo['cardMaskedNumber'] = date('d-m-Y',strtotime($dataArray["paymentList"][0]['posDate']));          
                $cardInfo['cardTid'] = (isset($dataArray["paymentList"][0]['approvalCode']) && !empty($dataArray["paymentList"][0]['approvalCode'])) ? $dataArray["paymentList"][0]['approvalCode'] : $cardInfo['cardApprovalCode'];
                $cardInfo['cardAcquirer'] = $cardInfo['cardTid'];
                $cardInfo['cardAcquirerMid'] = $cardInfo['cardTid'];
                $cardInfo['cardHolderName'] = $cardInfo['cardTid'];
                
            }

            if($paymentType2 == 'UPI'){
                $upiInfo['qrAmount'] = $dataArray["paymentList"][0]['amount'] * 100;
                $upiInfo['qrTransactionId'] = (int)$dataArray["paymentList"][0]['transactionID'];
                $upiInfo['qrHost'] = $dataArray["paymentList"][0]['lastFourDigits'];
            }
            
            if($paymentType2 == 'VOUCHER'){
                $voucherModeInfo['voucherNumber'] = $dataArray["paymentList"][0]['transactionID'];
            }
            if($paymentType2 == 'WALLET'){
                $walletModeInfo['walletTxnAmount'] = $dataArray["paymentList"][0]['amount'] * 100;
                $walletModeInfo['walletMid'] = $dataArray["paymentList"][0]['transactionID'];
                $walletModeInfo['walletMobileNumber'] = $dataArray["paymentList"][0]['userName'];
                $walletModeInfo['walletRrn'] = $dataArray["paymentList"][0]['userName'];
                $walletModeInfo['walletTid'] = $dataArray["paymentList"][0]['userName'];
                $walletModeInfo['walletRrn'] = $dataArray["paymentList"][0]['userName'];
                $walletModeInfo['walletMobileNumber'] = $dataArray["paymentList"][0]['userName'];
                $walletModeInfo['walletCardHolderName'] = $dataArray["paymentList"][0]['approvalCode'];
                $walletModeInfo['walletMaskPan'] = date('d-m-Y',strtotime($dataArray["paymentList"][0]['posDate']));
            }
            
            $pryagrajData['cashInfo'] = $cashInfo;
            $pryagrajData['cardInfo'] = $cardInfo;
            $pryagrajData['chequeInfo'] = $chequeInfo;
            $pryagrajData['qrBasedModeInfo'] = $upiInfo;
            $pryagrajData['voucherModeInfo'] = $voucherModeInfo;
            $pryagrajData['walletModeInfo'] = $walletModeInfo;
            $pryagrajData['sodexoModeInfo'] = $sodexoModeInfo;
            $pryagrajData['mobileNo'] = $dataArray['customers'][0]['phone'];
            $pryagrajData['userName'] = $dataArray['billingUsername'];
            $pryagrajData['deviceID'] = $dataArray['deviceID'];
            $pryagrajData['orderPayload'] = $dataArray;
            $pryagrajPaymentDetail = Municipal::postPryagrajBillPayment($pryagrajData,$chainID);
            $responseArray['pryagrajPaymentRequest'] = $pryagrajData;
            $responseArray['pryagrajPaymentResponse'] = $pryagrajPaymentDetail;
            if($pryagrajPaymentDetail['status'] == false){
                return $pryagrajPaymentDetail;
            }
            $PLtransactionID = (int)preg_replace('/[^0-9]/', '', $pryagrajData['transactionID']);
            $dataArray['municipalTransactionID'] = $PLtransactionID;
            
        }

        // if municipal type SULTANPUR
        if(!empty($storeDetail) && $storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'SULTANPUR'){
            // Municipal::saveOrderJson($dataArray, $chainID);
            $sultanpurData = array();
            $sultanpurMunicipalData = json_decode($dataArray['sultanpurMunicipal'],true);
            $sultanpurData['storeID'] = $storeID;
            $sultanpurData['propertyUniqueId'] = $sultanpurMunicipalData['property_id'];
            //$sultanpurData['discount'] = $sultanpurMunicipalData['discount'];
            $sultanpurData['transactionAmount'] = round($dataArray['grossBill'], 2);
            $sultanpurData['paybleAmount'] = round($sultanpurMunicipalData['due_amount'], 2);
            $paymentType = $dataArray['paymentList'][0]['paymentType'];
            list($paymentType1,$paymentType2) =  explode('_',$paymentType);
            $paymentInfo = array();
            $paymentInfo['paymentMode'] = $paymentType2;
            $paymentInfo['cardlastfourdigits'] = $dataArray['paymentList'][0]['lastFourDigits'];
            $paymentInfo['cardReferenceId'] = $dataArray['paymentList'][0]['uniqueReferenceID'];
            $paymentInfo['bankRRN'] = $dataArray['paymentList'][0]['bankRRN'];
            $sultanpurData['plTransactionId'] = $dataArray['orderID'].'-'.$sultanpurData['propertyUniqueId'];

            $paymentInfo['paymentType'] = $sultanpurMunicipalData['paymentType'];
            if($paymentType2 == 'CHEQUE' || $paymentType2 == 'DD'){
                $paymentInfo['instrumentNumber'] = $dataArray["paymentList"][0]['transactionID'];
                $paymentInfo['instrumentDate'] = $dataArray["paymentList"][0]['posDate'];
            }
            $sultanpurData['paymentInfo'] = $paymentInfo;
            $sultanpurData['paymentMode'] = $paymentType2;
            $sultanpurData['userName'] = $dataArray['billingUsername'];
            $sultanpurData['employeeName'] = $dataArray['customers'][0]['phone'];
            $sultanpurData['deviceID'] = $dataArray['deviceID'];
            $sultanpurData['orderID'] = $dataArray['orderID'];
            $sultanpurData['houseNo'] = $sultanpurMunicipalData['house_id'];
            $sultanpurData['wardNo'] = $sultanpurMunicipalData['ward_number'];
            $sultanpurData['finYear'] = $sultanpurMunicipalData['session'];
            $sultanpurData['mobile'] = $dataArray['customers'][0]['phone'];
            $sultanpurData['email'] = $dataArray['customers'][0]['email'];
            $sultanpurPaymentResponse = Municipal::initiatePaymentForSultanpurMunicipal($sultanpurData,$chainID);
            if($sultanpurPaymentResponse['status'] == false){
                return $sultanpurPaymentResponse;
            }
            $responseArray['sultanpurPaymentResponse'] = $sultanpurPaymentResponse;
            
        }
        
        // Municipal type DEHRADUN
        if(!empty($storeDetail) && $storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'DEHRADUN'){
            // Municipal::saveOrderJson($dataArray, $chainID);
            
            $dehradunMunicipalBillDetails = convertToArray(json_decode($dataArray['dehradunMunicipalBillDetails']));
            
            $dehradunData = array();
            $dehradunData['storeID'] = $storeID;
            $dehradunData['instrumentNumber'] = $dataArray['orderID'];
            $posDate = date('Ymd',strtotime($dataArray['posDate']));
            $dehradunData['instrumentDate'] = $posDate;
            $dehradunData['billId'] = $dehradunMunicipalBillDetails['billID'];
            $dehradunData['dehradunAuthToken'] = $dehradunMunicipalBillDetails['dehradunAuthToken'];
            $dehradunData['mobileNumber'] = $dataArray['customers'][0]['phone'];
            $dehradunData['totalAmountPaid'] = floatval($dataArray['netBill']);
            $transactionID = $posDate.$storeID.$dataArray['invoiceNumber'];
            $dehradunData['transactionID'] = $transactionID;
            $dehradunData['totalAmountPaid'] = $dataArray["paymentList"][0]['amount'];
            list($paymentType1,$paymentType2) =  explode('_',$dataArray["paymentList"][0]['paymentType']);
            $dehradunData['paymentMode'] = $paymentType2;
            $dehradunData['service'] = $dataArray['productsList'][0]['sku'];
            $firstName = $dataArray['customers'][0]['firstName'];
            $middleName = $dataArray['customers'][0]["middleName"];
            $lastName = $dataArray['customers'][0]['lastName'];

            if ($middleName == NULL || $middleName == "") {
                $name = $firstName." ".$lastName;
            }else{
                $name = $firstName." ".$middleName." ".$lastName;   
            }
            $dehradunData['paidBy'] = $name;
            // $cardInfo = array();
            // $chequeInfo = array();
            // $cashInfo = array();
            $cashInfo = null;
            $cardInfo = null;
            $chequeInfo = null;
            $upiInfo = null;
            $voucherModeInfo = null;
            $walletModeInfo = null;
            $sodexoModeInfo = null;
            $dehradunData['mobileNo'] = $dataArray['customers'][0]['phone'];
            $dehradunPaymentDetail = Municipal::makeDeMunBillPayment($dehradunData,$chainID);
            $responseArray['dehradunPaymentRequest'] = $dehradunData;
            $responseArray['dehradunPaymentResponse'] = $dehradunPaymentDetail;
            $responseArray['dehradunPaymentRequest']['propertyId'] = $dehradunMunicipalBillDetails['propertyId'];
            $responseArray['dehradunPaymentRequest']['tenantId'] = $dehradunMunicipalBillDetails['tenantId'];
            if($dehradunPaymentDetail['status'] == false){
                return $dehradunPaymentDetail;
            }
            $dataArray['municipalTransactionID'] = $dehradunData['transactionID'];

        }

        // if municipal type SOLAN
        if(!empty($storeDetail) && $storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'SOLAN'){
            $SOLANMunicipalData = json_decode($dataArray['solanMunicipalJson'], true);

            $solanData = array();
            $posDate = date('Ymd',strtotime($dataArray['posDate']));
            $solanData['storeID'] = $storeID;
            $solanData['billIdentifier'] = $SOLANMunicipalData['bill_id'];
            $solanData['customerMobile'] = $dataArray['customers'][0]['phone'];
            $solanData['amount'] = floatval($dataArray['netBill']);
            //$transactionID = $posDate.$storeID.$dataArray['invoiceNumber'];
            $transactionID = $dataArray['orderID'] . $solanData['billIdentifier'] . $solanData['amount'];
            $solanData['transactionID'] = $transactionID;
            $solanData['amount'] = $dataArray["paymentList"][0]['amount'];
            list($paymentType1,$paymentType2) =  explode('_',$dataArray["paymentList"][0]['paymentType']);
            $solanData['paymentMode'] = $paymentType2;
            $paymentInfo = array();
            $paymentInfo['paymentMode'] = $paymentType2;
            $paymentInfo['cardlastfourdigits'] = $dataArray['paymentList'][0]['lastFourDigits'];
            $paymentInfo['cardReferenceId'] = $dataArray['paymentList'][0]['uniqueReferenceID'];
            $paymentInfo['bankRRN'] = $dataArray['paymentList'][0]['bankRRN'];
            $paymentInfo['paymentType'] = $bareillyMunicipalData['paymentType'];
            $paymentInfo['instrumentNumber'] = "";
            $paymentInfo['instrumentDate'] = "";
            if($paymentType2 == 'CHEQUE' || $paymentType2 == 'DD'){
                $paymentInfo['instrumentNumber'] = $dataArray["paymentList"][0]['transactionID'];
                $paymentInfo['instrumentDate'] = $dataArray["paymentList"][0]['posDate'];
            }
            $solanData['paymentInfo'] = $paymentInfo;
            $solanData['mobileNo'] = $dataArray['customers'][0]['phone'];
            $solanData['userName'] = $dataArray['billingUsername'];
            $solanData['deviceID'] = $dataArray['deviceID'];
            $solanPaymentDetail = Municipal::initiatePaymentForSolanMunicipal($solanData,$chainID);
            $responseArray['solanPaymentRequest'] = $solanData;
            $responseArray['solanPaymentResponse'] = $solanPaymentDetail;
            if($solanPaymentDetail['status'] == false){
                return $solanPaymentDetail;
            }
            $dataArray['municipalTransactionID'] = $solanData['transactionID'];
        }

        // if municipal type firozabad

        if(!empty($storeDetail) && $storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'FIROZABAD'){
            // Municipal::saveOrderJson($dataArray, $chainID);
            $firozabadData = array();
            $municipalData = convertToArray(json_decode($dataArray['firozabadMunicipal']));
            $posDate = date('Ymd',strtotime($dataArray['posDate']));
            $firozabadData['storeID'] = $storeID;
            $firozabadData['firozabadMunicipal'] = $municipalData;
            $firozabadData['billIdentifier'] = $municipalData['billIdentifier'];
            $firozabadData['customerMobile'] = $dataArray['customers'][0]['phone'];
            $firozabadData['amount'] = floatval($dataArray['netBill']);
            $deviceID = $dataArray['deviceID'];
            
            $uniqueString = $dataArray['orderID'] . $firozabadData['billIdentifier'] . $firozabadData['amount'];
            $hash = md5($uniqueString);
            $transactionID = intval(substr(preg_replace('/[^0-9]/', '', $hash), 0, 18));
            if ($transactionID > PHP_INT_MAX) {
                // fallback
                $transactionID = intval(preg_replace('/[^0-9]/', '', $firozabadData['billIdentifier'] . $firozabadData['amount']));
            }
            
            //$firozabadData['transactionID'] = $transactionID;
            $firozabadData['transactionID'] = $dataArray['orderID'];
            $firozabadData['amount'] = $dataArray["paymentList"][0]['amount'];
            list($paymentType1,$paymentType2) =  explode('_',$dataArray["paymentList"][0]['paymentType']);
            $firozabadData['paymentMode'] = $paymentType2;
            $firozabadData['service'] = $dataArray['productsList'][0]['sku'];
            $firozabadData['serviceType'] = $dataArray['service'];
            // $cardInfo = array();
            // $chequeInfo = array();
            // $cashInfo = array();
            $cashInfo = null;
            $cardInfo = null;
            $chequeInfo = null;
            $upiInfo = null;
            $voucherModeInfo = null;
            $walletModeInfo = null;
            $sodexoModeInfo = null;


            if($paymentType2 == 'CASH'){
                $cashInfo['cashAmount'] = $dataArray["paymentList"][0]['amount'];
            }
            if($paymentType2 == 'CHEQUE'){
                $chequeInfo['chequeAmount'] = $dataArray["paymentList"][0]['amount'];
                $chequeInfo['chequeNumber'] = (int)$dataArray["paymentList"][0]['transactionID'];         
                $chequeInfo['chequeMicr'] = (int)$dataArray["paymentList"][0]['transactionID'];
                $chequeInfo['chequeDate'] = date('dmY',strtotime($dataArray["paymentList"][0]['posDate']));
                $chequeInfo['chequeBank'] = $dataArray["paymentList"][0]['cardHolderName'];
                
            }
            if($paymentType2 == 'CARD'){
                $cardInfo['cardAmount'] = $dataArray["paymentList"][0]['amount'];
                $cardInfo['cardApprovalCode'] = $dataArray["paymentList"][0]['transactionID'];
                $cardInfo['cardMaskedNumber'] = date('d-m-Y',strtotime($dataArray["paymentList"][0]['posDate']));          
                $cardInfo['cardTid'] = (isset($dataArray["paymentList"][0]['approvalCode']) && !empty($dataArray["paymentList"][0]['approvalCode'])) ? $dataArray["paymentList"][0]['approvalCode'] : $cardInfo['cardApprovalCode'];
                $cardInfo['cardAcquirer'] = $cardInfo['cardTid'];
                $cardInfo['cardAcquirerMid'] = $cardInfo['cardTid'];
                $cardInfo['cardHolderName'] = $cardInfo['cardTid'];
                
            }

            if($paymentType2 == 'UPI'){
                $upiInfo['qrAmount'] = $dataArray["paymentList"][0]['amount'];
                $upiInfo['qrTransactionId'] = (int)$dataArray["paymentList"][0]['transactionID'];
                $upiInfo['qrHost'] = $dataArray["paymentList"][0]['lastFourDigits'];
            }
            
            if($paymentType2 == 'VOUCHER'){
                $voucherModeInfo['voucherNumber'] = $dataArray["paymentList"][0]['transactionID'];
            }
            if($paymentType2 == 'WALLET'){
                $walletModeInfo['walletTxnAmount'] = $dataArray["paymentList"][0]['amount'];
                $walletModeInfo['walletMid'] = $dataArray["paymentList"][0]['transactionID'];
                $walletModeInfo['walletMobileNumber'] = $dataArray["paymentList"][0]['userName'];
                $walletModeInfo['walletRrn'] = $dataArray["paymentList"][0]['userName'];
                $walletModeInfo['walletTid'] = $dataArray["paymentList"][0]['userName'];
                $walletModeInfo['walletRrn'] = $dataArray["paymentList"][0]['userName'];
                $walletModeInfo['walletMobileNumber'] = $dataArray["paymentList"][0]['userName'];
                $walletModeInfo['walletCardHolderName'] = $dataArray["paymentList"][0]['approvalCode'];
                $walletModeInfo['walletMaskPan'] = date('d-m-Y',strtotime($dataArray["paymentList"][0]['posDate']));
            }
            
            $firozabadData['cashInfo'] = $cashInfo;
            $firozabadData['cardInfo'] = $cardInfo;
            $firozabadData['chequeInfo'] = $chequeInfo;
            $firozabadData['qrBasedModeInfo'] = $upiInfo;
            $firozabadData['voucherModeInfo'] = $voucherModeInfo;
            $firozabadData['walletModeInfo'] = $walletModeInfo;
            $firozabadData['sodexoModeInfo'] = $sodexoModeInfo;
            $firozabadData['mobileNo'] = $dataArray['customers'][0]['phone'];
            $firozabadData['userName'] = $dataArray['billingUsername'];
            $firozabadData['deviceID'] = $dataArray['deviceID'];
            $firozabadData['orderPayload'] = $dataArray;
            $firozabadPaymentDetail = Municipal::initiatePaymentForFirozabadMun($firozabadData,$chainID);
            $responseArray['firozabadPaymentRequest'] = $firozabadData;
            $responseArray['firozabadPaymentResponse'] = $firozabadPaymentDetail;
            if($firozabadPaymentDetail['status'] == false){
                return $firozabadPaymentDetail;
            }
            $dataArray['municipalTransactionID'] = $firozabadData['transactionID'];
        }
        // if municipal type Bareilly
        if(!empty($storeDetail) && $storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'BAREILLY'){
            // Municipal::saveOrderJson($dataArray, $chainID);
            $bareillyData = array();
            $bareillyMunicipalData = json_decode($dataArray['bareillyMunicipalJson'],true);
            $bareillyData['storeID'] = $storeID;
            $bareillyData['propertyUniqueId'] = $bareillyMunicipalData['propertyUniqueId'];
            $bareillyData['discount'] = $bareillyMunicipalData['discount'];
            $bareillyData['plTransactionId'] = $dataArray['paymentList'][0]['transactionID'];
            $bareillyData['transactionAmount'] = floatval($dataArray['grossBill']);
            $paymentType = $dataArray['paymentList'][0]['paymentType'];
            list($paymentType1,$paymentType2) =  explode('_',$paymentType);
            $paymentInfo = array();
            $paymentInfo['paymentMode'] = $paymentType2;
            $paymentInfo['cardlastfourdigits'] = $dataArray['paymentList'][0]['lastFourDigits'];
            $paymentInfo['cardReferenceId'] = $dataArray['paymentList'][0]['uniqueReferenceID'];
            $paymentInfo['bankRRN'] = $dataArray['paymentList'][0]['bankRRN'];
            $paymentInfo['paymentType'] = $bareillyMunicipalData['paymentType'];
            if($paymentType2 == 'CHEQUE' || $paymentType2 == 'DD'){
                $paymentInfo['instrumentNumber'] = $dataArray["paymentList"][0]['transactionID'];
                $paymentInfo['instrumentDate'] = $dataArray["paymentList"][0]['posDate'];
            }
            $bareillyData['paymentInfo'] = $paymentInfo;
            $bareillyData['userName'] = $dataArray['billingUsername'];
            $bareillyData['employeeName'] = $dataArray['customers'][0]['phone'];
            $bareillyData['deviceID'] = $dataArray['deviceID'];
            $bareillyData['orderID'] = $dataArray['orderID'];


            $bareillyPaymentResponse = Municipal::initiatePaymentForBareillyMunicipal($bareillyData,$chainID);
            if($bareillyPaymentResponse['status'] == false){
                return $response;
            }
            $responseArray['bareillyPaymentRequest'] = $bareillyPaymentResponse;
            $dataArray['municipalTransactionID'] = $bareillyData['plTransactionId'];
            
        }

        // if municipal type ayodhya
        if(!empty($storeDetail) && $storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'AYODHYA'){
            // Municipal::saveOrderJson($dataArray, $chainID);
            $ayodhyaData = array();
            $municipalData = convertToArray(json_decode($dataArray['ayodhyaMunicipalJson']));
            
            $posDate = date('Ymd',strtotime($dataArray['posDate']));
            $ayodhyaData['storeID'] = $storeID;
            $ayodhyaData['ayodhyaMunicipal'] = $municipalData;
            $ayodhyaData['propertyID'] = $municipalData['Property_Id'];
            $ayodhyaData['empID'] = $municipalData['empID'];
            $ayodhyaData['userName'] = $municipalData['billingUsername'];
            $ayodhyaData['customerMobileNo'] = $dataArray['customers'][0]['phone'];
            $ayodhyaData['customerName'] = $dataArray['customers'][0]['firstName'].' '.$dataArray['customers'][0]['lastName'];
            $ayodhyaData['amount'] = floatval($dataArray['netBill']);
            list($paymentType1,$paymentType2) =  explode('_',$dataArray["paymentList"][0]['paymentType']);
            $ayodhyaData['paymentMode'] = $paymentType2;
            $ayodhyaData['orderID'] = $dataArray['orderID'].'-'.$municipalData['Property_No'];
            $deviceID = $dataArray['deviceID'];
            $ayodhyaData['transactionID'] = $dataArray['orderID'];
            $ayodhyaData['amount'] = $dataArray["paymentList"][0]['amount'];
            list($paymentType1,$paymentType2) =  explode('_',$dataArray["paymentList"][0]['paymentType']);
            $ayodhyaData['paymentMode'] = $paymentType2;
            $ayodhyaData['mobileNo'] = $dataArray['customers'][0]['phone'];
            $ayodhyaData['userName'] = $dataArray['billingUsername'];
            $ayodhyaData['deviceID'] = $dataArray['deviceID'];
            $ayodhyaData['token'] = $dataArray['ayodhyaAccessToken'];
            $ayodhyaData['orderPayload'] = $dataArray;
            $ayodhyaPaymentDetail = AyodhyaMunicipal::InitiatePaymentForAyodhyaMunBill($ayodhyaData,$chainID);
            $responseArray['ayodhyaPaymentRequest'] = $ayodhyaData;
            $responseArray['ayodhyaPaymentResponse'] = $ayodhyaPaymentDetail;
            if($ayodhyaPaymentDetail['status'] == false){
                return $ayodhyaPaymentDetail;
            }
            $dataArray['municipalTransactionID'] = $ayodhyaData['transactionID'];
        }

        // if municipal type Farrukhabad
        if(!empty($storeDetail) && $storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'FARRUKHABAD'){
            
            $farrukhabadData = array();
            $farrukhabadMunicipalData = json_decode($dataArray['farrukhabadMunicipalJson'],true);
            $farrukhabadData['storeID'] = $storeID;
            $farrukhabadData['propertyUniqueId'] = $farrukhabadMunicipalData['UNIQUE_ID'];
            $farrukhabadData['plTransactionId'] = $dataArray['orderID'].'-'.$farrukhabadMunicipalData['UNIQUE_ID'];
            $farrukhabadData['orderID'] = $dataArray['orderID'].'-'.$farrukhabadMunicipalData['UNIQUE_ID'];
            $farrukhabadData['qbOrderID'] = $dataArray['orderID'];
            $farrukhabadData['transactionAmount'] = floatval($dataArray['grossBill']);
            $farrukhabadData['totalAmount'] = floatval($farrukhabadMunicipalData['TOTAL_TAX']);
            $paymentType = $dataArray['paymentList'][0]['paymentType'];
            list($paymentType1,$paymentType2) =  explode('_',$paymentType);
            $paymentInfo = array();
            $paymentInfo['paymentMode'] = $paymentType2;
            $paymentInfo['cardlastfourdigits'] = $dataArray['paymentList'][0]['lastFourDigits'];
            $paymentInfo['cardReferenceId'] = $dataArray['paymentList'][0]['uniqueReferenceID'];
            $paymentInfo['bankRRN'] = $dataArray['paymentList'][0]['bankRRN'];
            $paymentInfo['paymentType'] = $dataArray['paymentList'][0]['paymentType'];
            $paymentInfo['instrumentNumber'] = $dataArray["paymentList"][0]['transactionID'];
            $paymentInfo['instrumentDate'] = $dataArray["paymentList"][0]['posDate'];
            /*if($paymentType2 == 'CHEQUE' || $paymentType2 == 'DD'){
                $paymentInfo['instrumentNumber'] = $dataArray["paymentList"][0]['transactionID'];
                $paymentInfo['instrumentDate'] = $dataArray["paymentList"][0]['posDate'];
            }*/
            $farrukhabadData['paymentInfo'] = $paymentInfo;
            $farrukhabadData['userName'] = $dataArray['billingUsername'];
            $farrukhabadData['employeeName'] = $farrukhabadMunicipalData['OCCUPIER_N'];
            $farrukhabadData['deviceID'] = $dataArray['deviceID'];
            $farrukhabadData['serialNumber'] = $dataArray['serialNumber'];
            $farrukhabadData['municipalID'] = $farrukhabadMunicipalData['i_d'];
            $farrukhabadData['municipalTableName'] = $farrukhabadMunicipalData['table'];
            $farrukhabadData['municipalOccupierName'] = $farrukhabadMunicipalData['OCCUPIER_N'];
            $farrukhabadData['totalTax'] = $farrukhabadMunicipalData['total_tax_paid'];
            
            $farrukhabadPaymentResponse = farrukhabadMunicipal::InitiatePaymentForFarrukhabadMunBill($farrukhabadData,$chainID);
            if($farrukhabadPaymentResponse['status'] == false){
                return $farrukhabadPaymentResponse;
            }
            $responseArray['farrukhabadPaymentResponse'] = $farrukhabadPaymentResponse;
            $dataArray['municipalTransactionID'] = $farrukhabadData['plTransactionId'];
            
        }

        // Municipal integration end
        END_OF_MUNICIPAL_SECTION:

        // integration type AVIS
        if(!empty($getChainSettings) && strtoupper($getChainSettings[0]['merchantIntegrationModule']) == 'AVIS'){
            if(count($dataArray['productList']) > 1){
                $status['status'] = false;
                $status['message'] = "AVIS integration supports only one product at a time";
                return $status;
            }

            $avisData = [
                'vehicleNo' => "",
                'totalDays' => "",
                'totalHour' => "",
                'totalMinute' => "",
                'totalDistance' => "",
                'tollCharges' => "",
                'otherCharges' => ""
            ];
            foreach ($dataArray['productsList'][0]['attributes'] as $attribute) {
                if (isset($attribute['name']) && $attribute['name'] == "Vehicle Number") {
                    $avisData['vehicleNo'] = $attribute['attibuteValue'];
                }
                if (isset($attribute['name']) && $attribute['name'] == "Day") {
                    $avisData['totalDays'] = $attribute['attibuteValue'];
                }
                if (isset($attribute['name']) && $attribute['name'] == "Hour") {
                    $avisData['totalHour'] = $attribute['attibuteValue'];
                }
                if (isset($attribute['name']) && $attribute['name'] == "Minute") {
                    $avisData['totalMinute'] = $attribute['attibuteValue'];
                }
                if (isset($attribute['name']) && $attribute['name'] == "Distance") {
                    $avisData['totalDistance'] = $attribute['attibuteValue'];
                }
                if (isset($attribute['name']) && $attribute['name'] == "Toll Charges") {
                    $avisData['tollCharges'] = $attribute['attibuteValue'];
                }
                if (isset($attribute['name']) && $attribute['name'] == "Other Charges") {
                    $avisData['otherCharges'] = $attribute['attibuteValue'];
                }
            }
            $avisData['storeID'] = $storeID;
            $avisData['bookingID'] = $dataArray['rentalNumber'];
            $avisData['billNo'] = $dataArray['orderID'];
            $ordertime = $dataArray['orderCreationTimeLocal'];
            $avisData['billTime'] = $ordertime;
            $avisData['chauffeurPhone'] = $dataArray['billingUsername'];
            $avisData['bookingType'] = $dataArray['productsList'][0]['subCategoryName'];
            $avisData['applicableRate'] = "";
            $avisData['taxPercentage'] = $dataArray['taxes'];
            $avisData['netAmount'] = floatval($dataArray['netBill']);
            $avisData['totalTaxamount'] = $dataArray['taxes'];
            $avisData['Rounding'] = $dataArray['rounding'];
            $avisData['GrossValue'] = $dataArray['grossBill'];
            $avisData['paymentMode'] = $dataArray['paymentList'][0]['paymentType'];//
            $avisData['Amount'] = $dataArray["payableAmount"];
            $avisData['transactionID'] = $dataArray['paymentList'][0]['transactionID'];
            $avisData['bankRRN'] = $dataArray['paymentList'][0]['bankRRN'];
            $avisData['refID'] = $dataArray['paymentList'][0]['uniqueReferenceID'];
            $avisData['totalAmountPaid'] = $dataArray["paymentList"][0]['amount'];
            // list($paymentType1,$paymentType2) =  explode('_',$dataArray["paymentList"][0]['paymentType']);
            // $avisData['paymentMode'] = $paymentType2;
            // $avisData['service'] = $dataArray['productsList'][0]['sku'];
            // $avisData['mobileNo'] = $dataArray['customers'][0]['phone'];
            // dd($avisData);
            $avisPaymentDetail = Avis::makeAvisPayment($avisData,$chainID);
            $responseArray['AVISPaymentResponse'] = $avisPaymentDetail;
            // $responseArray['dehradunPaymentRequest'] = $dehradunData;
            // $responseArray['dehradunPaymentResponse'] = $dehradunPaymentDetail;
            // $responseArray['dehradunPaymentRequest']['propertyId'] = $dehradunMunicipalBillDetails['propertyId'];
            // $responseArray['dehradunPaymentRequest']['tenantId'] = $dehradunMunicipalBillDetails['tenantId'];
            // if($dehradunPaymentDetail['status'] == false){
            //     return $dehradunPaymentDetail;
            // }
        }
		if ($enableLogging) {
			Log::info("Order processing - IRN: " . json_encode([
				'chainID' => $chainID,
				'storeID' => $storeID,
				'orderID' => $dataArray["orderID"],
			]));
		}

        START_OF_IRN_GENERATION:

        if ($enableIRN == 0 && $enableGST == 0) {
            goto END_OF_IRN_GENERATION;
        }

        // generate IRN
        $IRNDetail  = array();
        $ewayDetail = array();
        $deliveryPlatform = $dataArray['deliveryPlatform'];
        $blockedDeliveryPlatformsForIRN = ["UNICOMMERCE", "EASYECOM", "SHOPIFY"];
        if(!in_array(strtoupper($deliveryPlatform), $blockedDeliveryPlatformsForIRN) || empty($deliveryPlatform) || $deliveryPlatform == null)
        {
            if($enableIRN == 1 && !empty($dataArray["customers"]) && $dataArray["customers"][0]['gstNumber'] != '' && $dataArray['isNoInvoiceSale'] == 0){
                $IRNData = $dataArray;
                $IRNData['storeID'] = $storeID;
                $IRNDetail = GST::generateIRN($IRNData,$chainID);
                if($IRNDetail['status'] == false){
                    $status['status'] = false;
                    $status["message"] = "Error in IRN ".$IRNDetail['message'];
                    $status['errorResponse'] = $IRNDetail;
                    if (!isset($status['errorResponse']['message'])) {
                        $errorMessage = "Error in IRN ";
                        if (isset($IRNDetail["validationError"]) && isset($IRNDetail["validationError"]["message"])) {
                            $errorMessage .= $IRNDetail["validationError"]["message"];
                        }
                        $status['errorResponse']['message'] = $errorMessage;
                        if ($IRNDetail['message'] == "") {
                            $status["message"] = $errorMessage;
                        }
                    }
                    return $status;
                } else{
                    //$dataArray['IRNNumber'] = $IRNDetail['IRN'];
                    if(isset($IRNDetail['ewaybillsNo']) && $IRNDetail['ewaybillsNo']!=''){
                    // $dataArray['ewayBillNo'] = $IRNDetail['ewaybillsNo'];
                    }
                }
            }
            if($enableGST==1 && $enableIRN!=1 && $dataArray['isNoInvoiceSale'] == 0){
                if(isset($dataArray['ewayBills']) && !empty($dataArray['ewayBills'])){
                    $ewayBillData = $dataArray['ewayBills'];
                    $ewayBillData['storeID'] = $storeID;
                    $ewayDetail = GST::generateEwayBillWithoutIRN($chainID,$ewayBillData);
                    if($ewayDetail['status'] == false){
                        $status['status'] = false;
                        $status["message"] = "Error in IRN ".$IRNDetail['message'];
                        $status['errorResponse'] = $IRNDetail;
                        if (!isset($status['errorResponse']['message'])) {
                            $errorMessage = "Error in IRN ";
                            if (isset($IRNDetail["validationError"]) && isset($IRNDetail["validationError"]["message"])) {
                                $errorMessage .= $IRNDetail["validationError"]["message"];
                            }
                            $status['errorResponse']['message'] = $errorMessage;
                            if ($IRNDetail['message'] == "") {
                                $status["message"] = $errorMessage;
                            }
                        }
                        return $status;
                    } else{
                        //$dataArray['IRNNumber'] = $IRNDetail['IRN'];
                        if(isset($IRNDetail['ewaybillsNo']) && $IRNDetail['ewaybillsNo']!=''){
                        // $dataArray['ewayBillNo'] = $IRNDetail['ewaybillsNo'];
                        }
                    }
                }

                if($enableGST==1 && $enableIRN!=1 && $dataArray['isNoInvoiceSale'] == 0){
                    if(isset($dataArray['ewayBills']) && !empty($dataArray['ewayBills'])){
                        $ewayBillData = $dataArray['ewayBills'];
                        $ewayBillData['storeID'] = $storeID;
                        $ewayDetail = GST::generateEwayBillWithoutIRN($chainID,$ewayBillData);
                        if($ewayDetail['status'] == false){
                            $status['status'] = false;
                            $message = $ewayDetail['message'];
                            $status['message'] = "error in E-way bill $message";
                            $status['errorResponse'] = $ewayDetail;
                            return $status;
                        }
                    }
                }
            }
        }
		if ($enableLogging) {
			Log::info("Order processing - customer: " . json_encode([
				'chainID' => $chainID,
				'storeID' => $storeID,
				'orderID' => $dataArray["orderID"],
			]));
		}

        END_OF_IRN_GENERATION:

        // Customers
        if (isset($dataArray["customers"])) {
            $status = OldOrder::customersOrder($storeID, $dataArray, $status);
            if($recordCustomerInfoOnOrder == 1){
                $customerOrderMapping = Customers::recordCustomerOrderMapping($storeID, $dataArray);
            }
            if(isset($status['customerAddressID']) && !empty($status['customerAddressID'])){
                $status['customerAddress'] = $status['customerAddressID'];
                unset($status['customerAddressID']);
            }
            $customer1 = $status["customers"];
            $isNewCustomer = $status["isNewCustomer"];
            $dataArray["finalCustomerID"] = $customer1;
            $dataArray["finalCustomerAccountID"] = $status["customerAccountID"];
            $dataArray["finalCustomerAccountName"] = "CustomerAccount".$customer1;

            $pointsExpiryDays = $settings['chain']['pointsExpiryDays'] ?? 0;
        }else{
            $customer1 = NULL;
        }

        if ($status["status"] == false) {
            $responseArray["orderCreationReport"] = $status;
            return $responseArray;
        }

        if ($customer1 > 0) {
            $responseArray["setLoyalty"] = 1;
        }else{
            $responseArray["setLoyalty"] = 0;
        }

        $responseArray["isNewCustomer"] = $isNewCustomer;

        // echo $hasCardPayment ;
        if (isset($dataArray["payments"])) {
            $countOf = count($dataArray["payments"]);
            for ($j = 0; $j < $countOf; $j++) {

            }
        }

        // Important variables to set
        $status["orderId"]              = $dataArray["orderID"];
        $status["billSettledTimeLocal"] = $dataArray["billSettledTimeLocal"];
        $status["timezone"]             = $dataArray["timezone"];

        // Check for isNoCharge
        $isNoCharge = 0;
        if (isset($dataArray["isNoCharge"])) {
            $isNoCharge = $dataArray["isNoCharge"];
        }
        $prodListArray = array();
        $productsList = $dataArray["productsList"];
        $countOf = count($productsList);
        $batchArray = array();
        $batchList = "";
        $prodBatchArray = array();
        $prodBatchVariantArray = array();
        $prodQuantArray = array();
        $prodBatchVariantQuantArray = array();
        $orderModArray = array();

        $getBilllingUserID = convertToArray(DB::table($usersTable)->where("userName",$dataArray["billingUsername"])->select("ID")->get());
        if (!empty($getBilllingUserID)) {
          $billingUserID = $getBilllingUserID[0]["ID"];
        }else{
          $billingUserID = -1;
        }

        if ($industry == "FOOD" || $enableModifier) {
          $chainModList = convertToArray(DB::select(DB::raw("SELECT A.modifierID,A.modifierName,B.productID,B.variantID,B.batchID FROM $modifiersTable AS A INNER JOIN $productPricesTable AS B ON A.modifierProductID = B.productID")));
        }else{
          $chainModList = array();
        }

        $tax_exempted_sales = 0;
        $taxable_sales_vat = 0;
        $orderTaxArray = array();

        /** fetch chain taxes start */
            $taxesTable = TableRegistry::getChainTable($chainID, 'taxes');
            $fetchChainTaxes = convertToArray(DB::table($taxesTable)->where('isActive',1)->get());
        /** fetch chain taxes end */
        // generate total tax on order array
        if (isset($dataArray["additionalCharges"])) {
            $additionalCharges["Additional Charges"] = $dataArray["additionalCharges"];
            $additonalChargeCount                    = count($additionalCharges["Additional Charges"]);

            $orderTaxArray = array();
            for ($j = 0; $j < $additonalChargeCount; $j++) {

                if (isset($additionalCharges["Additional Charges"][$j]["taxes"]) && !empty($additionalCharges["Additional Charges"][$j]["taxes"])) {
                  $orTaxCount  = count($additionalCharges["Additional Charges"][$j]["taxes"]);

                  for ($k=0; $k < $orTaxCount; $k++) {
                    $taxID = $additionalCharges["Additional Charges"][$j]["taxes"][$k]["taxID"];
                    $orTaxKey = array_search($taxID, array_column($orderTaxArray, 'taxID'));
                    if (is_int($orTaxKey)) {
                      $orderTaxArray[$orTaxKey]["taxValue"]+= $additionalCharges["Additional Charges"][$j]["taxes"][$k]["taxValue"];
                    }else{
                        $tempTaxArray = array();
                        $tempTaxArray["taxID"] = $taxID;
                        $tempTaxArray["taxName"] = $additionalCharges["Additional Charges"][$j]["taxes"][$k]["taxName"];
                        $tempTaxArray["taxPercentage"] = $additionalCharges["Additional Charges"][$j]["taxes"][$k]["taxPercentage"];

                        $tempTaxArray["taxValue"] = $additionalCharges["Additional Charges"][$j]["taxes"][$k]["taxValue"];
                        $tempTaxArray['accountID'] = $additionalCharges["Additional Charges"][$j]["taxes"][$k]["accountID"];
                        $tempTaxArray['accountName'] = $additionalCharges["Additional Charges"][$j]["taxes"][$k]["accountName"];

                        if($enableTaxFiling == 1){
                            $tKey = array_search($taxID, array_column($fetchChainTaxes, 'taxID'));
                            $tempTaxArray['taxGroupID'] = $fetchChainTaxes[$tKey]['taxGroupID'];
                        }


                        $orderTaxArray[] = $tempTaxArray;

                    }
                  }
                }

            }
        }


		if ($enableLogging) {
			Log::info("Order processing - batch array: " . json_encode([
				'chainID' => $chainID,
				'storeID' => $storeID,
				'orderID' => $dataArray["orderID"],
			]));
		}
        for ($j=0; $j < $countOf; $j++) {
            $variantID = $dataArray["productsList"][$j]["variantID"];
            $quantity = $dataArray["productsList"][$j]["quantityOrdered"];

            for ($pt=0; $pt < count($dataArray['productsList'][$j]['taxes']); $pt++) {
                $taxID = $dataArray['productsList'][$j]['taxes'][$pt]['taxID'];
                $taxValue = $dataArray['productsList'][$j]['taxes'][$pt]['taxValue'];
                $taxKey = array_search($taxID, array_column($orderTaxArray, 'taxID'));
                if (is_int($taxKey)) {
                    $orderTaxArray[$taxKey]["taxValue"]+= $taxValue;
                }else{
                    $tempTaxArray = array();
                    $tempTaxArray["taxID"] = $taxID;
                    $tempTaxArray["taxName"] = $dataArray['productsList'][$j]['taxes'][$pt]['taxName'];
                    $tempTaxArray["taxPercentage"] = $dataArray['productsList'][$j]['taxes'][$pt]['taxPercentage'];
                    $tempTaxArray["taxValue"] = $taxValue;
                    $tempTaxArray['accountID'] = $dataArray['productsList'][$j]['taxes'][$pt]['accountID'];
                    $tempTaxArray['accountName'] = $dataArray['productsList'][$j]['taxes'][$pt]['accountName'];
                    if($enableTaxFiling == 1){
                        $tKey = array_search($taxID, array_column($fetchChainTaxes, 'taxID'));
                        $tempTaxArray['taxGroupID'] = $fetchChainTaxes[$tKey]['taxGroupID'];
                    }
                    $orderTaxArray[] = $tempTaxArray;
                }
            }

            if (env("IRD_ENABLED") != null && env("IRD_ENABLED") == true) {
                $orderTaxableValue = $dataArray["productsList"][$j]["taxableValue"];
                if (isset($dataArray["productsList"][$j]["taxes"]) && count($dataArray["productsList"][$j]["taxes"]) > 0) {
                    $taxable_sales_vat += $orderTaxableValue;
                } else {
                    $tax_exempted_sales += $orderTaxableValue;
                }

            }

            if (isset($dataArray["productsList"][$j]["batchVariantID"]) && !empty($dataArray["productsList"][$j]["batchVariantID"]) && $dataArray["productsList"][$j]["batchVariantID"] > 0) {
                $batchVariantID = $dataArray["productsList"][$j]["batchVariantID"];
                if ($batchVariantID > 0) {
                    array_push($prodBatchVariantArray, $batchVariantID);
                }
                if (!isset($prodBatchVariantQuantArray[$batchVariantID])) {
                    $prodBatchVariantQuantArray[$batchVariantID] = $quantity;
                }else{
                    $prodBatchVariantQuantArray[$batchVariantID] += $quantity;
                }
            }elseif (isset($dataArray["productsList"][$j]["batchVariantID"]) && !empty($dataArray["productsList"][$j]["batchVariantID"]) && $dataArray["productsList"][$j]["batchVariantID"] < -1 && $automateProductGroupCreation) { // only create if setting enabled
              $createBatchArray = array();
              $createBatchArray['productID'] = $dataArray["productsList"][$j]["productID"];
              $createBatchArray['variantID'] = $dataArray["productsList"][$j]["variantID"];
              $createBatchArray['userID'] = $billingUserID;
              if (isset($dataArray["productsList"][$j]["batchVariantName"]) && $dataArray["productsList"][$j]["batchVariantName"] != null && $dataArray["productsList"][$j]["batchVariantName"] != '') {
                $createBatchArray['batchVariantName'] = $dataArray["productsList"][$j]["batchVariantName"];
              } else {
                $createBatchArray['batchVariantName'] = $dataArray["productsList"][$j]["productName"] . " - batch: " . date('d-M-Y H:i');
              }

              $barcode = NULL;
              $createBatchArray['barcode'] = isset($dataArray["productsList"][$j]["productGroupBarcode"])?$dataArray["productsList"][$j]["productGroupBarcode"]:NULL;
              $createBatchArray['serialNumber'] = isset($dataArray["productsList"][$j]["serialNumber"])?$dataArray["productsList"][$j]["serialNumber"]:NULL;

              $createBatchArray['attribute1'] = isset($dataArray["productsList"][$j]["imeiNumber"])?$dataArray["productsList"][$j]["imeiNumber"]:NULL;

              $createBatchArray['isActive'] = 1;

              $createBatchArray["sourceID"] = $storeID;
              $createBatchArray["sourceType"] = "STORE";

                // Storing attributes data
                for($aCount = 0; $aCount < count($dataArray["productsList"][$j]["attributes"]); $aCount++) {
                    $linkedTo = $dataArray["productsList"][$j]["attributes"][$aCount]["linkedTo"];
                    $value = $dataArray["productsList"][$j]["attributes"][$aCount]["attibuteValue"];
                    $createBatchArray[$linkedTo] = $value;
                }

              $batchCreation = ProductOpticals::createBatchVariants($createBatchArray, $chainID);

              $batchVariantID = $batchCreation["batchVariantID"];
              $dataArray["productsList"][$j]["batchVariantID"] = $batchVariantID;
              array_push($prodBatchVariantArray, $batchVariantID);
              if (!isset($prodBatchVariantQuantArray[$batchVariantID])) {
                  $prodBatchVariantQuantArray[$batchVariantID] = $quantity;
              }else{
                  $prodBatchVariantQuantArray[$batchVariantID] += $quantity;
              }

            }
            if (!isset($prodQuantArray[$variantID])) {
                $prodQuantArray[$variantID] = $quantity;
            }else{
                $prodQuantArray[$variantID] += $quantity;
            }

            if (!in_array($variantID, $prodListArray)) {
              array_push($prodListArray, $variantID);
            }

            if (isset($dataArray["productsList"][$j]["comboProductsList"]) && !empty($dataArray["productsList"][$j]["comboProductsList"])) {
              $comboProdCountOriginal = count($dataArray["productsList"][$j]["comboProductsList"]);
              for ($k=0; $k < $comboProdCountOriginal; $k++) {
                $variantID = $dataArray["productsList"][$j]["comboProductsList"][$k]["variantID"];
                if (!in_array($variantID, $prodListArray)) {
                  array_push($prodListArray, $variantID);
                }

              }

            }

            if (isset($dataArray["productsList"][$j]["modifiersList"]) && !empty($dataArray["productsList"][$j]["modifiersList"])) {
              $modProdCountOriginal = count($dataArray["productsList"][$j]["modifiersList"]);
              for ($k=0; $k < $modProdCountOriginal; $k++) {
                $modifierID = $dataArray["productsList"][$j]["modifiersList"][$k]["modifierID"];
                $modifierQuantity = $dataArray["productsList"][$j]["modifiersList"][$k]["modifierQuantity"];
                $mKey = array_search($modifierID, array_column($chainModList, 'modifierID'));
                if (!is_int($mKey)) {
                  continue;
                }
                $variantID = $chainModList[$mKey]["variantID"];
                $productID = $chainModList[$mKey]["productID"];

                $modBatchID = $chainModList[$mKey]["batchID"];
                $dataArray["productsList"][$j]["modifiersList"][$k]["modifierProductID"] = $productID;
                $dataArray["productsList"][$j]["modifiersList"][$k]["modifierVariantID"] = $variantID;

                if (!in_array($variantID, $prodListArray)) {
                  array_push($prodListArray, $variantID);
                }

                if (!in_array($modifierID, $orderModArray) && $modBatchID <= 0) {
                  array_push($orderModArray, $modifierID);
                }

                if (!isset($prodQuantArray[$variantID])) {
                    $prodQuantArray[$variantID] = $modifierQuantity;
                }else{
                    $prodQuantArray[$variantID] += $modifierQuantity;
                }

              }

            }

        }

        $dataArray['orderTaxArray'] = $orderTaxArray;

        $prodList = rtrim(implode(",", $prodListArray));
        $pbvCount = count($prodBatchVariantArray);
        for ($pbv=0; $pbv < $pbvCount; $pbv++) {
            if ($prodBatchVariantArray[$pbv] == "" || intval($prodBatchVariantArray[$pbv]) < 1) {
                unset($prodBatchVariantArray[$pbv]);
            }
        }
        $prodBatchVarList = ltrim(rtrim(implode(",", $prodBatchVariantArray), ","), ",");

        if (!empty($prodListArray)) {
          $getOldQuantity = convertToArray(DB::select(DB::raw("SELECT DISTINCT A.variantID, A.inventory,A.inventoryCost,B.batchID FROM $catalogueTable AS A INNER JOIN $productPricesTable AS B ON A.variantID = B.variantID WHERE A.variantID IN ($prodList)")));

        }else{
          $getOldQuantity = array();
        }
        $getOldQuantityQueryData = $getOldQuantity;

        // $analyseBatch = self::analyseBatch($prodArray,$chainID,$storeID);

		if ($enableLogging) {
			Log::info("Order processing - stock batch: " . json_encode([
				'chainID' => $chainID,
				'storeID' => $storeID,
				'orderID' => $dataArray["orderID"],
			]));
		}
        $stockBatchList = convertToArray(DB::table($stockBatchDetailTable)->where('isActive',1)->select('productID','variantID','quantity','batchID')->get());
        $prodWithBatchList = convertToArray(DB::table("$productPricesTable AS A")->where('A.isActive',1)->leftJoin("$stockBatchSummaryTable AS B","A.batchID","=","B.batchID")
        ->where("A.batchID",">",0)->where("B.batchType","LIKE","RECIPE")->select('A.productID','A.variantID','A.batchID','A.inventory','A.inventoryCost')->get());
        $stockBatchListCount = count($stockBatchList);
        $prodWithBatchListCount = count($prodWithBatchList);
        if (!empty($getOldQuantity)) {
            for ($i = 0; $i < $countOf; $i++) {
                $variantID = keyCheck($productsList[$i], "variantID");//samosa chat
                $quantityOrdered   = keyCheck($productsList[$i], "quantityOrdered");

                $key = array_search($variantID, array_column($getOldQuantity, 'variantID'));
                if (!is_int($key)) {
                  continue;
                }

                $oldQuantity = $getOldQuantity[$key]["inventory"];
                $inventoryCost= $getOldQuantity[$key]["inventoryCost"];
                $batchID = $getOldQuantity[$key]["batchID"];// samosa chat recipe ID
                $dataArray["productsList"][$i]["batchID"] = $batchID;
                if ($batchID != -1) {

                  if (!isset($batchArray[$batchID])) {
                      $batchArray[$batchID] = $quantityOrdered;
                      $batchList.=$batchID.",";
                  }else{
                      $batchArray[$batchID] += $quantityOrdered;
                  }

                  for ($q=0; $q < $stockBatchListCount; $q++) {
                    //samosa chat recipe prods

                    $stockBatchID = $stockBatchList[$q]["batchID"];
                    if ($stockBatchID != $batchID) {
                      continue;
                    }
                    $stockBatchQuantity = $stockBatchList[$q]["quantity"];
                    $stockBatchVariantID = $stockBatchList[$q]["variantID"];//samosa

                    $batchKeyCheck = array_search($stockBatchVariantID, array_column($prodWithBatchList, 'variantID'));

                    if (is_int($batchKeyCheck)) {
                      $prodWithBatchID = $prodWithBatchList[$batchKeyCheck]["batchID"];//samosa recipe ID

                      if ($prodWithBatchID != -1) {
                        if (!isset($batchArray[$prodWithBatchID])) {
                            $batchArray[$prodWithBatchID] = $stockBatchQuantity*$quantityOrdered;
                            $batchList.=$prodWithBatchID.",";
                        }else{
                            $batchArray[$prodWithBatchID] += $stockBatchQuantity*$quantityOrdered;
                        }

                      }

                      if (!isset($prodBatchArray[$stockBatchVariantID])) {
                          $prodBatchArray[$stockBatchVariantID] = $prodWithBatchID;
                      }

                      for ($r=0; $r < $stockBatchListCount; $r++) {
                        //samosa recipe products
                        // write the loop to fetch the recipe for products like potato

                      }


                    }

                  }

                }

                if (!isset($prodBatchArray[$variantID])) {
                    $prodBatchArray[$variantID] = $batchID;
                }

                if ($oldQuantity == 0 || $oldQuantity == 0.00) {
                    $costPrice = 0.00;
                }else{
                    $costPrice = $inventoryCost/$oldQuantity;
                }
                if(!isset($dataArray["productsList"][$i]['costPrice']) || empty($dataArray["productsList"][$i]['costPrice'])){
                    $dataArray["productsList"][$i]['costPrice'] = $costPrice;
                }


                $getOldQuantity[$key]["costPrice"] = $costPrice;
                if (!isset($getOldQuantity[$key]["newQuantity"])) {
                    $getOldQuantity[$key]["newQuantity"] = $oldQuantity - $quantityOrdered;
                }else{
                    $getOldQuantity[$key]["newQuantity"] -= $quantityOrdered;
                }


            }

            //combo product list fetch for recipe prods
            for ($i = 0; $i < $countOf; $i++) {
              if (!isset($productsList[$i]["comboProductsList"])) {
                continue;
              }
              for ($j=0; $j < count($productsList[$i]["comboProductsList"]); $j++) {
                $variantID = $productsList[$i]["comboProductsList"][$j]["variantID"];
                $quantityOrdered   = $productsList[$i]["comboProductsList"][$j]["itemQuantity"]*$productsList[$i]["comboProductsList"][$j]["quantity"]*$productsList[$i]["quantityOrdered"];

                $key = array_search($variantID, array_column($getOldQuantity, 'variantID'));
                if (!is_int($key)) {
                  continue;
                }
                $oldQuantity = $getOldQuantity[$key]["inventory"];
                $inventoryCost= $getOldQuantity[$key]["inventoryCost"];
                $batchID = $getOldQuantity[$key]["batchID"];
                $dataArray["productsList"][$i]["batchID"] = $batchID;

                if ($batchID != -1) {

                  if (!isset($batchArray[$batchID])) {
                      $batchArray[$batchID] = $quantityOrdered;
                      $batchList.=$batchID.",";
                  }else{
                      $batchArray[$batchID] += $quantityOrdered;
                  }

                  for ($q=0; $q < $stockBatchListCount; $q++) {
                    //samosa chat recipe prods

                    $stockBatchID = $stockBatchList[$q]["batchID"];
                    if ($stockBatchID != $batchID) {
                      continue;
                    }
                    $stockBatchQuantity = $stockBatchList[$q]["quantity"];
                    $stockBatchVariantID = $stockBatchList[$q]["variantID"];//samosa

                    $batchKeyCheck = array_search($stockBatchVariantID, array_column($prodWithBatchList, 'variantID'));

                    if (is_int($batchKeyCheck)) {
                      $prodWithBatchID = $prodWithBatchList[$batchKeyCheck]["batchID"];//samosa recipe ID

                      if ($prodWithBatchID != -1) {
                        if (!isset($batchArray[$prodWithBatchID])) {
                            $batchArray[$prodWithBatchID] = $stockBatchQuantity*$quantityOrdered;
                            $batchList.=$prodWithBatchID.",";
                        }else{
                            $batchArray[$prodWithBatchID] += $stockBatchQuantity*$quantityOrdered;
                        }

                      }

                      if (!isset($prodBatchArray[$stockBatchVariantID])) {
                          $prodBatchArray[$stockBatchVariantID] = $prodWithBatchID;
                      }

                      for ($r=0; $r < $stockBatchListCount; $r++) {
                        //samosa recipe products


                      }


                    }

                  }

                }

                if (!isset($prodBatchArray[$variantID])) {
                    $prodBatchArray[$variantID] = $batchID;
                }

                if ($oldQuantity == 0 || $oldQuantity == 0.00) {
                    $costPrice = 0.00;
                }else{
                    $costPrice = $inventoryCost/$oldQuantity;
                }

                if(!isset($dataArray["productsList"][$i]['costPrice']) || empty($dataArray["productsList"][$i]['costPrice'])){
                    $dataArray["productsList"][$i]['costPrice'] = $costPrice;
                }


                $getOldQuantity[$key]["costPrice"] = $costPrice;
                if (!isset($getOldQuantity[$key]["newQuantity"])) {
                    $getOldQuantity[$key]["newQuantity"] = $oldQuantity - $quantityOrdered;
                }else{
                    $getOldQuantity[$key]["newQuantity"] -= $quantityOrdered;
                }


              }


            }

            //modifiers product list fetch for recipe prods
            for ($i = 0; $i < $countOf; $i++) {
              if (!isset($productsList[$i]["modifiersList"])) {
                continue;
              }
              for ($j=0; $j < count($productsList[$i]["modifiersList"]); $j++) {
                $modifierID = $productsList[$i]["modifiersList"][$j]["modifierID"];
                $mKey = array_search($modifierID, array_column($chainModList, 'modifierID'));
                if (!is_int($mKey)) {
                  continue;
                }
                $variantID = $chainModList[$mKey]["variantID"];
                $productID = $chainModList[$mKey]["productID"];

                $modBatchID = $chainModList[$mKey]["batchID"];

                $quantityOrdered   = $productsList[$i]["modifiersList"][$j]["modifierQuantity"];

                $key = array_search($variantID, array_column($getOldQuantity, 'variantID'));
                if (!is_int($key)) {
                  continue;
                }
                $oldQuantity = $getOldQuantity[$key]["inventory"];
                $inventoryCost= $getOldQuantity[$key]["inventoryCost"];
                $batchID = $getOldQuantity[$key]["batchID"];
                // $dataArray["productsList"][$i]["batchID"] = $batchID;

                if ($batchID != -1) {

                  if (!isset($batchArray[$batchID])) {
                      $batchArray[$batchID] = $quantityOrdered;
                      $batchList.=$batchID.",";
                  }else{
                      $batchArray[$batchID] += $quantityOrdered;
                  }

                  for ($q=0; $q < $stockBatchListCount; $q++) {
                    //samosa chat recipe prods

                    $stockBatchID = $stockBatchList[$q]["batchID"];
                    if ($stockBatchID != $batchID) {
                      continue;
                    }
                    $stockBatchQuantity = $stockBatchList[$q]["quantity"];
                    $stockBatchVariantID = $stockBatchList[$q]["variantID"];//samosa

                    $batchKeyCheck = array_search($stockBatchVariantID, array_column($prodWithBatchList, 'variantID'));

                    if (is_int($batchKeyCheck)) {
                      $prodWithBatchID = $prodWithBatchList[$batchKeyCheck]["batchID"];//samosa recipe ID

                      if ($prodWithBatchID != -1) {
                        if (!isset($batchArray[$prodWithBatchID])) {
                            $batchArray[$prodWithBatchID] = $stockBatchQuantity*$quantityOrdered;
                            $batchList.=$prodWithBatchID.",";
                        }else{
                            $batchArray[$prodWithBatchID] += $stockBatchQuantity*$quantityOrdered;
                        }

                      }

                      if (!isset($prodBatchArray[$stockBatchVariantID])) {
                          $prodBatchArray[$stockBatchVariantID] = $prodWithBatchID;
                      }

                      for ($r=0; $r < $stockBatchListCount; $r++) {
                        //samosa recipe products


                      }


                    }

                  }

                }

                if (!isset($prodBatchArray[$variantID])) {
                    $prodBatchArray[$variantID] = $batchID;
                }

                // if ($oldQuantity == 0 || $oldQuantity == 0.00) {
                //     $costPrice = 0.00;
                // }else{
                //     $costPrice = $inventoryCost/$oldQuantity;
                // }
                // $dataArray["productsList"][$i]['costPrice'] = $costPrice;

                // $getOldQuantity[$key]["costPrice"] = $costPrice;
                // if (!isset($getOldQuantity[$key]["newQuantity"])) {
                //     $getOldQuantity[$key]["newQuantity"] = $oldQuantity - $quantityOrdered;
                // }else{
                //     $getOldQuantity[$key]["newQuantity"] -= $quantityOrdered;
                // }


              }


            }

        }

		if ($enableLogging) {
			Log::info("Order processing - batch list: " . json_encode([
				'chainID' => $chainID,
				'storeID' => $storeID,
				'orderID' => $dataArray["orderID"],
			]));
		}
        $batchList = rtrim($batchList,",");
        $varArray = array();
        $orderBatchArray = array();
        $yieldProdArray = array();
        $yieldTotalItems = 0;
        $yieldTotalCost = 0.00;
        $y = 0;
        if (!empty($batchArray)) {
          $getOldBatchQuantity = convertToArray(DB::select(DB::raw("SELECT A.batchID, A.variantID,A.productID,B.inventory,B.inventoryCost, B.variantName,C.productName, C.categoryID,D.categoryName,C.brandID,B.weight,B.weightUnit,E.brandName,C.soldIn AS unitID,F.name AS unitName, B.barcode,B.sku,C.HSNOrSACCode, A.quantity AS quantCount,A.yieldPercentage/100 AS yield,B.colourID, B.colourName,A.costPrice,C.productType FROM $stockBatchDetailTable AS A
              INNER JOIN $productPricesTable AS B ON A.variantID = B.variantID
              INNER JOIN $productsTable AS C ON A.productID = C.productID
              LEFT JOIN $categoryTable AS D ON C.categoryID = D.categoryID
              LEFT JOIN $brandsTable AS E ON C.brandID = E.brandID
              INNER JOIN $measurementUnitsTable AS F ON C.soldIn = F.unitID
              INNER JOIN $stockBatchSummaryTable AS G ON A.batchID = G.batchID
              WHERE A.batchID IN ($batchList)  AND A.isActive = 1 AND G.batchType LIKE 'RECIPE' ORDER BY A.batchID")));
          $batchCount = count($getOldBatchQuantity);

          for ($i=0; $i < $batchCount; $i++) {
              $batchID = $getOldBatchQuantity[$i]['batchID'];
              if (isset($batchArray[$batchID])) {
                  $batchQuantity = $batchArray[$batchID];
              }else{
                  $batchQuantity = 0;
              }
              $variantID = $getOldBatchQuantity[$i]['variantID'];
              $variantName = $getOldBatchQuantity[$i]['variantName'];
              $varQuant = $getOldBatchQuantity[$i]['quantCount'];
              $varInventory = $getOldBatchQuantity[$i]['inventory'];
              $varInventoryCost = $getOldBatchQuantity[$i]['inventoryCost'];
              $productID = $getOldBatchQuantity[$i]['productID'];
              $productName = $getOldBatchQuantity[$i]['productName'];
              $categoryID = $getOldBatchQuantity[$i]['categoryID'];
              $categoryName = $getOldBatchQuantity[$i]['categoryName'];
              $brandID = $getOldBatchQuantity[$i]['brandID'];
              $brandName = $getOldBatchQuantity[$i]['brandName'];
              $unitID = $getOldBatchQuantity[$i]['unitID'];
              $unitName = $getOldBatchQuantity[$i]['unitName'];
              $barcode = $getOldBatchQuantity[$i]['barcode'];
              $sku = $getOldBatchQuantity[$i]['sku'];
              $HSNOrSACCode = $getOldBatchQuantity[$i]['HSNOrSACCode'];
              $colourID = $getOldBatchQuantity[$i]["colourID"];
              $colourName = $getOldBatchQuantity[$i]['colourName'];
              $yield = $getOldBatchQuantity[$i]['yield'];
              $weight = $getOldBatchQuantity[$i]['weight'];
              $weightUnit = $getOldBatchQuantity[$i]['weightUnit'];
              $costPrice = $getOldBatchQuantity[$i]['costPrice'];
              $productType = $getOldBatchQuantity[$i]['productType'];

              if ($yield != 1 && $yield > 0) {
                $yieldProdArray[$y]['productID'] = $productID;
                $yieldProdArray[$y]['variantID'] = $variantID;
                $yieldProdArray[$y]['batchID'] = $batchID;
                $yieldProdArray[$y]['batchVariantID'] = 0;
                $yieldProdArray[$y]['brandID'] = $brandID;

                if ($varInventory > 0) {
                  $yieldProdArray[$y]['costPrice'] = round($varInventoryCost/$varInventory,2);
                }else{
                  $yieldProdArray[$y]['costPrice'] = 0.00;
                }

                $yieldProdArray[$y]['displayCostPrice'] = $yieldProdArray[$y]['costPrice'];
                $yieldProdArray[$y]['oldQuantity'] = 0;
                $yieldProdArray[$y]['newQuantity'] = 0;
                $yieldProdArray[$y]['quantity'] = round(($varQuant/$yield)*$batchQuantity,2);

                $yieldTotalItems+=$yieldProdArray[$y]['quantity'];

                $yieldProdArray[$y]['displayQuantity'] = $yieldProdArray[$y]['quantity'];
                $yieldProdArray[$y]['batchQuantity'] = $batchQuantity;
                $yieldProdArray[$y]['unitID'] = $unitID;
                $yieldProdArray[$y]['unitName'] = $unitName;
                $yieldProdArray[$y]['displayUnitID'] = $unitID;
                $yieldProdArray[$y]['displayUnitName'] = $unitName;
                $yieldProdArray[$y]['totalPrice'] = $yieldProdArray[$y]['quantity']*$yieldProdArray[$y]['costPrice'];
                $yieldTotalCost+=$yieldProdArray[$y]['totalPrice'];

                $yieldProdArray[$y]['timezone'] = $dataArray["timezone"];
                $yieldProdArray[$y]['discountTotalValue'] = 0.00;
                $yieldProdArray[$y]['checkInTimeLocal'] = $dataArray['orderCreationTimeLocal'];
                $yieldProdArray[$y]['checkInTimeUTC'] = localToUTC($yieldProdArray[$y]['checkInTimeLocal'],$yieldProdArray[$y]['timezone']);
                $yieldProdArray[$y]['taxValues'] = "0";
                $yieldProdArray[$y]['status'] = "APPROVED";
                $y++;
              }

              $orderBatchArray[$i]["storeID"] = $storeID;
              $orderBatchArray[$i]["orderID"] = $dataArray["orderID"];
              $orderBatchArray[$i]["batchID"] = $batchID;
              $orderBatchArray[$i]["productID"] = $productID;
              $orderBatchArray[$i]["variantID"] = $variantID;
              $orderBatchArray[$i]["quantity"] = $varQuant*$batchQuantity;
              $orderBatchArray[$i]['costPrice'] = $costPrice;
              $orderBatchArray[$i]["unitID"] = $unitID;
              $orderBatchArray[$i]["orderTimeLocal"] = $dataArray["orderCreationTimeLocal"];
              $orderBatchArray[$i]["timezone"] = $dataArray["timezone"];
              $orderBatchArray[$i]["orderTimeUTC"] =localToUTC($dataArray["orderCreationTimeLocal"],$dataArray["timezone"]);

              $varKey = array_search($variantID, array_column($getOldQuantity, 'variantID'));

              if (is_int($varKey)) {
                  if ($yield > 0) {
                    $getOldQuantity[$varKey]['newQuantity'] -= ($varQuant/$yield)*$batchQuantity;
                  }else{
                    $getOldQuantity[$varKey]['newQuantity'] -= $varQuant*$batchQuantity;
                  }

              }else{
                  $variantBatchArray = array();
                  $variantBatchArray['variantID'] = $variantID;
                  $variantBatchArray['inventory'] = $varInventory;
                  $variantBatchArray['inventoryCost'] = $varInventoryCost;
                  $variantBatchArray['newQuantity'] = $varInventory - ($varQuant/($yield > 0 ? $yield:1))*$batchQuantity;
                  $variantBatchArray['batchID'] = -1;
                  $getOldQuantity[] = $variantBatchArray;

              }

              $varKey1 = array_search($variantID, array_column($varArray, 'variantID'));

              if (is_int($varKey1)) {
                  $varArray[$varKey1]['quantity'] += $varQuant*$batchQuantity;
              }else{
                  $variantBatchArrayNew = array();
                  $variantBatchArrayNew['variantID'] = $variantID;
                  $variantBatchArrayNew['productID'] = $productID;
                  $variantBatchArrayNew['productType'] = $productType;
                  $variantBatchArrayNew['productName'] = $productName;
                  $variantBatchArrayNew['variantName'] = $variantName;

                  $variantBatchArrayNew['categoryID'] = $categoryID;
                  $variantBatchArrayNew['categoryName'] = $categoryName;

                  $variantBatchArrayNew['brandID'] = $brandID;
                  $variantBatchArrayNew['brandName'] = $brandName;

                  $variantBatchArrayNew['unitID'] = $unitID;
                  $variantBatchArrayNew['unitName'] = $unitName;

                  $variantBatchArrayNew['barcode'] = $barcode;
                  $variantBatchArrayNew['sku'] = $sku;
                  $variantBatchArrayNew['HSNOrSACCode'] = $HSNOrSACCode;

                  $variantBatchArrayNew['quantity'] = $varQuant*$batchQuantity;
                  $variantBatchArrayNew['batchQuantity'] = $batchQuantity;
                  $variantBatchArrayNew['batchID'] = $batchID;
                  $variantBatchArrayNew['colourID'] = $colourID;
                  $variantBatchArrayNew['colourName'] = $colourName;
                  $variantBatchArrayNew['unitName'] = $unitName;
                  $variantBatchArrayNew['weight'] = $weight;
                  $variantBatchArrayNew['weightUnit'] = $weightUnit;
                  $varArray[] = $variantBatchArrayNew;

              }
          }


        }else{
            $getOldBatchQuantity = array();

        }

		if ($enableLogging) {
			Log::info("Order processing - modifiers: " . json_encode([
				'chainID' => $chainID,
				'storeID' => $storeID,
				'orderID' => $dataArray["orderID"],
			]));
		}
        /** modifiers insertion in order detail table start**/
        if (!empty($orderModArray)) {
          $orderModList = rtrim(implode(",", $orderModArray));
          $getOldModQuantity = convertToArray(DB::select(DB::raw("SELECT A.modifierID, A.modifierProductID,B.variantID,B.productID,B.inventory,B.inventoryCost, B.variantName,C.productName, C.categoryID,D.categoryName,C.brandID,B.weight,B.weightUnit,E.brandName,C.soldIn AS unitID,F.name AS unitName, B.barcode,B.sku,C.HSNOrSACCode,B.colourID, B.colourName,C.productType FROM $modifiersTable AS A
              INNER JOIN $productPricesTable AS B ON A.modifierProductID = B.productID
              INNER JOIN $productsTable AS C ON A.modifierProductID = C.productID
              LEFT JOIN $categoryTable AS D ON C.categoryID = D.categoryID
              LEFT JOIN $brandsTable AS E ON C.brandID = E.brandID
              INNER JOIN $measurementUnitsTable AS F ON C.soldIn = F.unitID
              WHERE A.modifierID IN ($orderModList)  ORDER BY A.modifierID")));
          $batchCount = count($getOldModQuantity);

          for ($i=0; $i < $batchCount; $i++) {
              $variantID = $getOldModQuantity[$i]['variantID'];
              if (isset($prodQuantArray[$variantID])) {
                  $batchQuantity = $prodQuantArray[$variantID];
              }else{
                  $batchQuantity = 0;
              }
              $variantID = $getOldModQuantity[$i]['variantID'];
              $variantName = $getOldModQuantity[$i]['variantName'];
              $varQuant = 1;
              $varInventory = $getOldModQuantity[$i]['inventory'];
              $varInventoryCost = $getOldModQuantity[$i]['inventoryCost'];
              $productID = $getOldModQuantity[$i]['productID'];
              $productName = $getOldModQuantity[$i]['productName'];
              $productType = $getOldModQuantity[$i]['productType'];
              $categoryID = $getOldModQuantity[$i]['categoryID'];
              $categoryName = $getOldModQuantity[$i]['categoryName'];
              $brandID = $getOldModQuantity[$i]['brandID'];
              $brandName = $getOldModQuantity[$i]['brandName'];
              $unitID = $getOldModQuantity[$i]['unitID'];
              $unitName = $getOldModQuantity[$i]['unitName'];
              $barcode = $getOldModQuantity[$i]['barcode'];
              $sku = $getOldModQuantity[$i]['sku'];
              $HSNOrSACCode = $getOldModQuantity[$i]['HSNOrSACCode'];
              $colourID = $getOldModQuantity[$i]["colourID"];
              $colourName = $getOldModQuantity[$i]['colourName'];
              $weight = $getOldModQuantity[$i]['weight'];
              $weightUnit = $getOldModQuantity[$i]['weightUnit'];


              $orderBatchArray[$i]["storeID"] = $storeID;
              $orderBatchArray[$i]["orderID"] = $dataArray["orderID"];
              $orderBatchArray[$i]["batchID"] = -1;
              $orderBatchArray[$i]["productID"] = $productID;
              $orderBatchArray[$i]["variantID"] = $variantID;
              $orderBatchArray[$i]["quantity"] = $batchQuantity;
              $orderBatchArray[$i]["unitID"] = $unitID;
              $orderBatchArray[$i]["orderTimeLocal"] = $dataArray["orderCreationTimeLocal"];
              $orderBatchArray[$i]["timezone"] = $dataArray["timezone"];
              $orderBatchArray[$i]["orderTimeUTC"] =localToUTC($dataArray["orderCreationTimeLocal"],$dataArray["timezone"]);

              $varKey = array_search($variantID, array_column($getOldQuantity, 'variantID'));

              if (is_int($varKey)) {
                if (isset($getOldQuantity[$varKey]["newQuantity"])) {
                  $getOldQuantity[$varKey]['newQuantity'] -= $batchQuantity;
                }else{
                  $getOldQuantity[$varKey]['newQuantity'] = $varInventory - $batchQuantity;
                }


              }else{
                $variantBatchArray = array();
                $variantBatchArray['variantID'] = $variantID;
                $variantBatchArray['inventory'] = $varInventory;
                $variantBatchArray['inventoryCost'] = $varInventoryCost;
                $variantBatchArray['newQuantity'] = $varInventory - $batchQuantity;
                $variantBatchArray['batchID'] = -1;
                $getOldQuantity[] = $variantBatchArray;

              }

              $varKey1 = array_search($variantID, array_column($varArray, 'variantID'));

              if (is_int($varKey1)) {
                  $varArray[$varKey1]['quantity'] += $varQuant*$batchQuantity;
              }else{
                  $variantBatchArrayNew = array();
                  $variantBatchArrayNew['variantID'] = $variantID;
                  $variantBatchArrayNew['productID'] = $productID;

                  $variantBatchArrayNew['productName'] = $productName;
                  $variantBatchArrayNew['productType'] = $productType;
                  $variantBatchArrayNew['variantName'] = $variantName;

                  $variantBatchArrayNew['categoryID'] = $categoryID;
                  $variantBatchArrayNew['categoryName'] = $categoryName;

                  $variantBatchArrayNew['brandID'] = $brandID;
                  $variantBatchArrayNew['brandName'] = $brandName;

                  $variantBatchArrayNew['unitID'] = $unitID;
                  $variantBatchArrayNew['unitName'] = $unitName;

                  $variantBatchArrayNew['barcode'] = $barcode;
                  $variantBatchArrayNew['sku'] = $sku;
                  $variantBatchArrayNew['HSNOrSACCode'] = $HSNOrSACCode;

                  $variantBatchArrayNew['quantity'] = $batchQuantity;
                  $variantBatchArrayNew['batchQuantity'] = $batchQuantity;
                  $variantBatchArrayNew['batchID'] = $batchID;
                  $variantBatchArrayNew['colourID'] = $colourID;
                  $variantBatchArrayNew['colourName'] = $colourName;
                  $variantBatchArrayNew['unitName'] = $unitName;
                  $variantBatchArrayNew['weight'] = $weight;
                  $variantBatchArrayNew['weightUnit'] = $weightUnit;
                  $varArray[] = $variantBatchArrayNew;

              }
          }

        }else{
            $getOldModQuantity = array();

        }

        $varCount = count($varArray);
        $orderSubCount = -1;
        for ($g = 0; $g < $varCount; $g++) {
            $varListArray = array();
            $varListArray["orderSubID"]  = $orderSubCount;
            $orderSubCount--;
            $varListArray["productID"]   = $varArray[$g]["productID"];
            $varListArray["productName"] = $varArray[$g]["productName"];
            $varListArray["productType"] = $varArray[$g]["productType"];
            $varListArray["voidQuantity"]   = 0;
            $varListArray["categoryID"]  = $varArray[$g]["categoryID"];
            $varListArray["categoryName"]  = $varArray[$g]["categoryName"];


            $varListArray["brandID"]  = $varArray[$g]["brandID"];

            $varListArray["isOpenItem"]  = -1;

            $varListArray["brandName"]  = $varArray[$g]["brandName"];

            $varListArray["barcode"]  = $varArray[$g]["barcode"];

                $varListArray["sku"]  = $varArray[$g]["barcode"];

            $varListArray["subCategoryID"]  = -1;
            $varListArray["subCategoryName"]  = NULL;


            $varListArray["variantID"] = $varArray[$g]["variantID"];

            $varListArray["variantName"] = $varArray[$g]["variantName"];

            $varListArray["sizeID"] = 0;

            $varListArray["sizeName"] = NULL;

            $varListArray["colourID"] = $varArray[$g]["colourID"];

            $varListArray["colourName"] = $varArray[$g]["colourName"];

            if(isset($varArray[$g]["styleID"])){
                $varListArray["styleID"] = $varArray[$g]["styleID"];
            }

            if(isset($varArray[$i]["styleName"])){
                $varListArray["styleName"] = $varArray[$g]["styleName"];
            }

            $varListArray["HSNOrSACCode"] = $varArray[$g]["HSNOrSACCode"];

            $varListArray["soldIn"] = $varArray[$g]["unitName"];

            $varListArray["productBasePrice"]  = 0.00;

            $varListArray["productActualPrice"] = 0.00;

            $varListArray["measurementUnitID"] = $varArray[$g]["unitID"];
            $varListArray["soldInValue"] = $varArray[$g]["unitName"];
            $varListArray["weight"] = $varArray[$g]['weight'];
            $varListArray["weightUnit"] = $varArray[$g]['weightUnit'];

            $varListArray["weight"] = $varArray[$g]["weight"];
            $varListArray["weightUnit"] = $varArray[$g]["weightUnit"];

            $varListArray["quantityOrdered"]   = $varArray[$g]["quantity"];

            $varListArray["productValue"]      = 0.00;

            $varListArray["itemSales"]   = 0.00;

            $varListArray["taxableValue"]   = 0.00;
            $varListArray["taxIDs"]         = NULL;
            $varListArray["taxNames"]       = NULL;
            $varListArray["taxPercentages"] = NULL;
            $varListArray["taxValues"]      = NULL;

            $varListArray["totalTaxValue"] = 0.00;

            $varListArray["additionalChargeIDs"]    = NULL;

            $varListArray["additionalChargeNames"]    = NULL;

            $varListArray["additionalChargePrices"]    = NULL;

            $varListArray["additionalChargeValues"]    = NULL;

            $varListArray["totalChargeValue"] = 0.0000;

            $discount["Discounts"] = array();


            $varListArray["discountRemarks"] = NULL;
            $varListArray["discountTotalValue"] = 0.00;
            $varListArray["discountIDs"]    = NULL;

            $varListArray["discountNames"]    = NULL;
            $varListArray["discountValues"]    = NULL;

            $varListArray["voucherID"] = NULL;


            // Time related information
            $varListArray["orderTimeLocal"] = $dataArray["orderCreationTimeLocal"];
            if (isset($varListArray["orderTimeLocal"])) {
                $varListArray["orderTimeUTC"] = localToUTC($varListArray["orderTimeLocal"], $dataArray["timezone"]);
            }

            $varListArray["isNoCharge"] = 0;


            $varListArray["posDate"] = $dataArray["posDate"];

            $varListArray["invoiceNumber"] = $dataArray["invoiceNumber"];

            $varListArray["isVoid"] = 0;

            $varListArray["timezone"]   = $dataArray["timezone"];
            $varListArray["Status"]     = $dataArray["Status"];

            $dataArray["productsList"][] = $varListArray;
        }

        /** modifiers insertion in order detail table end **/


        if (!empty($prodBatchVariantArray) && rtrim($prodBatchVarList,',') != null && rtrim($prodBatchVarList,',') != '') {
            $getOldBatchVarQuantity = convertToArray(DB::select(DB::raw("SELECT * FROM $prodVariantCatalogueTable WHERE isActive = 1 AND batchVariantID IN ($prodBatchVarList)")));
            $getOldChainBatchVarQuantity = convertToArray(DB::select(DB::raw("SELECT * FROM $productBatchVariantsTable WHERE isActive = 1 AND batchVariantID IN ($prodBatchVarList)")));

        }else{
            $getOldBatchVarQuantity = array();
            $getOldChainBatchVarQuantity = array();
        }

        $nonProdBatchVarArray = array();

        $batchVarCount = count($getOldChainBatchVarQuantity);
        $g = 0;
        for ($i=0; $i < $batchVarCount; $i++) {
            $batchVariantID = $getOldChainBatchVarQuantity[$i]['batchVariantID'];
            $productID = $getOldChainBatchVarQuantity[$i]['productID'];
            $variantID = $getOldChainBatchVarQuantity[$i]['variantID'];

            $batchVarKey = array_search($batchVariantID, array_column($getOldBatchVarQuantity, 'batchVariantID'));

            if (!is_int($batchVarKey)) {
                $nonProdBatchVarArray[$g]['batchVariantID'] = $batchVariantID;
                $nonProdBatchVarArray[$g]['productID'] = $productID;
                $nonProdBatchVarArray[$g]['variantID'] = $variantID;
                $nonProdBatchVarArray[$g]['inventory'] = 0.00;
                $nonProdBatchVarArray[$g]['inventoryCost'] = 0.00;
                $nonProdBatchVarArray[$g]['isActive'] = 1;
                $g++;

                $getOldBatchVarQuantity[] = $nonProdBatchVarArray[$g];
            }
        }


        if (!empty($getOldBatchVarQuantity)) {
            for ($i=0; $i < $countOf; $i++) {
                $batchVariantID = keyCheck($productsList[$i], "batchVariantID");
                $quantityOrdered   = keyCheck($productsList[$i], "quantityOrdered");

                $prodBatchKey = array_search($batchVariantID, array_column($getOldBatchVarQuantity, 'batchVariantID'));
                if ($productsList[$i]["variantID"] != $getOldBatchVarQuantity[$prodBatchKey]["variantID"]) {
                    continue;
                }
                $oldQuantity = $getOldBatchVarQuantity[$prodBatchKey]["inventory"];
                $inventoryCost= $getOldBatchVarQuantity[$prodBatchKey]["inventoryCost"];

                if ($oldQuantity == 0 || $oldQuantity == 0.00) {
                    $costPrice = 0.00;
                }else{
                    $costPrice = $inventoryCost/$oldQuantity;
                }

                if(!isset($dataArray["productsList"][$i]['costPrice']) || empty($dataArray["productsList"][$i]['costPrice'])){
                    $dataArray["productsList"][$i]['costPrice'] = $costPrice;
                }


                $getOldBatchVarQuantity[$prodBatchKey]["costPrice"] = $costPrice;
                if (!isset($getOldBatchVarQuantity[$prodBatchKey]["newQuantity"])) {
                    $getOldBatchVarQuantity[$prodBatchKey]["newQuantity"] = $oldQuantity - $quantityOrdered;
                }else{
                    $getOldBatchVarQuantity[$prodBatchKey]["newQuantity"] -= $quantityOrdered;
                }


            }
        }


		if ($enableLogging) {
			Log::info("Order processing - OS: " . json_encode([
				'chainID' => $chainID,
				'storeID' => $storeID,
				'orderID' => $dataArray["orderID"],
			]));
		}
            $orderSummary    = self::createOrderSummary($storeID, $chainID, $dataArray, $tables, $settings);
            $orderSummary["customersIDs"] = $customer1;
            if (!empty($dataArray["customers"])) {
                $orderSummary["loyaltyPointsRedeemed"] = $dataArray["customers"][0]["loyaltyPointsRedeemed"] ?? 0;
            }

            $dataArray['prodBatchArray'] = $prodBatchArray;

            // tata motors integration

            if($getChainSettings[0]['merchantIntegrationModule'] == 'tataMotors'){
                $tataMotorsResponse = TataMotors::saveInvoiceData($storeID,$dataArray);
                if($tataMotorsResponse['status'] == true){
                    $dataArray['tataMotorsOrderSyncStatus'] = 1;
                }else{
                    $dataArray['tataMotorsOrderSyncStatus'] = 0;
                }
            }
			if ($enableLogging) {
				Log::info("Order processing - OD: " . json_encode([
					'chainID' => $chainID,
					'storeID' => $storeID,
					'orderID' => $dataArray["orderID"],
				]));
			}
            // Use the new OrderDetailBuilder for optimized processing
            $orderDetailBuilder = new \App\Services\OrderDetailBuilder($storeID, $chainID, $settings, $dataArray, $tables);

            try {
                $orderDetail = $orderDetailBuilder->createOrderDetail();
            } catch (\Exception $e) {
                Log::error('Exception during order detail creation: ' . $e->getMessage());
                // Fallback to old method
                $orderDetail = OldOrder::createOrderDetail($storeID, $chainID, $getChainSettings[0], $dataArray, $tables);
            }

            $checkSO = [];
            $keepExistingStoreIDFlag = 0;
            if(isset($dataArray["salesOrderID"]) && $dataArray["salesOrderID"] != null && $dataArray["salesOrderID"] != ""){
                $checkSO = convertToArray(DB::table($salesOrderSummaryTable)->where("salesOrderID",$orderSummary["salesOrderID"])->get());
                if((isset($dataArray["channelId"]) && $dataArray["channelId"] > 0) || (isset($dataArray["channelID"]) && $dataArray["channelID"] > 0)){
                    if (!isset($dataArray["channelId"])) {
                        $dataArray["channelId"] = $dataArray["channelID"];
                    }

                    $channelSalesOrderConfigurationDetails = convertToArray(DB::table($channelSalesOrderConfigurationTable)->where('channelID', $dataArray['channelId'])->where("isActive",1)->get());
                    $SORevenueRecognition = $channelSalesOrderConfigurationDetails[0]['SORevenueRecognition'];
                    if($SORevenueRecognition == "INVOICING"){
                        $orderAccounting = SalesOrder::createSalesOrderAccounting($storeID, $dataArray, $getChainSettings, $checkSO);
                        $keepExistingStoreIDFlag = 1;
                    }
                }

                //when receiving TDS and TCS on invoice in case of sales order
                $salesOrderTDSValue = $checkSO[0]['TDSValue'];
                $salesOrderTCSValue = $checkSO[0]['TCSValue'];
                if($enableSourceTax == 1){
                    $accountCount = 0;
                    if($salesOrderTDSValue <= 0 && $salesOrderTCSValue <= 0){
                        if(isset($dataArray['TDSValue']) && $dataArray['TDSValue'] > 0){
                            $temp = Accounts::createTDS($storeID, $dataArray);
                            for ($i = 0; $i < count($temp); $i++) {
                                $orderAccounting[] = $temp[$i];
                                $accountCount++;
                            }
                        }

                        if(isset($dataArray['TCSValue']) && $dataArray['TCSValue'] > 0){
                            $temp = Accounts::createTCS($storeID, $dataArray);
                            for ($i = 0; $i < count($temp); $i++) {
                                $orderAccounting[] = $temp[$i];
                                $accountCount++;
                            }
                        }

                        if((isset($dataArray['TCSValue']) && $dataArray['TCSValue'] > 0)){
                            $dataArray['tdstcsPayments'] = 1;
                            $temp = SalesOrder::createPayments($storeID, $dataArray,"Order Placed");
                            for ($i = 0; $i < count($temp); $i++) {
                                $orderAccounting[] = $temp[$i];
                                $accountCount++;
                            }
                        }else if(isset($dataArray['TDSValue']) && $dataArray['TDSValue'] > 0){
                            $dataArray['tdstcsPayments'] = 1;
                            $temp = SalesOrder::createPayments($storeID, $dataArray,"");
                            for ($i = 0; $i < count($temp); $i++) {
                                $orderAccounting[] = $temp[$i];
                                $accountCount++;
                            }
                        }

                    }
                }
            }else{

				if ($enableLogging) {
					Log::info("Order processing - OA: " . json_encode([
						'chainID' => $chainID,
						'storeID' => $storeID,
						'orderID' => $dataArray["orderID"],
					]));
				}

                // Check if we should use optimized accounting service
                $useOptimizedAccounting = 1;

                if (!$useOptimizedAccounting) {
                    // Only generate accounting records if not using optimized service
                    $orderAccounting = OldOrder::createOrderAccounting($storeID, $dataArray, $getChainSettings);
                } else {
                    // Use new optimized accounting service (generation + bulk insertion) - Phase 1
                    try {
                        $accountingService = new \App\Services\OrderAccountingService();

                        $additionalData = [
                            'deviceID' => $orderSummary["deviceID"],
                            'batchID' => $dataArray['batchID'] ?? null,
                            'channelId' => $dataArray['channelId'] ?? null,
                            'counterID' => $dataArray['counterID'] ?? null,
                            'keepExistingStoreIDFlag' => $keepExistingStoreIDFlag ?? 0,
                            'checkSO' => $checkSO ?? [],
                            'orderID' => $dataArray['orderID'] ?? 'unknown'
                        ];

                        $orderAccounting = $accountingService->generateOrderAccounting(
                            $storeID,
                            $dataArray,
                            $tables,
                            $globalChainAccounting,
                            $getChainSettings,
                            $additionalData
                        );

                        // Log::debug("Order accounting generated successfully", [
                        //     'orderID' => $dataArray['orderID'],
                        //     'storeID' => $storeID,
                        //     'records_generated' => count($orderAccounting),
                        //     'data' => $orderAccounting
                        // ]);
                        // $oldOrderAccounting = OldOrder::createOrderAccounting($storeID, $dataArray, $getChainSettings);
                        // Log::debug("Old order account", [
                        //     'orderID' => $dataArray['orderID'],
                        //     'storeID' => $storeID,
                        //     'records_generated' => count($oldOrderAccounting),
                        //     'data' => $oldOrderAccounting
                        // ]);
                    } catch (\Exception $e) {
                        Log::error('Exception during optimized accounting processing, falling back', [
                            'orderID' => $dataArray['orderID'],
                            'error' => $e->getMessage()
                        ]);
                        $useOptimizedAccounting = false; // Fallback to original
                    }
                }
            }
			if ($enableLogging) {
				Log::info("Order processing - OP: " . json_encode([
					'chainID' => $chainID,
					'storeID' => $storeID,
					'orderID' => $dataArray["orderID"],
				]));
			}


            // Use the optimized OrderPaymentsService for bulk payment processing
            $orderPaymentsService = new \App\Services\OrderPaymentsService();
            try {
                // Pass $checkLoyalty by reference
                $orderPayments = $orderPaymentsService->generateOrderPayments($storeID, $dataArray, $settings, []);
            } catch (\Exception $e) {
                Log::error('Exception during order payment creation: '. $e->getMessage());
                // Fallback to old method
                $orderPayments = OldOrder::createOrderPayments($storeID, $dataArray, $getChainSettings[0]);
            }

            //security deposit
            $securityInvoiceSummaryArray = [];
            $inventoryAssetMappingArray = [];
            if($enableInventoryAssetManagement == 1 && (!isset($dataArray['salesOrderID']) || $dataArray["salesOrderID"] == "" || $dataArray["salesOrderID"] == null)) {
                $securityInvoiceSummaryArray = OldOrder::createSecurityInvoiceSummary($storeID, $dataArray);

                //inventory asset mapping
                for($z = 0;$z<count($dataArray['productsList']);$z++){
                    if(isset($dataArray['productsList'][$z]['rfid']) && $dataArray['productsList'][$z]['rfid'] != null && $dataArray['productsList'][$z]['rfid'] != ""){
                        $inventoryAssetMapping['tagStatus'] = "HOLD";
                        $inventoryAssetMapping['holdTransactionID'] = $dataArray['orderID'];
                        $inventoryAssetMapping['holdTransactionSubID'] = $dataArray['productsList'][$z]['orderSubID'];
                        $inventoryAssetMapping['lastUpdatedAtLocal'] = $dataArray['orderCreationTimeLocal'];
                        $inventoryAssetMapping['lastUpdatedAtUTC'] = localToUTC($dataArray['orderCreationTimeLocal'], $dataArray['timezone']);
                        $inventoryAssetMapping['updationUser'] = $dataArray['billingUsername'];
                        $inventoryAssetMapping['taggedBatchVariantID'] = $dataArray['productsList'][$z]['batchVariantID'];

                        $inventoryAssetMappingArray[] = $inventoryAssetMapping;

                    }
                }
            }



            $creditNoteArray = array();
            $payCount = count($orderPayments);
            $creditNoteIDs = array();
            $updateWalletArray = array();
            $walletActivityTrackerArray = array();
            $walletRequestTracker = array();
            for ($i=0; $i < $payCount; $i++) {
              $paymentType = $orderPayments[$i]["paymentType"];
              $paymentSubID = $orderPayments[$i]["paymentSubID"];
              $transactionID = $orderPayments[$i]["transactionID"];

              if($paymentType == "PAYMENT_CREDIT_NOTE" && (!isset($dataArray['salesOrderID']) || $dataArray["salesOrderID"] == "" || $dataArray["salesOrderID"] == null)){
                $credArray = array("creditNoteID" => $transactionID, "redeemOrderID" => $orderSummary["orderID"], "redeemPaymentSubID" => $paymentSubID,"redeemTimeLocal" => $orderSummary["orderCreationTimeLocal"],"redeemTimeUTC" => $orderSummary["orderCreationTimeUTC"],"redeemAmount" => $orderPayments[$i]["amount"]);
                $creditNoteArray[] = $credArray;
                $creditNoteIDs[] = $transactionID;
              }
              if($enableQBWalletManagement === 1 && $paymentType == "PAYMENT_QB_WALLET"){
                $dataArray["walletPayments"] = $orderPayments[$i];
                $WalletData = QBWallet::consumeWalletBalance($dataArray, $chainID);
                if($WalletData["status"] == false){
                    return $WalletData;
                }
                $updateWalletArray = $WalletData["updateArray"];
                $walletActivityTrackerArray = $WalletData["walletActivityTrackerArray"];
                $walletRequestTracker = $WalletData["walletRequestTracker"];
              } else {
				unset($orderPayments[$i]['previousWalletBalance']);
			  }
            }
            // fetch credit notes
            $getCreditNote = convertToArray(DB::table($creditPaymentSummaryTable)->whereIn('creditNoteID',$creditNoteIDs)->get());
            $modDetail = $orderDetail["modDetail"];
            unset($orderDetail["modDetail"]);

            $comboDetail = $orderDetail["comboDetail"];
            unset($orderDetail["comboDetail"]);

            $membershipDetail = $orderDetail["membershipDetail"];
            $memCount = count($membershipDetail);

            $reservationDetail = $orderDetail["reservationDetail"];
            $resCount = count($reservationDetail);
            unset($orderDetail["reservationDetail"]);

            $voucherDetails = $orderDetail["voucherDetails"];
            unset($orderDetail["voucherDetails"]);

            $giftVoucherDLF = $orderDetail["giftVoucherDLF"];
            unset($orderDetail["giftVoucherDLF"]);

            // get memberships with auto renew details from membershipInfo
            $autorenewMemberships = convertToArray(DB::table($membershipInfoTable)->where("autoRenewMembership", 1)->where('isActive', 1)->get());
            // create a hashmap of membershipID
            $autorenewMembershipsMap = array();
            for ($i=0; $i < count($autorenewMemberships); $i++) {
                $autorenewMembershipsMap[$autorenewMemberships[$i]["membershipID"]] = $autorenewMemberships[$i];
            }

            for ($i=0; $i < $memCount; $i++) {
                $membershipDetail[$i]["customerID"] = $orderSummary["customersIDs"];
                $memProdCount = count($membershipDetail[$i]["memProds"]);
                for ($j=0; $j < $memProdCount; $j++) {
                    $membershipDetail[$i]["memProds"][$j]["customerID"] = $orderSummary["customersIDs"];
                }
                // add autorenew details
                if (isset($autorenewMembershipsMap[$membershipDetail[$i]["membershipID"]])) {
                    $membershipDetail[$i]["autoRenewMembership"] = 1;
                    // get auto renew details
                    $membershipRenewalPeriod = $autorenewMembershipsMap[$membershipDetail[$i]["membershipID"]]["membershipRenewalPeriod"];
                    $validityDuration = $autorenewMembershipsMap[$membershipDetail[$i]["membershipID"]]["validityDuration"];
                    $validityDurationUnit = strtolower($autorenewMembershipsMap[$membershipDetail[$i]["membershipID"]]["validityDurationUnit"]);
                    $renewalLevel = $autorenewMembershipsMap[$membershipDetail[$i]["membershipID"]]["renewalLevel"];
                    // set auto renew expiry date
                    $membershipDetail[$i]["membershipRenewalPeriod"] = $membershipRenewalPeriod;
                    $membershipDetail[$i]["autoRenewalExpiry"] = date("Y-m-d 23:59:59", strtotime($orderSummary["orderCreationTimeLocal"]. " + ".$validityDuration." ".$validityDurationUnit));
                    $membershipDetail[$i]["renewalLevel"] = $renewalLevel;
                    // set renewal due date
                    // if membershipRenewalPeriod is 30 assume it is monthly, 365 assume it is yearly
                    $renewalTimeTemp = $membershipRenewalPeriod . " days";
                    if ($membershipRenewalPeriod == 30) {
                        $renewalTimeTemp = "1 month";
                    } else if ($membershipRenewalPeriod == 365) {
                        $renewalTimeTemp = "1 year";
                    }
                    $membershipDetail[$i]["renewalDueDate"] = date("Y-m-d 00:00:00", strtotime($orderSummary["orderCreationTimeLocal"]. " + ".$renewalTimeTemp));
                    // lifetimeItemCount maxItemsPerOrder allowedCustomerTypes
                    $membershipDetail[$i]["lifetimeItemCount"] = $autorenewMembershipsMap[$membershipDetail[$i]["membershipID"]]["lifetimeItemCount"] || -1;
                    $membershipDetail[$i]["lifetimeItemCountPurchased"] = $autorenewMembershipsMap[$membershipDetail[$i]["membershipID"]]["lifetimeItemCount"] || -1;
                    $membershipDetail[$i]["maxItemsPerOrder"] = $autorenewMembershipsMap[$membershipDetail[$i]["membershipID"]]["maxItemsPerOrder"] || -1;
                    $membershipDetail[$i]["allowedCustomerTypes"] = $autorenewMembershipsMap[$membershipDetail[$i]["membershipID"]]["allowedCustomerTypes"] || 'ALL';
                }
            }

            unset($orderDetail["membershipDetail"]);
            $memTracker = $orderDetail["memTracker"];

            $memTrackCount = count($memTracker);
            $memPurcDetArray = array();
            $memPurcSumArray = array();
            $g = 0;
            $s = 0;

            if ($orderSummary["customersIDs"] > 0) {
              $getCustomerMemPurchases = convertToArray(DB::table($membershipPurchaseDetailTable)->where("customerID", $orderSummary["customersIDs"])->get());
            }else{
              $getCustomerMemPurchases = array();
            }

            $purCount = count($getCustomerMemPurchases);
            for ($i=0; $i < $memTrackCount; $i++) {
              $memTracker[$i]["customerID"] = $orderSummary["customersIDs"];

              for ($j=0; $j < $purCount; $j++) {
                  if ($getCustomerMemPurchases[$j]["customerID"] == $memTracker[$i]["customerID"] &&

                      $getCustomerMemPurchases[$j]["orderID"] == $memTracker[$i]["purchasedOrderID"] && $getCustomerMemPurchases[$j]["productID"] == $memTracker[$i]["productID"] && $getCustomerMemPurchases[$j]["variantID"] == $memTracker[$i]["variantID"]) {
                    $getCustomerMemPurchases[$j]["quantityUsed"]+= $memTracker[$i]["quantity"];
                  $memPurcDetArray[$s] = $getCustomerMemPurchases[$j];
                  $s++;
                  break;
                  }
              }
            }


            unset($orderDetail["memTracker"]);

			if ($enableLogging) {
				Log::info("Order processing - kot: " . json_encode([
					'chainID' => $chainID,
					'storeID' => $storeID,
					'orderID' => $dataArray["orderID"],
				]));
			}
            $kotDetail = OldOrder::createKotDetail($storeID, $dataArray);
            $kotVoidDetail = OldOrder::createKotVoidDetail($storeID, $dataArray);

            //token No
            if($enableTokenGeneration){
                $localOrderCreationTimeLocal = $dataArray["orderCreationTimeLocal"];
                $localDate = date("Y-m-d", strtotime($localOrderCreationTimeLocal));

                $existingTokenDetails = convertToArray(DB::select(DB::raw("SELECT max(tokenNo) AS tokenNo FROM $orderSummaryTable where DATE(orderCreationTimeLocal) = ?"),[$localDate]));
                if(!empty($existingTokenDetails)){
                    $existingTokenNo = $existingTokenDetails[0]['tokenNo'];
                }else{
                    $existingTokenNo = 0;
                }
                $newTokenNo = $existingTokenNo + 1;

                $orderSummary['tokenNo'] = $newTokenNo;
            }

            //sales order
            $updateSalesOrderArray = array();
            $channelSalesOrderConfiguration = array();
            $salesOrderActivityTrackerArray = array();
            if (isset($orderSummary["salesOrderID"]) && !empty($orderSummary["salesOrderID"])) {

                //check for sales order payment
                $channelID = $dataArray['channelId'];
                if (!isset($dataArray['channelId']) && isset($dataArray['channelID'])) {
                    $channelID = $dataArray['channelID'];
                }
                if($channelID > 0){
                    // Channel sales order configuration table is already defined in the tables array from self::getOrderTables
                    $channelSalesOrderConfiguration = convertToArray(DB::table($channelSalesOrderConfigurationTable)->where("channelID",$channelID)->where("isActive",1)->get());
                    $SOPaymentDueTerms = $channelSalesOrderConfiguration[0]['SOPaymentDueTerms'];
                    $SOPaymentDueLimitReference = $channelSalesOrderConfiguration[0]['SOPaymentDueLimitReference'];
                    $SOPaymentDueLimit = $channelSalesOrderConfiguration[0]['SOPaymentDueLimit'];

                    if($SOPaymentDueTerms == "INVOICING"){
                        $paymentListArray = $dataArray['paymentList'];
                        $paymentCount = count($paymentListArray);
                        for($i=0; $i < $paymentCount; $i++){
                            $paymentType = $paymentListArray[$i]["paymentType"];
                            if ($paymentType == "PAYMENT_CREDIT") {
                                $status['status'] = false;
                                $status['message'] = "PAYMENT_CREDIT is not allowed for this sales order";
                                return $status;
                            }
                        }
                    }

                    if($SOPaymentDueLimitReference == "INVOICE"){
                        $paymentDueDate = new DateTime($currentTimeLocal);
                        $paymentDueDate->modify('+' . $SOPaymentDueLimit . ' days');
                        $orderSummary["paymentDueDate"] = $paymentDueDate->format('Y-m-d H:i:s');
                    }
                }

                $totalQuantity = 0;
                for($i = 0; $i < count($salesOrderProductsList); $i++){
                    $totalQuantity += $salesOrderProductsList[$i]['quantityOrdered'];
                }


                if (!empty($checkSO)) {

                    $checkSODetail = convertToArray(DB::table($salesOrderDetailTable)->where("salesOrderID",$orderSummary["salesOrderID"])->get());

                    $salesOrderSubIDArray = [];

                    for($i = 0;$i<count($checkSODetail);$i++){
                        $salesOrderSubIDArray[$checkSODetail[$i]["salesOrderSubID"]] = $checkSODetail[$i];
                    }

                    $updateSalesOrderDetailArray = [];
                    for($i = 0;$i<count($salesOrderProductsList);$i++){

                        if(!isset($salesOrderProductsList[$i]["salesOrderSubID"])){
                            $status["status"] = false;
                            $status["message"] = "Sales Order Sub ID not provided";
                            $responseArray["orderCreationReport"] = $status;
                            return $responseArray;

                        }

                        $remainingSalesOrderSubIDQuantity = $salesOrderSubIDArray[$salesOrderProductsList[$i]["salesOrderSubID"]]['quantityOrdered'] - $salesOrderSubIDArray[$salesOrderProductsList[$i]["salesOrderSubID"]]['invoicedQuantity'];

                        if($salesOrderProductsList[$i]["quantityOrdered"] > $remainingSalesOrderSubIDQuantity){
                            $status["status"] = false;
                            $status["message"] = "Quantity cannot be greater than remaining quantity for sales order sub ID ".$salesOrderProductsList[$i]["salesOrderSubID"];
                            $responseArray["orderCreationReport"] = $status;
                            return $responseArray;
                        }

                        $updateSalesOrderDetailArray[$i]["salesOrderID"] = $orderSummary["salesOrderID"];
                        $updateSalesOrderDetailArray[$i]["salesOrderSubID"] = $salesOrderProductsList[$i]["salesOrderSubID"];
                        $updateSalesOrderDetailArray[$i]["invoicedQuantity"] = $salesOrderSubIDArray[$salesOrderProductsList[$i]["salesOrderSubID"]]['invoicedQuantity'] + $salesOrderProductsList[$i]["quantityOrdered"];

                    }


                    $quantityOrdered = $checkSO[0]["quantityOrdered"];
                    $quantityInvoiced = $checkSO[0]["quantityInvoiced"];

                    if($totalQuantity + $quantityInvoiced > $quantityOrdered){

                        $status["status"] = false;
                        $status["quantityOrdered"] = $quantityOrdered;
                        $status["quantityInvoiced"] = $quantityInvoiced;
                        $status["totalQuantity"] = $totalQuantity;
                        $status["message"] = "Quantity + Invoiced Quantity Cannot be Greater Than Order Quantity";
                        $responseArray["orderCreationReport"] = $status;
                        return $responseArray;

                    }

                    $updateSalesOrderArray['salesOrderID'] = $orderSummary["salesOrderID"];
                    $updateSalesOrderArray['quantityInvoiced'] = $totalQuantity + $quantityInvoiced;
                    $updateSalesOrderArray['invoicedBill'] = $checkSO[0]["invoicedBill"] + $dataArray['grossBill'];

                    if($totalQuantity + $quantityInvoiced == $quantityOrdered){
                        $updateSalesOrderArray['invoiceStatus'] = "INVOICED";
                    }else{
                        $updateSalesOrderArray['invoiceStatus'] = "PARTIAL_INVOICED";
                    }
                }

                //salesOrderActivityTracker
                $salesOrderActivityTrackerArray['storeID'] = $storeID;
                $salesOrderActivityTrackerArray['salesOrderID'] = $orderSummary['salesOrderID'];
                $salesOrderActivityTrackerArray['orderID'] = $orderSummary['orderID'];
                $salesOrderActivityTrackerArray['amount'] = $dataArray['grossBill'];
                $salesOrderActivityTrackerArray['status'] = "INVOICED";
                $salesOrderActivityTrackerArray['user'] = $dataArray["billingUsername"];
                $salesOrderActivityTrackerArray['creationTimeLocal'] = $orderSummary['orderCreationTimeLocal'];
                $salesOrderActivityTrackerArray['creationTimeUTC'] = $orderSummary['orderCreationTimeUTC'];
                $salesOrderActivityTrackerArray['activityType'] = "INVOICE";


            }

            $updateSalesOrderFulfillmentSummaryArray = array();
            if(isset($dataArray["fulfillmentID"]) && !empty($dataArray["fulfillmentID"])){

                $updateSalesOrderFulfillmentSummaryArray['fulfillmentID'] = $dataArray["fulfillmentID"];
                $updateSalesOrderFulfillmentSummaryArray['invoiceStatus'] = "INVOICED";
                $updateSalesOrderFulfillmentSummaryArray["fulfilledOrderID"] = $orderSummary["orderID"];

            }

            $voidDeviceID = $dataArray['voidDeviceID'];
            // $responseArray["orderSummary"] = $orderSummary;
            // $responseArray["orderDetail"] = $orderDetail;
            // $responseArray["orderPayments"] = $orderPayments;
            // $responseArray["orderAccounting"] = $orderAccounting;
            // return $responseArray;
            if($getChainIndustry[0]['enableOnlineBillingOnly'] == 1){
                $accountingExchange = 0.00;
                $accountCount = count($orderAccounting);
                for ($i=0; $i < $accountCount; $i++) {
                    $accountingExchange = round($accountingExchange,2) + round($orderAccounting[$i]['exchange'],2);
                }

                $zeroValue = 1;
                if($merchantIntegrationModule == "Wonderla"){
                    $zeroValue = 0.00;
                }

                if(abs($accountingExchange) > $zeroValue){
                    $status['status'] = false;
                    $status['message'] = "Accounting exchange error, difference amount is $accountingExchange";
                    $status['data'] = $orderAccounting;
                    return $status;
                }
            }

            $serviceDetail = convertToArray(DB::table($serviceInfoTable)->where('isActive',1)->select('serviceID','enablePricingSlabs')->get());
            $serviceMap = [];
            foreach ($serviceDetail as $detail) {
                $serviceMap[$detail['serviceID']] = $detail['enablePricingSlabs'];
            }

            $isNoCharge = $dataArray['isNoCharge'];

			if ($enableLogging) {
				Log::info("Order processing - db trx: " . json_encode([
					'chainID' => $chainID,
					'storeID' => $storeID,
					'orderID' => $dataArray["orderID"],
				]));
			}
			// Set isolation level to REPEATABLE READ
			// DB::statement('SET TRANSACTION ISOLATION LEVEL REPEATABLE READ');

            // Database transaction begin
            DB::beginTransaction();

            $salesOrderID = $dataArray['salesOrderID'];
            if($enableSalesControl == 1 && !isset($salesOrderID) && empty($salesOrderID)){
                $dataArray['type'] = "ORDER";
                $dataArray['enableOnlineBillingOnly'] = $enableOnlineBillingOnly;
                $salesTracker = SalesControl::salesControlTracker($dataArray, $chainID);
                if($salesTracker['status'] == false){
                    DB::rollback();
                    return $salesTracker;
                }
            }

            // $dbtransaction = DB::transaction(function() use ($orderSummaryTable,$vehicleTrackerTable, $vehicleArray, $chainModules, $orderDetailTable, $orderSummary, $orderDetail, $orderAccountingTable, $orderAccounting, $chainOrderSummaryTable, $chainOrderDetailTable,$chainOrderPaymentsTable,$orderPaymentsTable, $orderPayments, $storeID, $customersTable, $customerAccountTable, $PISummaryTable,$orderBatchArray, $orderBatchDetailTable, $discountVoucherTable,$modDetail, $orderModifiersTable, $chainOrderModifiersTable, $industry, $appointmentSummaryTable, $appointmentLogsTable, $kotDetail, $kotVoidDetail, $kotDetailTable, $chainKotDetailTable, $kotVoidDetailTable, $chainKotVoidDetailTable, $creditNoteValidationLogsTable, $orderComboProductsTable, $chainOrderComboProductsTable,$comboDetail, $creditPaymentSummaryTable, $creditNoteArray,$membershipPurchaseSummaryTable,$membershipPurchaseDetailTable,$membershipTrackerTable, $membershipDetail,$memTracker,$memPurcDetArray,$yieldProdArray,$yieldTotalItems,$yieldTotalCost,$stockTransactionSummaryTable,$stockTransactionDetailTable, $chainID, $status,$globalEcomOrderSummaryTable,$enableIRN, $IRNDetail, $ewayDetail,$enableGST,$loyaltyPointsLedgerTable,$timezone, $dataArray, $globalChainAccounting, $chainOrderAccountingTable, $reservationDetail, $resCount, $enableEcomReservation,$enableProductBundle,$enableCurrencyConversion, $chainCustomerAccountTable, $enableCrossStoreCreditNoteRedemption, $enablePartialRedemptionOfCreditNote, $getCreditNote,$currentTimeLocal,$currentTimeUTC,$voucherDetails,
            // $chainInvoiceSummaryTable, $recordChainLevelSalesInvoice, $chainInvoiceDetailTable,$chainInvoicePaymentsTable,$chainInvoiceComboProductsTable,$chainInvoiceModifiersTable, $enableTokenGeneration, $voidDeviceID, $globalEcomStoreSettingsTable, $creditNoteExpiryDays, $enableCreditNoteExpiry, $serviceMap, $orderServicePricingSlabsTable, $enableRRAIntegration, $enableModifier, $enableChannelManagement, $salesOrderSummaryTable, $updateSalesOrderArray, $salesOrderFulfillmentSummaryTable, $updateSalesOrderFulfillmentSummaryArray, $enableQBWalletManagement, $updateWalletArray, $walletActivityTrackerArray, $walletRequestTracker, $listOfStoresTable, $generateInvoiceNumberFromServer, $isNoCharge, $enableOnlineBillingOnly, $channelSalesOrderConfiguration, $storeDetail, $dispatchUPAgriSales, $salesOrderDetailTable, $updateSalesOrderDetailArray, $enableTaxFiling,$enableSourceTax, $salesOrderActivityTrackerTable, $salesOrderActivityTrackerArray, $securityInvoiceSummaryTable, $securityInvoiceSummaryArray, $enableSalesControl,$enableRMSIntegration,$enablePartyMaster,$getChainSettings, $inventoryAssetTagMappingTable, $inventoryAssetMappingArray, $enableAccounting, $customerLedgerTable, $salesOrderID, $keepExistingStoreIDFlag, $checkSO, $crmSettingsTable, $orderDeliveryTrackerTable, $usersTable, $userWalletTable, $walletActivityTrackerTable, $walletMappingTable, $walletRequestTrackerTable)
            // {

                if ($industry != "SPA_AND_SALON") {

                      if (isset($orderSummary["appointmentID"])) {
                          unset($orderSummary["appointmentID"]);
                      }

                }

                if ($industry != "FOOD") {
                    if (isset($orderSummary["tableID"])) {
                      unset($orderSummary["tableID"]);
                    }

                    if (isset($orderSummary["tableName"])) {
                      unset($orderSummary["tableName"]);
                    }

                    if (isset($orderSummary["serverName"])) {
                      unset($orderSummary["serverName"]);
                    }

                    if (isset($orderSummary["paxSize"])) {
                      unset($orderSummary["paxSize"]);
                    }

                }

                if ($industry != "IN_FLIGHT") {

                      if (isset($orderSummary["aircraftID"])) {
                          unset($orderSummary["aircraftID"]);
                      }

                      if (isset($orderSummary["airlineID"])) {
                          unset($orderSummary["airlineID"]);
                      }

                      if (isset($orderSummary["tripID"])) {
                          unset($orderSummary["tripID"]);
                      }

                      if (isset($orderSummary["legID"])) {
                          unset($orderSummary["legID"]);
                      }

                      if (isset($orderSummary["sectorID"])) {
                          unset($orderSummary["sectorID"]);
                      }

                      if (isset($orderSummary["routeID"])) {
                          unset($orderSummary["routeID"]);
                      }

                      if (isset($orderSummary["seatNumber"])) {
                          unset($orderSummary["seatNumber"]);
                      }

                      if (isset($orderSummary["flightNumber"])) {
                          unset($orderSummary["flightNumber"]);
                      }

                      if (isset($orderSummary["airlineName"])) {
                          unset($orderSummary["airlineName"]);
                      }

                      if (isset($orderSummary["ISRNumber"])) {
                          unset($orderSummary["ISRNumber"]);
                      }

                }

                try {
                    if(!isset($orderSummary["customersIDs"]) || $orderSummary["customersIDs"] == null || $orderSummary["customersIDs"] == "") {
                        $orderSummary["loyaltyPointsCollected"] = 0.00;
                    }

                    $MUNBillNumber = "";
                    $MUNChallanNumber = "";

                    if(isset($orderSummary['MUNBillNumber'])){
                        $MUNBillNumber = $orderSummary['MUNBillNumber'];
                        unset($orderSummary['MUNBillNumber']);
                    }
                    if(isset($orderSummary['MUNChallanNumber'])){
                        $MUNChallanNumber = $orderSummary['MUNChallanNumber'];
                        unset($orderSummary['MUNChallanNumber']);
                    }

                    // @todo Need to move to LockableValidator::generateInvoiceNumberWithLock
                    if($generateInvoiceNumberFromServer == 1 && $enableOnlineBillingOnly == 1){
                        $customStoreOrderPrefix = "";
                        if($isNoCharge == 0){
                            // $getStoreData = convertToArray(DB::table($listOfStoresTable)->select('customStoreOrderPrefix','storeTransactionResetDate')->where('storeID',$storeID)->get());
                            $customStoreOrderPrefix = $storeDetail[0]['customStoreOrderPrefix'];
                            $storeTransactionResetDate = $storeDetail[0]['storeTransactionResetDate'];
                            if(isset($storeTransactionResetDate) && !empty($storeTransactionResetDate) && $storeTransactionResetDate != NULL && $storeTransactionResetDate <= $today){

                                $getMaxInvoiceNumber = convertToArray(DB::table($chainInvoiceSummaryTable)->where('storeID',$storeID)->where('isNoCharge',0)->where('isNoInvoiceSale',0)->where('Status','ORST_DELIVERED')->whereDate('orderLogTimeLocal','>=',$storeTransactionResetDate)->select('invoiceNumber')->orderBy('ID','DESC')->limit(1)->lockForUpdate()->get());

                            }else{
                                $getMaxInvoiceNumber = convertToArray(DB::table($chainInvoiceSummaryTable)->where('storeID',$storeID)->where('isNoCharge',0)->where('isNoInvoiceSale',0)->where('Status','ORST_DELIVERED')->select('invoiceNumber')->orderBy('ID','DESC')->limit(1)->lockForUpdate()->get());
                            }

                        }
                        if($isNoCharge == 1){
                            // $getStoreData = convertToArray(DB::table($listOfStoresTable)->select('NCCustomStoreOrderPrefix','storeTransactionResetDate')->where('storeID',$storeID)->get());
                            $customStoreOrderPrefix = $storeDetail[0]['NCCustomStoreOrderPrefix'];
                            $storeTransactionResetDate = $storeDetail[0]['storeTransactionResetDate'];

                            if(isset($storeTransactionResetDate) && !empty($storeTransactionResetDate) && $storeTransactionResetDate != NULL && $storeTransactionResetDate <= $today){
                                $getMaxInvoiceNumber = convertToArray(DB::table($chainInvoiceSummaryTable)->where('storeID',$storeID)->where('isNoCharge',1)->where('isNoInvoiceSale',0)->where('Status','ORST_DELIVERED')->whereDate('orderLogTimeLocal','>=',$storeTransactionResetDate)->select('invoiceNumber')->orderBy('ID','DESC')->limit(1)->lockForUpdate()->get());

                            }else{
                                $getMaxInvoiceNumber = convertToArray(DB::table($chainInvoiceSummaryTable)->where('storeID',$storeID)->where('isNoCharge',1)->where('isNoInvoiceSale',0)->where('Status','ORST_DELIVERED')->select('invoiceNumber')->orderBy('ID','DESC')->limit(1)->lockForUpdate()->get());
                            }

                        }
                        if(empty($getMaxInvoiceNumber)){
                            $lastInvoiceNumber = 0 ;
                        }else{
                            $lastInvoiceNumber = $getMaxInvoiceNumber[0]['invoiceNumber'];
                            $lastInvoiceNumberArray = explode('-', $lastInvoiceNumber);
                            $lastInvoiceNumber = intval($lastInvoiceNumberArray[count($lastInvoiceNumberArray) - 1]);

                        }

                        $nextInvoiceNumber = $lastInvoiceNumber + 1;
                        if($customStoreOrderPrefix != ""){
                            $customInvoiceNumber = $customStoreOrderPrefix.'-'.$nextInvoiceNumber;
                        } else{
                            $customInvoiceNumber = $nextInvoiceNumber;
                        }
                        $orderSummary['invoiceNumber'] = $customInvoiceNumber;
                        self::$finalInvoiceNumber = $customInvoiceNumber;
                    }

                    $temporaryChannelID = null;
                    if (isset($orderSummary['channelID'])) {
                        $temporaryChannelID = $orderSummary['channelID'];
                        unset($orderSummary['channelID']);
                    }
                    $storeOrderSummary = array();
                    $storeOrderSummary = $orderSummary;
                    if($enableSourceTax == 1){
                        if(isset($storeOrderSummary['TDSID'])){
                            unset($storeOrderSummary['TDSID']);
                        }
                        if(isset($storeOrderSummary['TDSPercentage'])){
                            unset($storeOrderSummary['TDSPercentage']);
                        }
                        if(isset($storeOrderSummary['TDSName'])){
                            unset($storeOrderSummary['TDSName']);
                        }
                        if(isset($storeOrderSummary['TDSValue'])){
                            unset($storeOrderSummary['TDSValue']);
                        }

                        if(isset($storeOrderSummary['TCSID'])){
                            unset($storeOrderSummary['TCSID']);
                        }

                        if(isset($storeOrderSummary['TCSPercentage'])){
                            unset($storeOrderSummary['TCSPercentage']);
                        }

                        if(isset($storeOrderSummary['TCSName'])){
                            unset($storeOrderSummary['TCSName']);
                        }

                        if(isset($storeOrderSummary['TCSValue'])){
                            unset($storeOrderSummary['TCSValue']);
                        }

                    }

                    $tempCounterID = null;
                    if(!$enableRMSIntegration){
                        $tempCounterID = $storeOrderSummary['counterID'];
                        unset($storeOrderSummary['counterID']);
                    }


                    DB::table($orderSummaryTable)->insert($storeOrderSummary);
                    if ($temporaryChannelID != null) {
                        $orderSummary['channelID'] = $temporaryChannelID;
                    }
                    if($tempCounterID != null){
                        $orderSummary['counterID'] = $tempCounterID;
                    }
                    if($enableTokenGeneration){
                        unset($orderSummary["tokenNo"]);
                    }
                    $orderSummary["storeID"] = $storeID;
                    if($enableIRN==1){
                        if (!empty($IRNDetail) && $IRNDetail['status'] == true) {
                            $orderSummary['IRNNumber'] = $IRNDetail['IRN'];
                            $orderSummary['irnQRCode'] = $IRNDetail['irnQRCode'];
                            $orderSummary['irnAckNo'] = $IRNDetail['irnDetialArray']['AckNo'];
                            $orderSummary['irnAckDt'] = $IRNDetail['irnDetialArray']['AckDt'];
                            if(isset($IRNDetail['ewaybillsNo']) && $IRNDetail['ewaybillsNo']!=''){
                                $orderSummary['ewayBillNo'] = $IRNDetail['ewaybillsNo'];
                            }
                        }
                    }
                    if($enableGST==1 && $enableIRN!=1 ){
                        if(!empty($ewayDetail) && $ewayDetail['status']==true){
                            $orderSummary['ewayBillNo'] = $ewayDetail['ewaybillNo'];
                        }
                    }
                    if($MUNBillNumber != ''){
                        $orderSummary['MUNBillNumber'] = $MUNBillNumber;
                    }
                    if($MUNChallanNumber != ''){
                        $orderSummary['MUNChallanNumber'] = $MUNChallanNumber;
                    }
                    if($getChainSettings[0]['merchantIntegrationModule'] == 'tataMotors'){
                        $orderSummary['tataMotorsOrderSyncStatus'] = $dataArray['tataMotorsOrderSyncStatus'];
                    }
                    DB::table($chainOrderSummaryTable)->insert($orderSummary);
                } catch(\Illuminate\Database\QueryException $e){
                    DB::rollback();
                    Log::info("duplicate check",["err" => $e,"order" => $orderSummary]);
                    $errorCode = (isset($e->errorInfo) && isset($e->errorInfo[1])) ? $e->errorInfo[1] : '-1';
                    if($errorCode == '1062') {
                        // Duplicate entry
                        $responseArray["errorMessage"] = "Duplicate entry";
                        $responseArray["orderCreationReport"] = $status;
                        $responseArray["orderID"] = $dataArray["orderID"];
                        $responseArray["invoiceNumber"] = $dataArray["invoiceNumber"];
                        //$responseArray["dbError"] = $e->getMessage();
                        return $responseArray;
                    } else {
                        // Some other error ??
                        $responseArray["errorMessage"] = "DB error";
                        $responseArray["orderCreationReport"] = $status;
                        $responseArray['error'] = $e->getMessage();
                        $responseArray["orderID"] = $dataArray["orderID"];
                        $responseArray["invoiceNumber"] = $dataArray["invoiceNumber"];
                        // $responseArray["dbError"] = $e->getMessage();
                        return $responseArray;
                    }
                }

                if ($industry == "SPA_AND_SALON") {
                  $checkTable = convertToArray(DB::statement("SHOW TABLES LIKE '$appointmentSummaryTable'"));
                  if (!empty($checkTable) && isset($orderSummary["appointmentID"]) && !empty($orderSummary["appointmentID"])) {
                    $updateAptArray = array();
                    $updateAptArray["appointmentID"] = $orderSummary["appointmentID"];
                    $updateAptArray["status"] = "COMPLETED";
                    $updateAptArray["linkedOrderID"] = $orderSummary["orderID"];

                    DB::table($appointmentSummaryTable)->where("appointmentID", $updateAptArray["appointmentID"])->update($updateAptArray);


                    $aptLogArray = array();
                    $aptLogArray["appointmentID"] = $updateAptArray["appointmentID"];
                    $aptLogArray["storeID"] = $storeID;
                    $aptLogArray["user"] = $orderSummary["userName"];
                    $aptLogArray["logTimeLocal"] = $orderSummary["orderCreationTimeLocal"];
                    $aptLogArray["logTimeUTC"] = $orderSummary["orderCreationTimeUTC"];
                    $aptLogArray["timezone"] = $orderSummary["timezone"];
                    $aptLogArray["isActive"] = 1;
                    $aptLogArray["status"] = "COMPLETED";

                    DB::table($appointmentLogsTable)->insert($aptLogArray);
                  }
                }
                if (isset($orderSummary["customersIDs"]) && $orderSummary["customersIDs"] != '') {

                    $custPointsData = convertToArray(DB::table($customersTable)->where("customerID", $orderSummary["customersIDs"])->select('pointsCollected','pointsRedeemed','availablePoints')->get());

                    $custArray = array();
                    $orderCustomerID = $orderSummary["customersIDs"];
                    $oldPointsCollected = floatval($custPointsData[0]["pointsCollected"]);
                    $custArray["pointsCollected"] = $oldPointsCollected + floatval($orderSummary["loyaltyPointsCollected"]);
                    $oldPointsRedeemed = floatval($custPointsData[0]["pointsRedeemed"]);
                    $custArray["pointsRedeemed"] = $oldPointsRedeemed + floatval($orderSummary["loyaltyPointsRedeemed"]);
                    $oldAvailablePoints = floatval($custPointsData[0]["availablePoints"]);

                    $loyaltyPaymentKey = array_search('PAYMENT_LOYALTY', array_column($orderPayments, 'paymentType'));

                    if (is_int($loyaltyPaymentKey)) {
                        $custArray["pointsRedeemed"] = $oldPointsRedeemed + floatval($orderPayments[$loyaltyPaymentKey]['transactionID']);
                        $custArray["availablePoints"] = floatval($custArray["pointsCollected"] - $custArray["pointsRedeemed"]);
                    }else{
                        $custArray["availablePoints"] = floatval($custArray["pointsCollected"] - $custArray["pointsRedeemed"]);

                    }

                    self::$finalCustArray["custArray"] = $custArray;
                    self::$finalCustArray["oldPointsCollected"] = $oldPointsCollected;
                    self::$finalCustArray["oldPointsRedeemed"] = $oldPointsRedeemed;

                    $custCreditData = convertToArray(DB::table($customerAccountTable)->where('customerID', $orderCustomerID)->select('amountDue','amountPaid','balance')->get());
                    self::$finalCustCredit["custCreditData"] = $custCreditData;

                    if($enableCrossStoreCreditNoteRedemption == 1){
                        $chainCustCreditData = convertToArray(DB::table($chainCustomerAccountTable)->where('customerID', $orderCustomerID)->select('amountDue','amountPaid','amountSettled','balance')->get());
                        self::$finalChainCustCredit["custCreditData"] = $chainCustCreditData;
                    }

                    if ($custArray["availablePoints"] == $oldAvailablePoints && $custArray["pointsCollected"] == $oldPointsCollected && $custArray["pointsRedeemed"] == $oldPointsRedeemed) {
                        // do not update
                    } else {
                        DB::table($customersTable)->where("customerID", $orderCustomerID)->update($custArray);
                        $custArray["customerID"] = $orderCustomerID;
                        // update customer loyaltyPoint ledger
                        if($orderSummary["loyaltyPointsCollected"] > 0 ){
                            $pointExpiryDays = $settings['chain']['pointsExpiryDays'] ?? 0;
                            $pointExpiryDate = date("Y-m-d H:i:s",strtotime("+".$pointExpiryDays."days"));
                            if ($pointExpiryDays == 0) {
                                $pointExpiryDate = null;
                            }
                            $loyaltyPointLedger = array();
                            $loyaltyPointLedger['customerID'] = $orderCustomerID;
                            $loyaltyPointLedger['storeID'] = $storeID;
                            $loyaltyPointLedger['transactionType'] = 'EARNED';
                            $loyaltyPointLedger['transactionSubType'] = 'ORDER';
                            $loyaltyPointLedger['transactionPoints'] = $orderSummary["loyaltyPointsCollected"];
                            $loyaltyPointLedger['customerBalance'] = $oldAvailablePoints + $orderSummary["loyaltyPointsCollected"];
                            $loyaltyPointLedger['transactionRemarks'] = $dataArray["orderID"];
                            $loyaltyPointLedger['creationTimeLocal'] = date("Y-m-d H:i:s");
                            $loyaltyPointLedger['creationTimeUTC'] = $currentTimeUTC;
                            $loyaltyPointLedger['lastUpdatedAtLocal'] = $currentTimeLocal;
                            $loyaltyPointLedger['lastUpdatedAtUTC'] = $currentTimeUTC;
                            $loyaltyPointLedger['pointsExpiryDate'] = $pointExpiryDate;
                            $loyaltyPointLedger['timezone'] = $timezone;
                            DB::table($loyaltyPointsLedgerTable)->insert($loyaltyPointLedger);
                        }

                        if($orderSummary["loyaltyPointsRedeemed"] > 0 || $orderSummary["loyaltyPointsRedeemed"] == -1){
                            $pointExpiryDays = $settings['chain']['pointsExpiryDays'] ?? 0;
                            $pointExpiryDate = date("Y-m-d H:i:s",strtotime("+".$pointExpiryDays."days"));
                            if ($pointExpiryDays == 0) {
                                $pointExpiryDate = null;
                            }
                            $loyaltyPointLedger = array();
                            $loyaltyPointLedger['customerID'] = $orderCustomerID;
                            $loyaltyPointLedger['storeID'] = $storeID;
                            $loyaltyPointLedger['transactionType'] = 'REDEEMED';
                            $loyaltyPointLedger['transactionSubType'] = 'ORDER';
                            $loyaltyPointLedger['transactionPoints'] = $orderSummary["loyaltyPointsRedeemed"] > 0 ? $orderSummary["loyaltyPointsRedeemed"] : ($custArray["availablePoints"] - $oldAvailablePoints);
                            $loyaltyPointLedger['customerBalance'] = $custArray['availablePoints'];
                            $loyaltyPointLedger['transactionRemarks'] = $dataArray["orderID"];
                            $loyaltyPointLedger['creationTimeLocal'] = date("Y-m-d H:i:s");
                            $loyaltyPointLedger['creationTimeUTC'] = $currentTimeUTC;
                            $loyaltyPointLedger['lastUpdatedAtLocal'] = $currentTimeLocal;
                            $loyaltyPointLedger['lastUpdatedAtUTC'] = $currentTimeUTC;
                            $loyaltyPointLedger['pointsExpiryDate'] = $pointExpiryDate;
                            $loyaltyPointLedger['timezone'] = $timezone;
                            DB::table($loyaltyPointsLedgerTable)->insert($loyaltyPointLedger);
                        }
                    }
                    if( $enableCrossStoreCreditNoteRedemption ==  1 && !empty($chainCustCreditData)){
                        $chainCustAccountArray = array();
                        $chainCustAccountArray["amountDue"] = $chainCustCreditData[0]["amountDue"] + self::$finalCreditAmount;
                        $chainCustAccountArray["balance"] = $chainCustCreditData[0]["balance"] + self::$finalCreditAmount;
                        $chainCustAccountArray["amountSettled"] = $chainCustCreditData[0]["amountSettled"] + self::$finalSettledAmount;
                        DB::table($chainCustomerAccountTable)->where('customerID', $orderCustomerID)->update($chainCustAccountArray);
                        self::$finalCustCredit["chainCustAccountArray"] = $chainCustAccountArray;
                    }

                    if (!empty($custCreditData)) {
                        $custAccountArray = array();
                        $custAccountArray["amountDue"] = $custCreditData[0]["amountDue"] + self::$finalCreditAmount;
                        $custAccountArray["balance"] = $custCreditData[0]["balance"] + self::$finalCreditAmount;

                        if ($custAccountArray["balance"] == $custCreditData[0]["balance"]) {
                            // do not update
                        } else {
                            DB::table($customerAccountTable)->where('customerID', $orderCustomerID)->update($custAccountArray);
                            $custAccountArray["customerID"] = $orderCustomerID;
                            self::$finalCustCredit["custAccountArray"] = $custAccountArray;

                            // Update order status
                            if (floatval($custCreditData[0]["balance"]) < 0) {
                                if (floatval(0 - $custCreditData[0]["balance"]) >= floatval($orderSummary["grossBill"])) {
                                    // The order is PAID with customer's balance
                                    $orderSummary["paymentStatus"] = 'PAID';
                                    $updateOrdArray = array("paymentStatus" => "PAID");
                                    // DB::table($chainOrderSummaryTable)->where("orderID", $orderSummary["orderID"])->update($updateOrdArray);
                                    // DB::table($orderSummaryTable)->where("orderID", $orderSummary["orderID"])->update($updateOrdArray);
                                } else {
                                    // The order is PARTIALLY_PAID with customer's balance
                                    $orderSummary["paymentStatus"] = 'PARTIALLY_PAID';
                                    $updateOrdArray = array("paymentStatus" => "PARTIALLY_PAID");
                                    // DB::table($chainOrderSummaryTable)->where("orderID", $orderSummary["orderID"])->update($updateOrdArray);
                                    // DB::table($orderSummaryTable)->where("orderID", $orderSummary["orderID"])->update($updateOrdArray);
                                }
                            }
                        }

                    }

                }


                if (isset($orderSummary["PINumber"]) && !empty($orderSummary["PINumber"])) {
                    $updatePIArray = array();
                    $checkPI = convertToArray(DB::table($PISummaryTable)->where("proformaInvoiceID",$orderSummary["PINumber"])->get());
                    if (!empty($checkPI)) {
                      if ($checkPI[0]["PIType"] == "ONLINE_ORDER") {
                        $updatePIArray["status"] = "Dispatched";
                      }elseif($checkPI[0]["PIType"] == "QB_ONLINE_ORDER"){
                        $updatePIArray["status"] = "Invoiced";
                      }else{
                        $updatePIArray["status"] = "PIST_FULFILLED";
                      }

                      $updatePIArray["proformaInvoiceID"] = $orderSummary["PINumber"];
                      $updatePIArray["fulfilledOrderID"] = $orderSummary["orderID"];

                      // update the  orderID instead of PINumber on redeem orderID in credit payment summry and credit validation log
                      $globalEcomStoreSettingsDetail = convertToArray(DB::table($globalEcomStoreSettingsTable)->where('chainID', $chainID)->where('storeID', $storeID)->where('isActive', 1)->get());
                      $allowCreditNoteSettlement = $globalEcomStoreSettingsDetail[0]['allowCreditNoteSettlement'];
                      if($allowCreditNoteSettlement ==1){
                        // Credit note validation logs and credit payment summary tables are already defined in the tables array from self::getOrderTables
                        $getCreditNoteDetail = convertToArray(DB::table($creditNoteValidationLogsTable)->select('creditNoteID')->where('orderID',$orderSummary["PINumber"])->where('status','ACTIVE')->get());
                        if(!empty($getCreditNoteDetail)){
                            $creditNoteIDs = array_column($getCreditNoteDetail,'creditNoteID');
                            if(isset($dataArray['salesOrderID']) && $dataArray['salesOrderID'] != ''){
                                $updateCreditNote = array();
                                $updateCreditNoteLog['orderID'] = $orderSummary["orderID"];
                                DB::table($creditNoteValidationLogsTable)->whereIn('creditNoteID',$creditNoteIDs)->update($updateCreditNoteLog);
                                DB::table($creditPaymentSummaryTable)->where('creditNoteID',$creditNoteID)->update($updateCreditNote);
                            }
                        }
                      }

                      DB::table($PISummaryTable)->where('proformaInvoiceID', $updatePIArray["proformaInvoiceID"])->update($updatePIArray);

                      if ($checkPI[0]['PIType'] == "QB_ONLINE_ORDER") {
                        $updatePIArray["deliveryStatus"] = $updatePIArray["status"];

                        DB::table($globalEcomOrderSummaryTable)->where('proformaInvoiceID', $updatePIArray["proformaInvoiceID"])->update($updatePIArray);

                      }
                    }

                      if (!empty($checkPI) && $checkPI[0]["PIType"] == "ONLINE_ORDER") {
                       // Order delivery tracker table is already defined in the tables array from TableRegistry::getOrderTables

                       $orderDeliveryTrackerArray = array();
                       $orderDeliveryTrackerArray['storeID'] = $storeID;
                       $orderDeliveryTrackerArray['proformaInvoiceID'] = $updatePIArray["proformaInvoiceID"];
                       $orderDeliveryTrackerArray['orderStatus'] = $updatePIArray["status"];
                       $orderDeliveryTrackerArray['productCount'] = count($orderDetail);
                       $orderDeliveryTrackerArray['amount'] = $orderSummary['grossBill'];
                       $orderDeliveryTrackerArray['logTimeLocal']=$orderSummary["orderCreationTimeLocal"];
                       $orderDeliveryTrackerArray['logTImeUTC']=$orderSummary["orderCreationTimeUTC"];
                       $orderDeliveryTrackerArray['timezone'] = $orderSummary["timezone"];
                       $orderDeliveryTrackerArray['isActive'] = 1;
                       $orderDeliveryTrackerArray['userID'] = $billingUserID;


                        DB::table($orderDeliveryTrackerTable)->insert($orderDeliveryTrackerArray);
                      } else if (!empty($checkPI) && $checkPI[0]["PIType"] == "QB_ONLINE_ORDER") {


                        // $qbPocketArray = array();
                        // $qbPocketArray['proformaInvoiceID'] = $orderSummary["PINumber"];
                        // $qbPocketArray['orderStatus'] = ($checkPI[0]["dummy2"] > 0) ? "DISPATCHED" : "DELIVERED";
                        // $qbPocketArray['productCount'] = count($orderDetail);
                        // $qbPocketArray['amount'] = $orderSummary['grossBill'];
                        // $qbPocketArray['storeID'] = $storeID;
                        // $qbPocketArray['chainID'] = $chainID;
                        // $queueNumber = queueNumber($chainID);
                        // $qbPocketUpdateJob = (new EcomOrderUpdateJob($qbPocketArray))->onQueue("$queueNumber");
                        // dispatch($qbPocketUpdateJob);
                        // $responseArray["qbPocketUpdateJob"] = $qbPocketUpdateJob;

                        // Order delivery tracker table is already defined in the tables array from TableRegistry::getOrderTables

                        $orderDeliveryTrackerArray = array();
                        $orderDeliveryTrackerArray['storeID'] = $storeID;
                        $orderDeliveryTrackerArray['proformaInvoiceID'] = $updatePIArray["proformaInvoiceID"];
                        $orderDeliveryTrackerArray['orderStatus'] = "Invoiced";
                        $orderDeliveryTrackerArray['productCount'] = count($orderDetail);
                        $orderDeliveryTrackerArray['amount'] = $orderSummary['grossBill'];
                        $orderDeliveryTrackerArray['logTimeLocal']=$orderSummary["orderCreationTimeLocal"];
                        $orderDeliveryTrackerArray['logTImeUTC']=$orderSummary["orderCreationTimeUTC"];
                        $orderDeliveryTrackerArray['timezone'] = $orderSummary["timezone"];
                        $orderDeliveryTrackerArray['isActive'] = 1;
                        $orderDeliveryTrackerArray['userID'] = $billingUserID;
                        DB::table($orderDeliveryTrackerTable)->insert($orderDeliveryTrackerArray);

                      }
                }

                //sales Order
                if(!empty($updateSalesOrderArray)){

                    if($updateSalesOrderArray['quantityInvoiced'] == $checkSO[0]["quantityOrdered"]){
                        $updateSalesOrderArray['invoiceStatus'] = "INVOICED";
                    }else{
                        $updateSalesOrderArray['invoiceStatus'] = "PARTIAL_INVOICED";
                    }

                    DB::table($salesOrderSummaryTable)->where('salesOrderID', $updateSalesOrderArray["salesOrderID"])->update($updateSalesOrderArray);
                }

                if(!empty($updateSalesOrderFulfillmentSummaryArray)){

                    DB::table($salesOrderFulfillmentSummaryTable)->where('fulfillmentID', $updateSalesOrderFulfillmentSummaryArray["fulfillmentID"])->update($updateSalesOrderFulfillmentSummaryArray);
                }

                if(!empty($updateSalesOrderDetailArray)){
                   for($i = 0;$i<count($updateSalesOrderDetailArray);$i++){
                        DB::table($salesOrderDetailTable)->where('salesOrderID', $updateSalesOrderDetailArray[$i]["salesOrderID"])->where('salesOrderSubID', $updateSalesOrderDetailArray[$i]["salesOrderSubID"])->update(['invoicedQuantity' => $updateSalesOrderDetailArray[$i]['invoicedQuantity']]);
                   }
                }

                if(!empty($salesOrderActivityTrackerArray)){
                    DB::table($salesOrderActivityTrackerTable)->insert($salesOrderActivityTrackerArray);
                }

                for($i = 0;$i<count($securityInvoiceSummaryArray);$i++){
                    DB::table($securityInvoiceSummaryTable)->insert($securityInvoiceSummaryArray[$i]);
                }

                for($i = 0;$i<count($inventoryAssetMappingArray);$i++){
                    DB::table($inventoryAssetTagMappingTable)->where('isActive',1)->where('taggedBatchVariantID',$inventoryAssetMappingArray[$i]['taggedBatchVariantID'])->update($inventoryAssetMappingArray[$i]);
                }

                // Order Details Bulk Insertion Implementation
                $countOf = count($orderDetail);

                // Feature flag for bulk insertion (default to true, can be disabled for specific chains)
                $useBulkInsertion = true;

                try {
                    if ($useBulkInsertion && $countOf > 0) {
                        try {
                            // // Use new bulk insertion approach
                            // $bulkSettings = [
                            //     'industry' => $industry,
                            //     'enableModifier' => $enableModifier,
                            //     'enableProductBundle' => $enableProductBundle
                            // ];
                            
                            $bulkResult = $orderDetailBuilder->insertOrderDetailsBulk(
                                $orderDetail, $serviceMap
                            );
                            
                            if (!$bulkResult['status']) {
                                Log::error('Bulk insertion failed, falling back to individual inserts', [
                                    'orderID' => $dataArray['orderID'],
                                    'error' => $bulkResult['error']
                                ]);
                                $useBulkInsertion = false; // Fallback to original
                            }
                        } catch (\Exception $e) {
                            Log::error('Exception during bulk insertion, falling back to individual inserts', [
                                'orderID' => $dataArray['orderID'],
                                'error' => $e->getMessage()
                            ]);
                            $useBulkInsertion = false; // Fallback to original
                        }
                    }
                    if (!$useBulkInsertion) {
                        // Original individual insertion approach
                        for ($i = 0; $i < $countOf; $i++) {
                            if($industry != "FOOD" && !$enableModifier){
                                if (isset($orderDetail[$i]["totalModifierValue"])) {
                                    unset($orderDetail[$i]["totalModifierValue"]);
                                }

                                unset($orderDetail[$i]["kotNumber"]);

                                if (isset($orderDetail[$i]["numKotPrint"])) {
                                    unset($orderDetail[$i]["numKotPrint"]);
                                }

                                if (isset($orderDetail[$i]["kotRemarks"])) {
                                    unset($orderDetail[$i]["kotRemarks"]);
                                }
                            }

                            if ($industry != "SPA_AND_SALON") {
                                if (isset($orderDetail[$i]["startTimeLocal"])) {
                                    unset($orderDetail[$i]["startTimeLocal"]);
                                }

                                if (isset($orderDetail[$i]["startTimeUTC"])) {
                                    unset($orderDetail[$i]["startTimeUTC"]);
                                }

                                if (isset($orderDetail[$i]["endTimeLocal"])) {
                                    unset($orderDetail[$i]["endTimeLocal"]);
                                }

                                if (isset($orderDetail[$i]["endTimeUTC"])) {
                                    unset($orderDetail[$i]["endTimeUTC"]);
                                }

                                if (isset($orderDetail[$i]["serviceDuration"])) {
                                    unset($orderDetail[$i]["serviceDuration"]);
                                }

                                if (isset($orderDetail[$i]["serviceDurationUnit"])) {
                                    unset($orderDetail[$i]["serviceDurationUnit"]);
                                }

                                if (isset($orderDetail[$i]["staff"])) {
                                    unset($orderDetail[$i]["staff"]);
                                }
                            }

                            if ($industry != "IN_FLIGHT") {
                            if (isset($orderDetail[$i]["tripID"])) {
                                unset($orderDetail[$i]["tripID"]);
                            }

                            if (isset($orderDetail[$i]["legID"])) {
                                unset($orderDetail[$i]["legID"]);
                            }
                            }

                            if($enableProductBundle == 0){
                                unset($orderDetail[$i]["bundleID"]);
                                unset($orderDetail[$i]["bundleName"]);
                            }

                            DB::table($orderDetailTable)->insert($orderDetail[$i]);

                            $orderDetail[$i]["storeID"] = $storeID;
                            DB::table($chainOrderDetailTable)->insert($orderDetail[$i]);

                            $enableServicePricing = $serviceMap[$orderDetail[$i]['productID']];
                            if($enableServicePricing){
                                $insertArray = array();
                                $insertArray['productID'] = $orderDetail[$i]['productID'];
                                $insertArray['variantID'] = $orderDetail[$i]['variantID'];
                                $insertArray['storeID'] = $orderDetail[$i]['storeID'];
                                $insertArray['orderID'] = $orderDetail[$i]['orderID'];
                                $insertArray['orderSubID'] = $orderDetail[$i]['orderSubID'];
                                $insertArray['JSON'] = json_encode($dataArray["productsList"][$i]['servicePricingSlabList']);
                                $insertArray['posDate'] = $orderDetail[$i]['posDate'];
                                $insertArray['isActive'] = 1;
                                DB::table($orderServicePricingSlabsTable)->insert($insertArray);
                            }
                        }
                    }
                } catch(\Illuminate\Database\QueryException $e){
                    DB::rollback();
                    Log::error("Order detail insertion failed", [
                        "error" => $e->getMessage(),
                        "chainID" => $chainID,
                        "storeID" => $storeID,
                        "orderID" => $dataArray["orderID"],
                        "errorCode" => $e->errorInfo[1] ?? 'unknown',
                        "using_bulk" => $useBulkInsertion ?? false
                    ]);

                    $errorCode = (isset($e->errorInfo) && isset($e->errorInfo[1])) ? $e->errorInfo[1] : '-1';
                    if($errorCode == '1062') {
                        // Duplicate entry
                        $responseArray["status"] = false;
                        $responseArray["errorMessage"] = "Duplicate entry in order detail.";
                        $responseArray["orderCreationReport"] = $status;
                        $responseArray["orderID"] = $dataArray["orderID"];
                        $responseArray["invoiceNumber"] = $dataArray["invoiceNumber"];
                        return $responseArray;
                    } else {
                        // Some other error
                        $responseArray["status"] = false;
                        $responseArray["errorMessage"] = "DB error during order detail insertion";
                        $responseArray['error'] = $e->getMessage();
                        $responseArray["orderCreationReport"] = $status;
                        $responseArray["orderID"] = $dataArray["orderID"];
                        $responseArray["invoiceNumber"] = $dataArray["invoiceNumber"];
                        return $responseArray;
                    }
                } catch(\Exception $e) {
                    DB::rollback();
                    Log::error("Order detail processing failed", [
                        "error" => $e->getMessage(),
                        "chainID" => $chainID,
                        "storeID" => $storeID,
                        "orderID" => $dataArray["orderID"],
                        "using_bulk" => $useBulkInsertion ?? false,
                        "trace" => $e->getTraceAsString()
                    ]);

                    $responseArray["status"] = false;
                    $responseArray["errorMessage"] = "Error processing order details: " . $e->getMessage();
                    $responseArray["orderCreationReport"] = $status;
                    $responseArray["orderID"] = $dataArray["orderID"];
                    $responseArray["invoiceNumber"] = $dataArray["invoiceNumber"];
                    return $responseArray;
                }

                  if (!empty($comboDetail)) {
                      $comboCount = count($comboDetail);
                      for ($i=0; $i < $comboCount; $i++) {
                         DB::table($orderComboProductsTable)->insert($comboDetail[$i]);
                         $comboDetail[$i]["storeID"] = $storeID;
                         DB::table($chainOrderComboProductsTable)->insert($comboDetail[$i]);
                      }
                  }

                //modifiers entry

                if ($industry == "FOOD" || $enableModifier) {
                    if (!empty($modDetail)) {
                        $modCount = count($modDetail);
                        for ($i=0; $i < $modCount; $i++) {
                           DB::table($orderModifiersTable)->insert($modDetail[$i]);
                           $modDetail[$i]["storeID"] = $storeID;
                           DB::table($chainOrderModifiersTable)->insert($modDetail[$i]);
                        }
                    }

                    if (!empty($kotDetail)) {
                      $kotCount = count($kotDetail);
                      for ($i=0; $i < $kotCount; $i++) {
                         DB::table($kotDetailTable)->insert($kotDetail[$i]);
                         $kotDetail[$i]["storeID"] = $storeID;
                         DB::table($chainKotDetailTable)->insert($kotDetail[$i]);
                      }

                    }

                    if (!empty($kotVoidDetail)) {
                      $kotCount = count($kotVoidDetail);
                      for ($i=0; $i < $kotCount; $i++) {
                         DB::table($kotVoidDetailTable)->insert($kotVoidDetail[$i]);
                         $kotVoidDetail[$i]["storeID"] = $storeID;
                         DB::table($chainKotVoidDetailTable)->insert($kotVoidDetail[$i]);
                      }

                    }

                }

                if (!empty($membershipDetail)) {
                    for ($i=0; $i < count($membershipDetail); $i++) {
                        $memProdArray = $membershipDetail[$i]["memProds"];
                        unset($membershipDetail[$i]["memProds"]);
                        $memTransID = DB::table($membershipPurchaseSummaryTable)->insertGetId($membershipDetail[$i]);
                        // array_push(self::$finalMembershipGUID, $membershipDetail[$i]['membershipGUID']);
                        $tempMemTracker = array();
                        $tempMemTracker['subId']          = $membershipDetail[$i]['orderSubID'];
                        $tempMemTracker['membershipGUID'] = $membershipDetail[$i]['membershipGUID'];
                        array_push(self::$finalMembershipGUID, $tempMemTracker);

                        for ($j=0; $j < count($memProdArray); $j++) {
                            $memProdArray[$j]["transactionID"] = $memTransID;
                            DB::table($membershipPurchaseDetailTable)->insert($memProdArray[$j]);
                        }
                    }
                }

                if (!empty($memTracker)) {
                    for ($i=0; $i < count($memTracker); $i++) {
                        DB::table($membershipTrackerTable)->insert($memTracker[$i]);
						$tempDiscountUsed = $memTracker[$i]['discountTotalValue'] * 1.0000;
						$lifetimeItemCountUsed = $memTracker[$i]['quantity'] * 1.0000;
                        DB::select(DB::raw("UPDATE $membershipPurchaseSummaryTable SET totalRedemptionAmount = totalRedemptionAmount + ?, lifetimeItemCount = lifetimeItemCount - ? WHERE membershipGUID = ?;"), [$tempDiscountUsed, $lifetimeItemCountUsed, $memTracker[$i]['membershipGUID']]);
                    }
                }


                $memTransArray = array();
                if (!empty($memPurcDetArray)) {
                    for ($i=0; $i < count($memPurcDetArray); $i++) {
                        $transactionID = $memPurcDetArray[$i]["transactionID"];
                        if (!in_array($transactionID, $memTransArray)) {
                            array_push($memTransArray, $transactionID);
                        }
                        //  ID unset
                        unset($memPurcDetArray[$i]["ID"]);
                        DB::table($membershipPurchaseDetailTable)->where("customerID", $memPurcDetArray[$i]["customerID"])->where("storeID", $memPurcDetArray[$i]["storeID"])->where("orderID", $memPurcDetArray[$i]["orderID"])->where("productID", $memPurcDetArray[$i]["productID"])->where("variantID", $memPurcDetArray[$i]["variantID"])->update($memPurcDetArray[$i]);
                    }
                }

                if (!empty($memTransArray)) {
                  $memTransList = rtrim(implode(",", $memTransArray));

                  $getMemDetail =  convertToArray(DB::select(DB::raw("SELECT * FROM $membershipPurchaseDetailTable WHERE transactionID IN ($memTransList) ORDER BY transactionID")));
                  $memCount = count($getMemDetail);
                  $memUpdateArray = array();
                  $s = 0;
                  $tempTransID = NULL;
                  $flag = 0;
                  for ($i=0; $i < $memCount; $i++) {
                    $transactionID = $getMemDetail[$i]["transactionID"];
                    if($tempTransID == NULL){
                      $tempTransID = $transactionID;
                    }

                    if ($getMemDetail[$i]["quantityPurchased"] > $getMemDetail[$i]["quantityUsed"] && $tempTransID == $transactionID) {
                      $flag = 1;
                    }elseif ($getMemDetail[$i]["quantityPurchased"] <= $getMemDetail[$i]["quantityUsed"] && $flag == 1) {
                      if ($tempTransID != $transactionID) {
                        $tempTransID = $transactionID;
                        $flag = 0;
                      }else{
                        continue;
                      }

                    }elseif ($getMemDetail[$i]["quantityPurchased"] <= $getMemDetail[$i]["quantityUsed"] && $flag == 0) {
                      if ($tempTransID != $transactionID) {
                        $tempTransID = $transactionID;
                        $memUpdateArray[] = array("ID" => $transactionID,"membershipStatus" => "COMPLETED");
                        $flag = 0;
                      }else{
                        continue;
                      }

                    }
                  }

                }
                if (!empty($memUpdateArray)) {
                  for ($i=0; $i < count($memUpdateArray); $i++) {
                    DB::table($membershipPurchaseSummaryTable)->where("ID", $memUpdateArray[$i]["ID"])->update($memUpdateArray[$i]);

                  }
                }



                // Use the same flag that was set earlier to avoid duplicate logic
                if ($useOptimizedAccounting) {
                    // Use new optimized accounting service (generation + bulk insertion) - Phase 1
                    try {
                        $accountingResult = $accountingService->insertOrderAccountingBulk(
                            $orderAccounting,
                            $storeID,
                            $tables,
                            $globalChainAccounting,
                            $additionalData,
                            $getChainSettings
                        );

                        // $accountingResult = $accountingService->generateAndInsertOrderAccounting(
                        //     $storeID,
                        //     $dataArray,
                        //     $tables,
                        //     $globalChainAccounting,
                        //     $getChainSettings,
                        //     $additionalData
                        // );

                        if (!$accountingResult['status']) {
                            Log::error('Optimized accounting processing failed, falling back to original', [
                                'orderID' => $dataArray['orderID'],
                                'error' => $accountingResult['error'] ?? 'Unknown error'
                            ]);
                            $useOptimizedAccounting = false; // Fallback to original
                        }
                    } catch (\Exception $e) {
                        Log::error('Exception during optimized accounting processing, falling back', [
                            'orderID' => $dataArray['orderID'],
                            'error' => $e->getMessage()
                        ]);
                        $useOptimizedAccounting = false; // Fallback to original
                    }
                }

                if (!$useOptimizedAccounting) {
                    // Original approach: Generate accounting records then insert individually
                    $countOf = count($orderAccounting);

                    // Add required fields to all accounting records
                    for ($i = 0; $i < $countOf; $i++) {
                        $orderAccounting[$i]["accountDeviceID"] = $orderSummary["deviceID"];
                        if(isset($dataArray['batchID']) && !empty($dataArray['batchID'])){
                            $orderAccounting[$i]["batchID"] = $dataArray['batchID'];
                        }
                    }

                    // Original individual insertion approach
                    for ($i = 0; $i < $countOf; $i++) {
                        unset($orderAccounting[$i]["storeID"]);
                        DB::table($orderAccountingTable)->insert($orderAccounting[$i]);
                        // check if global chain accounting is eanbled record accounting in chain accounting
                        if($globalChainAccounting == 1){
                            if(!$keepExistingStoreIDFlag){
                                $orderAccounting[$i]['storeID'] = $storeID;
                            }else{
                                if($orderAccounting[$i]['debit'] > 0){
                                    $orderAccounting[$i]['storeID'] = $checkSO[0]['storeID'];
                                }else{
                                    $orderAccounting[$i]['storeID'] = $storeID;
                                }
                            }
                            $orderAccounting[$i]['channelID'] = $dataArray['channelId'];
                            $orderAccounting[$i]['counterID'] = $dataArray['counterID'];
                            DB::table($chainOrderAccountingTable)->insert($orderAccounting[$i]);
                            unset($orderAccounting[$i]['storeID']);
                            unset($orderAccounting[$i]['channelID']);
                            unset($orderAccounting[$i]['counterID']);
                        }
                    }
                }

                if($dataArray["salesOrderID"] != NULL && $dataArray["salesOrderID"] != ""){

                    $isValid = false;
                    if($channelSalesOrderConfiguration[0]['SOPaymentDueTerms'] == "CREDIT_SALE"){
                        for($i = 0; $i < count($dataArray['paymentList']); $i++){
                            $isValid = true;
                            break;
                        }
                    }



                    if($isValid || $channelSalesOrderConfiguration[0]['SOPaymentDueTerms'] == "INVOICING"){
                        $salesOrderCreditSettlementArray = array();
                        $salesOrderCreditSettlementArray["storeID"] = $storeID;
                        $salesOrderCreditSettlementArray['customerID'] = $dataArray['customers'][0]['customerID'];
                        $salesOrderCreditSettlementArray['userName'] = $dataArray['userName'];
                        $salesOrderCreditSettlementArray['posDate'] = $dataArray['posDate'];
                        $salesOrderCreditSettlementArray['paymentList'] = $dataArray['paymentList'];

                        $salesOrderTotalAmountPaid = 0;
                        $countSalesOrderCreditSettlementArray['paymentList'];
                        for($s = 0;$s < $countSalesOrderCreditSettlementArray;$s++){
                            $salesOrderTotalAmountPaid += $countSalesOrderCreditSettlementArray['paymentList'][$s]['amount'];
                        }

                        $salesOrderCreditSettlementArray["totalAmountPaid"] = $salesOrderTotalAmountPaid;
                        $salesOrderCreditSettlementArray["salesOrderIDArray"] = [$salesOrderID];
                        $salesOrderCreditSettlementArray["billSettledTimeLocal"] = $dataArray["orderCreationTimeLocal"];
                        $salesOrderCreditSettlementArray["timezone"] = $dataArray["timezone"];
                        $salesOrderCreditSettlementArray["deviceID"] = $dataArray["deviceID"];
                        $salesOrderCreditSettlementArray["updateCustomerAccountBal"] = 1;
                        $salesOrderCreditSettlementArray["orderID"] = $orderID;

                        Log::info("chainID: ".$chainID." || salesOrderCreditSettlementArray Request",$salesOrderCreditSettlementArray);
                        $salesOrderCreditSettlementResponse = SalesOrder::salesOrderCreditSettlement($chainID,$salesOrderCreditSettlementArray);
                        Log::info("chainID: ".$chainID." || creditSettlementResponse Response",$salesOrderCreditSettlementResponse);
                    }

                }

                /** Handle for tax filing start */
                if($enableTaxFiling == 1){
                    $taxFilingArray = array();
                    $taxFilingArray['storeID'] = $storeID;
                    $taxFilingArray['orderID'] = $orderSummary['orderID'];
                    $taxFilingArray['transactionType'] = 'SALES_INVOICE';

                    $posTax = TaxFiling::postTaxTracker($taxFilingArray,$chainID);
                }
                /** Handle for tax filing end */


                $useBulkPaymentInsertion = true;
                if ($useBulkPaymentInsertion && count($orderPayments) > 0) {
                    try {
                        // Use new bulk insertion approach
                        $bulkResult = $orderPaymentsService->insertOrderPayments(
                            $storeID, $orderPayments, $orderPaymentsTable, $chainOrderPaymentsTable
                        );

                        if (!$bulkResult['status']) {
                            Log::error('Bulk payment insertion failed, falling back to individual inserts', [
                                'orderID' => $dataArray['orderID'],
                                'error' => $bulkResult['error']
                            ]);
                            $useBulkPaymentInsertion = false; // Fallback to original
                        } else {
                            // Process payment-specific business logic after successful bulk insertion
                            try {
                                $paymentLogicResult = $orderPaymentsService->processPaymentSpecificLogic(
                                    $orderPayments, $orderSummary, $dataArray,
                                    ['chain' => $getChainSettings[0]],
                                    [
                                        'discountVoucherTable' => $discountVoucherTable,
                                        'creditNoteValidationLogsTable' => $creditNoteValidationLogsTable
                                    ],
                                    $chainID
                                );

                                if (!$paymentLogicResult['status']) {
                                    Log::error('Payment-specific logic processing failed', [
                                        'orderID' => $dataArray['orderID'],
                                        'error' => $paymentLogicResult['message'] ?? 'Unknown error'
                                    ]);
                                    // Note: Transaction rollback is handled within the service method
                                    return $paymentLogicResult;
                                }
                            } catch (\Exception $e) {
                                Log::error('Exception during payment-specific logic processing', [
                                    'orderID' => $dataArray['orderID'],
                                    'error' => $e->getMessage()
                                ]);
                                DB::rollback();
                                return [
                                    'status' => false,
                                    'message' => 'Payment-specific logic processing failed: ' . $e->getMessage()
                                ];
                            }
                        }
                    } catch (\Exception $e) {
                        Log::error('Exception during bulk payment insertion, falling back to individual inserts', [
                            'orderID' => $dataArray['orderID'],
                            'error' => $e->getMessage()
                        ]);
                        $useBulkPaymentInsertion = false; // Fallback to original
                    }
                }

                if (!$useBulkPaymentInsertion) {
                    $countOf = count($orderPayments);
                    for ($i = 0; $i < $countOf; $i++) {
                        if($enableCurrencyConversion == 1){
                            if (isset($orderPayments[$i]["shortAmount"])) {
                                unset($orderPayments[$i]["shortAmount"]);
                            }
                            if (isset($orderPayments[$i]["tripID"])) {
                                unset($orderPayments[$i]["tripID"]);
                            }

                            if (isset($orderPayments[$i]["legID"])) {
                                unset($orderPayments[$i]["legID"]);
                            }
                        }
                        if ($enableCurrencyConversion == 0) {
                            if (isset($orderPayments[$i]["tripID"])) {
                                unset($orderPayments[$i]["tripID"]);
                            }

                            if (isset($orderPayments[$i]["legID"])) {
                                unset($orderPayments[$i]["legID"]);
                            }

                            if (isset($orderPayments[$i]["tenderedAmount"])) {
                                unset($orderPayments[$i]["tenderedAmount"]);
                            }

                            if (isset($orderPayments[$i]["shortAmount"])) {
                                unset($orderPayments[$i]["shortAmount"]);
                            }

                            if (isset($orderPayments[$i]["tenderedAmountCurrency"])) {
                                unset($orderPayments[$i]["tenderedAmountCurrency"]);
                            }

                            if (isset($orderPayments[$i]["tenderedAmountCurrencyCF"])) {
                                unset($orderPayments[$i]["tenderedAmountCurrencyCF"]);
                            }

                            if (isset($orderPayments[$i]["changeAmount"])) {
                                unset($orderPayments[$i]["changeAmount"]);
                            }

                            if (isset($orderPayments[$i]["changeAmountCurrency"])) {
                                unset($orderPayments[$i]["changeAmountCurrency"]);
                            }

                            if (isset($orderPayments[$i]["changeAmountCurrencyCF"])) {
                                unset($orderPayments[$i]["changeAmountCurrencyCF"]);
                            }

                            if (isset($orderPayments[$i]["cardHolderName"])) {
                                unset($orderPayments[$i]["cardHolderName"]);
                            }

                            if (isset($orderPayments[$i]["expDate"])) {
                                unset($orderPayments[$i]["expDate"]);
                            }

                        }

                        DB::table($orderPaymentsTable)->insert($orderPayments[$i]);
                        
                        $orderPayments[$i]["storeID"] = $storeID;
                        DB::table($chainOrderPaymentsTable)->insert($orderPayments[$i]);

                        if ($orderPayments[$i]["paymentType"] == 'PAYMENT_VOUCHER') {
                            $transactionID = preg_replace('/[^A-Za-z0-9]/', '', $orderPayments[$i]["transactionID"]);

                            $checkVoucher = convertToArray(DB::table($discountVoucherTable)->where('voucherCode', $transactionID)->select('voucherID')->get());
                            if (!empty($checkVoucher)) {
                                $voucherID = preg_replace('/[^A-Za-z0-9]/', '', $checkVoucher[0]['voucherID']);
                                $updateVoucherArray = array();
                                $updateVoucherArray["voucherID"] = $voucherID;
                                if ($checkVoucher[0]["allocated"] <= 1) {
                                    $updateVoucherArray["customerID"] = $orderSummary["customersIDs"];
                                }
                                DB::table($discountVoucherTable)->where('voucherID', $updateVoucherArray['voucherID'])->update($updateVoucherArray);
                            }
                        }

                        if ($orderPayments[$i]["paymentType"] == 'PAYMENT_CREDIT_NOTE' && (!isset($dataArray['salesOrderID']) || $dataArray["salesOrderID"] == "" || $dataArray["salesOrderID"] == null)) {
                            $updatePayArray = array("status" => "INACTIVE");
                            DB::table($creditNoteValidationLogsTable)->where("orderID", $orderPayments[$i]["orderID"])->where("storeID", $storeID)->where("customerID", $orderSummary["customersIDs"])->where("creditNoteID", $orderPayments[$i]["transactionID"])->update($updatePayArray);
                        }

                        if($orderPayments[$i]['paymentType'] == 'PAYMENT_PARTY' && $enablePartyMaster == 1 && isset($orderPayments[$i]['partyID']) && $orderPayments[$i]['partyID'] > 0 && (!isset($dataArray['salesOrderID']) || $dataArray["salesOrderID"] == "" || $dataArray["salesOrderID"] == null)){
                            $partyArray = array();
                            $partyArray['partyID'] = $orderPayments[$i]['partyID'];
                            $partyArray['storeID'] = $orderSummary['storeID'];
                            $partyArray['counterID'] = isset($orderSummary['counterID']) ? $orderSummary['counterID'] : 0;
                            $partyArray['deviceID'] = $orderSummary['deviceID'];
                            $partyArray['batchID'] = isset($dataArray['batchID']) ? $dataArray['batchID'] : 0;
                            $partyArray['transactionUser'] = $orderSummary['billingUsername'];
                            $partyArray['transactionID'] = $orderPayments[$i]['orderID'];
                            $partyArray['transactionSubID'] = $orderPayments[$i]['paymentSubID'];
                            $partyArray['transRefID'] = NULL;
                            $partyArray['transactionType'] = 'SALES_INVOICE';
                            $partyArray['transactionTimeLocal'] = $orderPayments[$i]['orderTimeLocal'];
                            $partyArray['posDate'] = $orderSummary['posDate'];
                            $partyArray['transactionTimeUTC'] = $orderPayments[$i]['orderTimeUTC'];
                            $partyArray['timezone'] = $orderPayments[$i]['timezone'];
                            $partyArray['transactionAmount'] = $orderPayments[$i]['amount'];
                            $partyArray['transactionActivity'] = 'DEBIT';
                            $partyArray['transactionRemarks'] = NULL;
                            $partyLedger = PartyMasterLedger::postPartyMasterLedger($partyArray,$chainID);

                            if($partyLedger['status'] == false){
                                DB::rollback();
                                return $partyLedger;
                            }

                        }
                    }
                }

                // tender declaration
                if($getChainSettings[0]["enableTenderDeclarationOnInvoice"] == 1){
                    if($chainSettings[0]['mandateTenderDeclarationOnInvoice'] == 1 && (!isset($dataArray['tenderDeclarations']) || empty($dataArray['tenderDeclarations']) || count($dataArray['tenderDeclarations']) == 0) && $dataArray['paymentList'][0]['paymentType'] == 'PAYMENT_CASH'){
                        DB::rollback();
                        $status["status"] = false;
                        $status["message"] = "Tender declaration is mandatory";
                        return $status;
                    }
                    $declaration = Cash::createTenderDeclarationTransaction($chainID, $dataArray);
                    if($declaration["status"] == false){
                        DB::rollback();
                        $status["status"] = false;
                        $status["message"] = "Error in creating tender declaration";
                        return $status;
                    }
                }

                /* Record invoice data start */

                try{
                    $invoiceSummaryArray = array();
                    $invoiceDetailArray = array();
                    $invoicePaymentArray = array();
                    $invoiceComboProductsArray = array();
                    $invoiceModifiersArray = array();

                    $invoiceSummaryArray = $orderSummary;
                    $invoiceSummaryArray['orderRefID'] = NULL;
                    // insert data in invoice summary array

                    if($recordChainLevelSalesInvoice){
                        if($storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'UP_AGRICULTURE'){
                            if($dispatchUPAgriSales['status'] == true){
                                $invoiceSummaryArray['UPAgriOrderSyncStatus'] = 1;
                                //$invoiceSummaryArray['dummy1'] = "SYNCED";
                            }
                        }

                        if($storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'AURIONPRO'){
                            if($dispatchUPAgriSales['status'] == true){
                                $invoiceSummaryArray['UPAgriOrderSyncStatus'] = 1;
                                //$invoiceSummaryArray['dummy1'] = "SYNCED";
                            }
                        }

                        if($enableRRAIntegration == 1){
                            $invoiceSummaryArray['rcptNo'] = $dataArray['rcptNo'];
                            $invoiceSummaryArray['intrlData'] = $dataArray['intrlData'];
                            $invoiceSummaryArray['rcptSign'] = $dataArray['rcptSign'];
                            $invoiceSummaryArray['totRcptNo'] = $dataArray['totRcptNo'];
                            $invoiceSummaryArray['vsdcRcptPbctDate'] = $dataArray['vsdcRcptPbctDate'];
                            $invoiceSummaryArray['sdcID'] = $dataArray['sdcID'];
                            $invoiceSummaryArray['mrcNo'] = $dataArray['mrcNo'];
                            $invoiceSummaryArray['webRecptNo'] = $dataArray['webRecptNo'];
                            $invoiceSummaryArray['RRAResponseJSON'] = $dataArray['RRAResponseJSON'];
                            $invoiceSummaryArray['RRAPurcahseCode'] = $dataArray['RRAPurchaseCode'];
                        }
                        if($storeDetail[0]['enableMunicipalIntegration'] == 1 && isset($dataArray['municipalTransactionID'])){
                            $invoiceSummaryArray['externalOrderID'] = $dataArray['municipalTransactionID'];
                        }
                        DB::table($chainInvoiceSummaryTable)->insert($invoiceSummaryArray);
                    }


                    // $invoiceDetailArray = $orderDetail;
                    // $idCount = count($invoiceDetailArray);
                    // for ($i=0; $i < $idCount; $i++) {
                    //     $invoiceDetailArray[$i]["orderRefID"] = NULL;
                    //     if($recordChainLevelSalesInvoice){
                    //         if($storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'UP_AGRICULTURE'){
                    //             if($dispatchUPAgriSales['status'] == true){
                    //                 $invoiceDetailArray[$i]['dummy1'] = "SYNCED";
                    //             }
                    //         }
                    //         if($storeDetail[0]['enableMunicipalIntegration'] == 1 && strtoupper($storeDetail[0]['municipalType']) == 'AURIONPRO'){
                    //             if($dispatchUPAgriSales['status'] == true){
                    //                 $invoiceDetailArray[$i]['dummy1'] = "SYNCED";
                    //             }
                    //         }
                    //         DB::table($chainInvoiceDetailTable)->insert($invoiceDetailArray[$i]);
                    //     }
                    // }

                    $invoicePaymentArray = $orderPayments;
                    $ipCount = count($invoicePaymentArray);

                    for ($i=0; $i < $ipCount; $i++) {
                        $invoicePaymentArray[$i]["orderRefID"] = NULL;
                        if($recordChainLevelSalesInvoice){
                            DB::table($chainInvoicePaymentsTable)->insert($invoicePaymentArray[$i]);
                        }

                        if ((!isset($salesOrderID) || $salesOrderID == "") && ($invoicePaymentArray[$i]["paymentType"] == 'PAYMENT_CREDIT_NOTE' || $invoicePaymentArray[$i]["paymentType"] == 'PAYMENT_CREDIT')) {

                            $tempLedgerInsertArray = array();
                            $tempLedgerInsertArray["customerID"] = $invoiceSummaryArray["customersIDs"];
                            $tempLedgerInsertArray["storeID"] = $invoiceSummaryArray["storeID"];
                            $tempLedgerInsertArray["transactionID"] = $invoiceSummaryArray["orderID"];
                            $tempLedgerInsertArray["transactionSubID"] = 1;
                            $tempLedgerInsertArray["transactionType"] = $invoicePaymentArray[$i]["paymentType"] == 'PAYMENT_CREDIT'?"TRN_CREDIT_SALE" : "TRN_ADVANCE_CREDIT_SETTLEMENT";
                            $tempLedgerInsertArray["transactionActivity"] = "DEBIT";
                            $tempLedgerInsertArray["transactionAmount"] = (-1)*$invoicePaymentArray[$i]["amount"];
                            $tempLedgerInsertArray["closingBalance"] = (-1)*(isset($chainCustAccountArray["balance"]) ? $chainCustAccountArray["balance"] : 0);
                            $tempLedgerInsertArray["paymentType"] = $invoicePaymentArray[$i]["paymentType"];
                            $tempLedgerInsertArray["settlementStatus"] = $invoicePaymentArray[$i]["paymentType"] == 'PAYMENT_CREDIT'? 'UNSETTLED' : 'SETTLED';
                            $tempLedgerInsertArray["settledAmount"] = $invoicePaymentArray[$i]["paymentType"] == 'PAYMENT_CREDIT'?0.00:$invoicePaymentArray[$i]['amount'];
                            $tempLedgerInsertArray["transactionTimeLocal"] = $invoicePaymentArray[$i]["orderTimeLocal"];
                            $tempLedgerInsertArray["transactionTimeUTC"] = $invoicePaymentArray[$i]["orderTimeUTC"];
                            $tempLedgerInsertArray["timezone"] = $invoicePaymentArray[$i]["timezone"];
                            $tempLedgerInsertArray["isActive"] = 1;
                            $settleLedgerID = DB::table($customerLedgerTable)->insertGetId($tempLedgerInsertArray);

                        }
                    }

                    if (!empty($comboDetail)) {
                        $invoiceComboProductsArray = $comboDetail;
                        $comboCount = count($invoiceComboProductsArray);
                        for ($i=0; $i < $comboCount; $i++) {
                            $invoiceComboProductsArray[$i]["orderRefID"] = NULL;
                            if($recordChainLevelSalesInvoice){
                                DB::table($chainInvoiceComboProductsTable)->insert($invoiceComboProductsArray[$i]);
                            }
                        }
                    }

                    if (!empty($modDetail)) {
                        $invoiceModifiersArray = $modDetail;
                        $modCount = count($invoiceModifiersArray);
                        for ($i=0; $i < $modCount; $i++) {
                            $invoiceModifiersArray[$i]["orderRefID"] = NULL;
                            if($recordChainLevelSalesInvoice){
                                DB::table($chainInvoiceModifiersTable)->insert($invoiceModifiersArray[$i]);
                            }
                        }
                    }

                } catch(\Illuminate\Database\QueryException $e){
                    DB::rollback();
                    Log::info("duplicate check order detail",[
                        "err" => $e,
                        "chainID" => $chainID, "storeID" => $storeID, "order id" => $invoiceSummaryArray[0]["orderID"]
                    ]);
                    $errorCode = (isset($e->errorInfo) && isset($e->errorInfo[1])) ? $e->errorInfo[1] : '-1';
                    if($errorCode == '1062') {
                        // Duplicate entry
                        $responseArray["errorMessage"] = "Duplicate entry in invoice summary.";
                        $responseArray["orderCreationReport"] = $status;
                        $responseArray["orderID"] = $dataArray["orderID"];
                        $responseArray["invoiceNumber"] = $invoiceSummaryArray[0]['invoiceNumber'];
                        return $responseArray;
                    } else {
                        // Some other error ??
                        $responseArray["errorMessage"] = "DB error";
                        $responseArray['error'] = $e;
                        $responseArray["orderCreationReport"] = $status;
                        $responseArray["orderID"] = $dataArray["orderID"];
                        $responseArray["invoiceNumber"] = $invoiceSummaryArray[0]['invoiceNumber'];
                        return $responseArray;
                    }
                }

                /* Record invoice data end */

                if(!empty($creditNoteArray)){
                  $credCount = count($creditNoteArray);
                  for ($i=0; $i < $credCount; $i++) {
                    if($enableCrossStoreCreditNoteRedemption == 1){
                        $creditNoteArray[$i]['redeemStoreID'] = $storeID;
                    }
                    $creditNoteKey = array_search($creditNoteArray[$i]["creditNoteID"],array_column($getCreditNote,'creditNoteID'));
                    $creditNoteAmount = $getCreditNote[$creditNoteKey]['amount'];
                    $creditNoteRedeemAmount = $creditNoteArray[$i]["redeemAmount"];
                    DB::table($creditPaymentSummaryTable)->where("creditNoteID",$creditNoteArray[$i]["creditNoteID"])->update($creditNoteArray[$i]);
                    if($enablePartialRedemptionOfCreditNote == 1 && $creditNoteRedeemAmount < $creditNoteAmount){
                        $getMaxCreditNoteID = convertToArray(DB::table($creditPaymentSummaryTable)->where("transactionType","TRN_ADVANCE_PAYMENT")->max("creditNoteID"));
                        $insertCreditNoteArray = $getCreditNote[$creditNoteKey];
                        $insertCreditNoteArray['amount'] = $creditNoteAmount - $creditNoteRedeemAmount;
                        $cnTaxArray = array();
                        $cnTDSArray = array();
                        $cnTCSArray = array();
                        for($c=0; $c<count($orderAccounting); $c++){
                            if(!isset($orderAccounting[$c]['dummy1']) || $orderAccounting[$c]['dummy1'] == NULL){
                                continue;
                            }
                            if($orderAccounting[$c]['dummy1'] == 'CREDIT_NOTE_TAX_ADJUSTMENT'){
                                $tempCNTaxArray = array();
                                $tempCNTaxArray['taxID'] = $orderAccounting[$c]['dummy2'];
                                $tempCNTaxArray['taxName'] = $orderAccounting[$c]['dummy3'];
                                $tempCNTaxArray['taxPercentage'] = $orderAccounting[$c]['dummy4'];
                                $tempCNTaxArray['taxValue'] = $orderAccounting[$c]['dummy5'];
                                array_push($cnTaxArray, $tempCNTaxArray);
                            }elseif($orderAccounting[$c]['dummy1'] == 'CREDIT_NOTE_TDS_ADJUSTMENT'){
                                $tempCNTDSArray = array();
                                $tempCNTDSArray['taxID'] = $orderAccounting[$c]['dummy2'];
                                $tempCNTDSArray['taxName'] = $orderAccounting[$c]['dummy3'];
                                $tempCNTDSArray['taxPercentage'] = $orderAccounting[$c]['dummy4'];
                                $tempCNTDSArray['taxValue'] = $orderAccounting[$c]['dummy5'];
                                array_push($cnTDSArray, $tempCNTDSArray);
                            }elseif($orderAccounting[$c]['dummy1'] == 'CREDIT_NOTE_TCS_ADJUSTMENT'){
                                $tempCNTCSArray = array();
                                $tempCNTCSArray['taxID'] = $orderAccounting[$c]['dummy2'];
                                $tempCNTCSArray['taxName'] = $orderAccounting[$c]['dummy3'];
                                $tempCNTCSArray['taxPercentage'] = $orderAccounting[$c]['dummy4'];
                                $tempCNTCSArray['taxValue'] = $orderAccounting[$c]['dummy5'];
                                array_push($cnTCSArray, $tempCNTCSArray);
                            }elseif($orderAccounting[$c]['dummy1'] == 'CREDIT_NOTE_PAYMENT_ADJUSTMENT'){
                                $insertCreditNoteArray['amountCredited'] = $orderAccounting[$c]['dummy2'];
                            }
                        }
                        if(!empty($cnTaxArray)){
                            $insertCreditNoteArray['taxIDs'] = implode(",",array_column($cnTaxArray,'taxID'));
                            $insertCreditNoteArray['taxNames'] = implode(",",array_column($cnTaxArray,'taxName'));
                            $insertCreditNoteArray['taxPercentages'] = implode(",",array_column($cnTaxArray,'taxPercentage'));
                            $insertCreditNoteArray['taxValues'] = implode(",",array_column($cnTaxArray,'taxValue'));
                            $insertCreditNoteArray['totalTaxValue'] = array_sum(array_column($cnTaxArray,'taxValue'));
                        }

                        if(!empty($cnTDSArray)){
                            $insertCreditNoteArray['TDSID'] = $cnTDSArray[0]['taxID'];
                            $insertCreditNoteArray['TDSName'] = $cnTDSArray[0]['taxName'];
                            $insertCreditNoteArray['TDSPercentage'] = $cnTDSArray[0]['taxPercentage'];
                            $insertCreditNoteArray['TDSValue'] = $cnTDSArray[0]['taxValue'];

                        }

                        if(!empty($cnTCSArray)){
                            $insertCreditNoteArray['TCSID'] = $cnTCSArray[0]['taxID'];
                            $insertCreditNoteArray['TCSName'] = $cnTCSArray[0]['taxName'];
                            $insertCreditNoteArray['TCSPercentage'] = $cnTCSArray[0]['taxPercentage'];
                            $insertCreditNoteArray['TCSValue'] = $cnTCSArray[0]['taxValue'];

                        }

                        $insertCreditNoteArray['creditPaymentTimeLocal'] = $currentTimeLocal;
                        $insertCreditNoteArray['creditPaymentTimeUTC'] = $currentTimeUTC;
                        $insertCreditNoteArray['creditNoteID'] = $getMaxCreditNoteID + 1;
                        $insertCreditNoteArray['redeemPaymentSubID'] = 0;
                        $insertCreditNoteArray['redeemStoreID'] = 0;
                        $insertCreditNoteArray['redeemAmount'] = 0.00;
                        $creditTransactionID = generateRandomString(12);
                        $insertCreditNoteArray['creditTransactionID'] = $creditTransactionID;
                        $unsetCreditNoteField = array('ID','redeemOrderID','redeemTimeLocal','redeemTimeUTC');
                        $creditNoteCount = count($unsetCreditNoteField);
                        if (isset($enableCreditNoteExpiry) && $enableCreditNoteExpiry == 1) {
                            $noOfDays = $creditNoteExpiryDays;
                            $currentDate = date('Y-m-d');
                            if($noOfDays > 0){
                                $expiryDate = new DateTime($currentDate);
                                $expiryDate->modify('+'.$noOfDays.' day');
                                $finalExpiryDate =  $expiryDate->format('Y-m-d');
                                $insertCreditNoteArray['creditNoteExpiryDate'] = $finalExpiryDate;
                            } else{
                                $insertCreditNoteArray['creditNoteExpiryDate'] = $currentDate;
                            }
                        }

                        for ($c=0; $c < $creditNoteCount; $c++) {
                            $insertCreditNoteArray[$unsetCreditNoteField[$c]] = NULL;
                        }
                        DB::table($creditPaymentSummaryTable)->insert($insertCreditNoteArray);

                        // $tempLedgerInsertArray = array();
                        // $tempLedgerInsertArray["customerID"] = $invoiceSummaryArray["customersIDs"];
                        // $tempLedgerInsertArray["storeID"] = $invoiceSummaryArray["storeID"];
                        // $tempLedgerInsertArray["transactionID"] = $creditTransactionID;
                        // $tempLedgerInsertArray["transactionSubID"] = 1;
                        // $tempLedgerInsertArray["transactionType"] = "TRN_ADVANCE_PAYMENT";
                        // $tempLedgerInsertArray["transactionActivity"] = "CREDIT";
                        // $tempLedgerInsertArray["transactionAmount"] = $creditNoteAmount - $creditNoteRedeemAmount;;
                        // $tempLedgerInsertArray["closingBalance"] = isset($chainCustAccountArray["balance"]) ? $chainCustAccountArray["balance"] : 0;
                        // $tempLedgerInsertArray["paymentType"] = $invoicePaymentArray[$i]["paymentType"];
                        // $tempLedgerInsertArray["settlementStatus"] = 'PAYMENT_CREDIT_NOTE';
                        // $tempLedgerInsertArray["settledAmount"] = 0.00;
                        // $tempLedgerInsertArray["transactionTimeLocal"] = $insertCreditNoteArray['creditPaymentTimeLocal'];
                        // $tempLedgerInsertArray["transactionTimeUTC"] = $insertCreditNoteArray['creditPaymentTimeUTC'];
                        // $tempLedgerInsertArray["timezone"] = $insertCreditNoteArray['timezone'];
                        // $tempLedgerInsertArray["isActive"] = 1;
                        // $settleLedgerID = DB::table($customerLedgerTable)->insertGetId($tempLedgerInsertArray);
                        // $settleLedgerID = "";

                        if ($enableCreditNoteExpiry == 0) {
                            $dateNow = new DateTime(date('Y-m-d'));
                            date_add($dateNow, date_interval_create_from_date_string((365*10).' days')); // keep 10 years default expiration
                            $expiryDateF = date_format($dateNow, "Y-m-d");
                            $insertCreditNoteArray['creditNoteExpiryDate'] = $expiryDateF;
                        }
                        array_push(self::$creditNotesGenerated, $insertCreditNoteArray);
                    }

                  }
                }

                $countOf = count($orderBatchArray);

                for ($i=0; $i < $countOf; $i++) {
                    DB::table($orderBatchDetailTable)->insert($orderBatchArray[$i]);
                }

                if (!empty($yieldProdArray)) {
                  $yieldTransactionArray = array();
                  $yieldTransactionArray["invoiceNumber"] = $orderSummary["orderID"];
                  $yieldTransactionArray["invoiceDate"] = $orderSummary["posDate"];
                  $yieldTransactionArray["sourceID"] = $storeID;
                  $yieldTransactionArray["sourceType"] = "STORE";
                  $yieldTransactionArray["destinationID"] = $storeID;
                  $yieldTransactionArray["destinationType"] = "STORE";
                  $yieldTransactionArray["transactionType"] = "STOCK_YIELD_WASTAGE";
                  $yieldTransactionArray["transactionTimeLocal"] = $orderSummary["orderCreationTimeLocal"];
                  $yieldTransactionArray["transactionTimeUTC"] = $orderSummary["orderCreationTimeUTC"];
                  $yieldTransactionArray["timezone"] = $orderSummary["timezone"];
                  $yieldTransactionArray["totalCost"] = $yieldTotalCost;
                  $yieldTransactionArray["totalItemNumber"] = count($yieldProdArray);

                  // Users table is already defined in the tables array from TableRegistry::getOrderTables
                  $getUserID = convertToArray(DB::table($usersTable)->where("userName","LIKE",$orderSummary["billingUsername"])->get());
                  $yieldTransactionArray["user"] = $getUserID[0]["ID"];
                  $yieldTransactionArray["remarks"] = "";

                  self::$yieldTransactionID = DB::table($stockTransactionSummaryTable)->insertGetId($yieldTransactionArray);

                  $countOf = count($yieldProdArray);
                  for ($i=0; $i < $countOf; $i++) {
                    DB::table($stockTransactionDetailTable)->insert($yieldProdArray[$i]);
                  }

                }

                if(isset($voucherDetails) && count($voucherDetails)>0){
                    for($ovu = 0; $ovu<count($voucherDetails); $ovu++ ){
                        if (!isset($voucherDetails[$ovu]["voucherCode"]) || $voucherDetails[$ovu]["voucherCode"] == null || $voucherDetails[$ovu]["voucherCode"] == "") {
                            continue;
                        }
                        DB::table($discountVoucherTable)->insert($voucherDetails[$ovu]);
                    }
                }
                //for parking module
                if($chainModules && $chainModules[0]['parkingModule']){
                    $vehicleTrackerID = isset($vehicleArray[0]['ID']) ? $vehicleArray[0]['ID'] : "";
                    $updateVehicleTracker['checkOutTimeLocal'] = $vehicleArray[0]['checkOutTimeLocal'];
                    $updateVehicleTracker["checkOutTimeUTC"]   = localtoUTC($updateVehicleTracker['checkOutTimeLocal'], $timezone);
                    $updateVehicleTracker['status'] = $vehicleArray[0]['status'];
                    $updateVehicleTracker['totalValue'] = $vehicleArray[0]['totalValue'];
                    $updateVehicleTracker['orderID'] = $dataArray['orderID'];
                    $updateVehicleTracker['rate'] = $vehicleArray[0]['rate'];

                    if($vehicleTrackerID != ""){
                        DB::table($vehicleTrackerTable)->where('ID',$vehicleTrackerID)->update($updateVehicleTracker);
                    }else{

                        $vehicleArray[0]['checkInTimeUTC'] = localtoUTC($vehicleArray[0]['checkInTimeLocal'], $timezone);
                        $vehicleArray[0]['checkOutTimeUTC'] = localtoUTC($vehicleArray[0]['checkOutTimeLocal'], $timezone);
                        $vehicleArray[0]['orderID'] = $dataArray['orderID'];
                        $vehicleArray[0]['productID'] = $dataArray['productsList'][0]['productID'];
                        $vehicleArray[0]['timezone'] = $timezone;
                        $vehicleArray[0]['storeID'] = $storeID;
                        DB::table($vehicleTrackerTable)->insert($vehicleArray[0]);
                    }
                }


                /** Handle for tax filing start */
                if($enableTaxFiling == 1){
                    $taxFilingArray = array();
                    $taxFilingArray['storeID'] = $storeID;
                    $taxFilingArray['orderID'] = $orderSummary['orderID'];
                    $taxFilingArray['transactionType'] = 'SALES_INVOICE';

                    $posTax = TaxFiling::postTaxTracker($taxFilingArray,$chainID);

                }
                /** Handle for tax filing end */


                if($enableQBWalletManagement === 1 && !empty($updateWalletArray)){

                    // Wallet tables are already defined in the tables array from TableRegistry::getOrderTables
                    // If not, we can add them to the getOrderTables method

                    // Wallet IDs
                    $walletID = $walletActivityTrackerArray["walletID"];
                    $walletMapID = $walletActivityTrackerArray["walletMapID"];
                    $isParent = $updateWalletArray["isParent"];
                    unset($updateWalletArray["isParent"]);

                    $walletActivityTrackerArray['counterID'] = $dataArray['counterID'];

                    // Update wallet balance
                    DB::table($userWalletTable)->where("walletID", $walletID)->update($updateWalletArray);

                    // update wallet mapping
                    // Update the amount consumed and amount recharged for the wallet mapping
                    if( $isParent == 1){
                        $amount = $updateWalletArray["lastTransactionAmount"];
                        DB::table($walletMappingTable)->where("walletMapID", $walletMapID)->update([
                            'amountConsumed' => DB::raw('amountConsumed + ' . $amount),
                            'walletMapBalance' => DB::raw('walletMapBalance - ' . $amount)
                        ]);
                    }else{
                        $amount = $updateWalletArray["lastTransactionAmount"];
                        DB::table($walletMappingTable)->where("walletMapID", $walletMapID)->update([
                            'amountConsumed' => DB::raw('amountConsumed + ' . $amount)
                        ]);
                    }
                    // Insert record into wallet activity tracker
                    // This table is used to keep track of all the wallet transactions
                    DB::table($walletActivityTrackerTable)->insert($walletActivityTrackerArray);

                    // Insert record into wallet request tracker
                    // This table is used to keep track of all the wallet payment requests
                    DB::table($walletRequestTrackerTable)->insert($walletRequestTracker);

                }
            // });

			if ($enableLogging) {
				Log::info("Order processing - db trx close: " . json_encode([
					'chainID' => $chainID,
					'storeID' => $storeID,
					'orderID' => $dataArray["orderID"],
				]));
			}
            // for reservation bookings
            if($enableEcomReservation == 1){
                $reservationDetailsList = array();
                try {
                        // Reservation tables are already defined in the tables array from self::getOrderTables

                        $slotIDArray = array_unique(array_column($reservationDetail, 'slotID'));
                        $slotsDetail = convertToArray(DB::table($reservationSlotsTable)->whereIn("slotID", $slotIDArray)->where("isActive", 1)->lockForUpdate()->get());
                        $responseArray["orderCreationReport"] = $status;
                        $responseArray["orderID"] = $dataArray["orderID"];
                        $responseArray["invoiceNumber"] = $dataArray["invoiceNumber"];
                        for ($i=0; $i < $resCount; $i++) {
                            $slotID = $reservationDetail[$i]["slotID"];
                            $slotDate = $reservationDetail[$i]["slotDate"];
                            $ticketCount = $reservationDetail[$i]["ticketCount"];
                            //by vishal
                            $pricingID = $reservationDetail[$i]["pricingID"];
                            $guestCount = $reservationDetail[$i]["guestCount"];
                            //$reservationId = $reservationDetail[$i]["reservationId"];
                            $qtyOrd = $reservationDetail[$i]["qtyOrd"];

                            $reservationProductID = $reservationDetail[$i]["reservationProductID"];
                            unset($reservationDetail[$i]["ticketCount"]);
                            unset($reservationDetail[$i]["qtyOrd"]);
                            //unset($reservationDetail[$i]["pricingID"]);
                            unset($reservationDetail[$i]["reservationId"]);
                            $key = array_search($slotID, array_column($slotsDetail, 'slotID'));
                            if(!is_int($key)){
                                DB::rollback();
                                $responseArray["errorMessage"] = "Invalid slotID $slotID";
                                return $responseArray;
                            }

                            // $reservationDetail[$i]['orderID'] = $dataArray["orderID"];
                            $reservationDetail[$i]['customerID'] = $orderSummary["customersIDs"];
                            $reservationDetail[$i]['storeID'] = $storeID;
                            $bookingsAllowed = $slotsDetail[$key]["bookingsAllowed"];
                            $activeBookings = $slotsDetail[$key]["activeBookings"];
                            // $minGuestPerBooking = $ticketCount * $slotsDetail[$key]["minGuestPerBooking"];
                            // $maxGuestPerBooking = $ticketCount * $slotsDetail[$key]["maxGuestPerBooking"];
                            // $minGuestPerBooking = $guestCount * $slotsDetail[$key]["minGuestPerBooking"];
                            // $maxGuestPerBooking = $guestCount * $slotsDetail[$key]["maxGuestPerBooking"];
                            $minGuestPerBooking = $slotsDetail[$key]["minGuestPerBooking"];
                            $maxGuestPerBooking = $slotsDetail[$key]["maxGuestPerBooking"];
                            $availableBookings  = $bookingsAllowed - $activeBookings;
                            if($availableBookings < $guestCount){
                                DB::rollback();
                                $responseArray["errorMessage"] = "ticket count is more than available bookings!";
                                return $responseArray;
                            }

                            if($minGuestPerBooking > $reservationDetail[$i]['guestCount']){
                                DB::rollback();
                                $responseArray["errorMessage"] = "Guest count is less than min guest per booking!!";
                                return $responseArray;
                            }

                            if($reservationDetail[$i]['guestCount'] > $maxGuestPerBooking){
                                DB::rollback();
                                $responseArray["errorMessage"] = "Guest count is more than max guest per booking!";
                                return $responseArray;
                            }

                            $reservationSummaryData = convertToArray(DB::table($reservationSummaryTable)->where(['reservationProductID' => $reservationProductID, "slotID" => $slotID, "storeID" => $storeID])->get());
                            if(empty($reservationSummaryData)){

                                $counterr = 0;
                            }else{
                                $counterr = count($reservationSummaryData);
                            }
                            // reservationProductID
                            $totalGuestCount = 0;
                            for ($j=0; $j < $qtyOrd; $j++) {
                                $reservationDetail[$i]["reservationID"] = "RT".$storeID.$slotID.date("Ymd", strtotime($slotDate)).($counterr++);
                                array_push($reservationDetailsList, $reservationDetail[$i]);
                                DB::table($reservationSummaryTable)->insert($reservationDetail[$i]);
                                $totalGuestCount += $reservationDetail[$i]["guestCount"];
                                $reservationTrackerArray = [];
                                $reservationTrackerArray["storeID"] = $storeID;
                                $reservationTrackerArray["slotID"] = $slotID;
                                $reservationTrackerArray["reservationID"] = $reservationDetail[$i]["reservationID"];
                                $reservationTrackerArray["reservationTrackerID"] = $reservationProductID;
                                $reservationTrackerArray["reservationStatus"] = $reservationDetail[$i]["bookingStatus"];
                                $reservationTrackerArray["logTimeLocal"] = date("Y-m-d H:i:s");
                                $reservationTrackerArray["timezone"] = $timezone;
                                $reservationTrackerArray["logTImeUTC"] = localToUTC($reservationTrackerArray["logTimeLocal"], $timezone);
                                $reservationTrackerArray["isActive"] = 1;
                                DB::table($reservationTrackerTable)->insert($reservationTrackerArray);
                            }
                            DB::table($reservationSlotsTable)->where('slotID', $slotID)->update(['activeBookings' => DB::raw('activeBookings + '.$totalGuestCount)]);
                            //DB::table($reservationSlotsTable)->where('slotID', $slotID)->update(['activeBookings' => DB::raw('activeBookings + '.$guestCount)]);
                        }
                    }catch(\Illuminate\Database\QueryException $e){
                    DB::rollback();
                    $responseArray["errorMessage"] = $e->getMessage();
                    $responseArray["orderCreationReport"] = $status;
                    $responseArray["orderID"] = $dataArray["orderID"];
                    $responseArray["invoiceNumber"] = $dataArray["invoiceNumber"];
                    return $responseArray;
                }
            }

            // if (isset($dbtransaction) && $dbtransaction != null && isset($dbtransaction["errorMessage"])){
            //     return $dbtransaction;
            // }


			if ($enableLogging) {
				Log::info("Order processing - ledger: " . json_encode([
					'chainID' => $chainID,
					'storeID' => $storeID,
					'orderID' => $dataArray["orderID"],
				]));
			}
            /** Handle stock ledger update start */

            if($enableStockLedger == 1 && (!isset($dataArray['salesOrderID']) || $dataArray['salesOrderID'] == null || $dataArray['salesOrderID'] == "")){
                $ledgerArray = array();
                $ledgerArray['sourceID'] = $storeID;
                $ledgerArray['sourceType'] = 'STORE';
                $ledgerArray['inventorySourceID'] = $storeID;
                $ledgerArray['inventorySourceType'] = 'STORE';
                $ledgerArray['inventorySourceID'] = $storeID;
                $ledgerArray['transactionID'] = $dataArray['orderID'];
                $ledgerArray['transactionDataSource'] = 'ORDER';
                $ledgerArray['transactionType'] = 'SALES_INVOICE';
                if(isset($dataArray['isNoInvoiceSale']) && $dataArray['isNoInvoiceSale'] == 1){

                }else{
                    $updateLedger = StockLedger::addStockLedger($ledgerArray,$chainID);
					// if update ledger failed revert the transaction and return
					if (isset($updateLedger) && isset($updateLedger['status']) && $updateLedger['status'] == false) {
                        Log::debug("updateLedger: " . json_encode($updateLedger));
						DB::rollBack();
						$responseArray["errorMessage"] = $updateLedger["message"] ?? "Failed to update stock ledger";
						$responseArray["orderCreationReport"] = $updateLedger;
						$responseArray["orderID"] = $dataArray["orderID"];
						$responseArray["invoiceNumber"] = $dataArray["invoiceNumber"];
						Log::error($responseArray);
						return $responseArray;
					}
                }

                if(isset(self::$yieldTransactionID) && self::$yieldTransactionID > 0){
                    $ledgerArray = array();
                    $ledgerArray['sourceID'] = $storeID;
                    $ledgerArray['sourceType'] = 'STORE';
                    $ledgerArray['inventorySourceID'] = $storeID;
                    $ledgerArray['inventorySourceType'] = 'STORE';
                    $ledgerArray['inventorySourceID'] = $storeID;
                    $ledgerArray['transactionID'] = self::$yieldTransactionID;
                    $ledgerArray['transactiondataA$dataArraySource'] = 'STOCK_YIELD_WASTAGE';
                    $ledgerArray['transactionType'] = 'STOCK_YIELD_WASTAGE';

                    if(isset($dataArray['isNoInvoiceSale']) && $dataArray['isNoInvoiceSale'] == 1){

                    }else{
                        $updateLedger = StockLedger::addStockLedger($ledgerArray,$chainID);
						// if update ledger failed revert the transaction and return
						if (isset($updateLedger) && isset($updateLedger['status']) && $updateLedger['status'] == false) {
							DB::rollBack();
							$responseArray["errorMessage"] = $updateLedger["message"] ?? "Failed to update stock ledger";
							$responseArray["orderCreationReport"] = $updateLedger;
							$responseArray["orderID"] = $dataArray["orderID"];
							$responseArray["invoiceNumber"] = $dataArray["invoiceNumber"];
							Log::error($responseArray);
							return $responseArray;
						}
                    }

                }

            }

            //dispatch job for ERP
            if($merchantIntegrationModule == "Wonderla"){
                $queueNumber = queueNumber($chainID);
                $wonderlaArray['sourceOperationID'] = $dataArray['orderID'];
                $wonderlaArray['sourceOperationType'] = 'ORDER';
                $wonderlaJob = (new WonderlaPostInvoiceJob($wonderlaArray,$chainID))->onQueue($queueNumber);
                dispatch($wonderlaJob);

                //adding data to wonderlaIntegrationTracker
                // Wonderla integration tracker table is already defined in the tables array from TableRegistry::getOrderTables
                $wonderlaIntegrationTrackerArray['summaryType'] = "REVENUE";
                $wonderlaIntegrationTrackerArray['sourceOperationID'] = $dataArray['orderID'];
                $wonderlaIntegrationTrackerArray['sourceOperationType'] = "ORDER";
                $wonderlaIntegrationTrackerArray['posDate'] = $dataArray['posDate'];
                $wonderlaIntegrationTrackerArray['transactionTimeLocal'] = $dataArray['orderCreationTimeLocal'];
                $wonderlaIntegrationTrackerArray['transactionTimeUTC'] = $orderSummary["orderCreationTimeUTC"];
                $wonderlaIntegrationTrackerArray['isSynced'] = 0;

                DB::table($wonderlaIntegrationTrackerTable)->insert($wonderlaIntegrationTrackerArray);

            }

            /** Handle stock ledger update end */
            DB::commit();

            // update order tables
            DB::table($orderSummaryTable)->where("orderID", $dataArray['orderID'])->update(['isInventorySynced' => 1]);
            DB::table($chainOrderSummaryTable)->where("orderID", $dataArray['orderID'])->update(['isInventorySynced' => 1]);
            if ($recordChainLevelSalesInvoice) {
                DB::table($chainInvoiceSummaryTable)->where("orderID", $dataArray['orderID'])->update(['isInventorySynced' => 1]);
            }

            // if merchantIntegration is beepkart
            // update credit note summary table
            $merchantIntegrationModule = $getChainSettings[0]['merchantIntegrationModule'];
            if ($merchantIntegrationModule == "beepkartModule") {
                if (isset(self::$finalCustCredit["custAccountArray"])) {
                    // credit adjustment has happened. So update the credit note summary table
                    if (floatval(self::$finalCustCredit["custCreditData"][0]["balance"]) < 0) {
                        $positiveBalance = abs(floatval(self::$finalCustCredit["custCreditData"][0]["balance"]));
                        $tempPaymentSubID = 0;
                        // from $orderPayments get paymentSubID and amount for CREDIT_SALE
                        for ($i = 0; $i < count($orderPayments); $i++) {
                            if ($orderPayments[$i]["paymentType"] == "PAYMENT_CREDIT") {
                                $tempPaymentSubID = $orderPayments[$i]["paymentSubID"];
                                $tempAmount = $orderPayments[$i]["amount"];
                                break;
                            }
                        }
                        $tempAmount = $positiveBalance; // settle complete balance for now. Later we can settle partial amount @todo
                        if ($tempPaymentSubID == 0 || $tempAmount == 0) {
                            $responseArray["creditAdjustment"] = "CREDIT_SALE payment not found in orderPayments";
                        } else {
                            $updateCreditNoteArray = array();
                            $updateCreditNoteArray["redeemOrderID"] = $orderSummary["orderID"];
                            $updateCreditNoteArray["redeemTimeLocal"] = $orderSummary["orderCreationTimeLocal"];
                            $updateCreditNoteArray["redeemTimeUTC"] = $orderSummary["orderCreationTimeUTC"];
                            $updateCreditNoteArray["redeemStoreID"] = $storeID;
                            $updateCreditNoteArray["redeemAmount"] = $positiveBalance;
                            $updateCreditNoteArray["redeemPaymentSubID"] = $tempPaymentSubID; // order payment CREDIT_SALE

                            // get credit note details for the customer
                            $creditNoteDetails = convertToArray(DB::table($creditPaymentSummaryTable)->where("customerID", $orderSummary["customersIDs"])->where('redeemAmount', 0)->where('amount', $tempAmount)->where("transactionType", "TRN_ADVANCE_PAYMENT")->where("status", "ACTIVE")->first());
                            if (isset($creditNoteDetails) && !empty($creditNoteDetails)) {
                                DB::table($creditPaymentSummaryTable)->where("creditNoteID", $creditNoteDetails["creditNoteID"])->update($updateCreditNoteArray);
                            }
                        }
                    }
                }
            }

			$queueNumber = queueNumber($chainID);
			switch ($merchantIntegrationModule) {
				case 'CostaCoffee':
					// if customer is present in order then only proceed
					if (isset($dataArray["customers"]) && isset($dataArray["customers"][0]) && isset($dataArray["customers"][0]["notes"]) && !empty($dataArray["customers"][0]["notes"])) {
						$customerNotes = $dataArray["customers"][0]["notes"];
						$storeGUID = $storeDetail[0]["storeGUID"];
						$costaCoffeeData = [
							"amount" => intval($orderSummary["grossBill"] * 100),
							"qrId" => $customerNotes,
							"referenceNo" => intval(preg_replace('/[^0-9]/', '', $orderSummary["orderID"])),
							"storeNumber" => intval($storeGUID) ?? intval($storeID),
							"checkSeq" => intval($orderSummary["invoiceNumber"]),
							"revenueCenterName" => $getChainSettings[0]["chainGUID"] ?? "Costa"
						];
						$createDLFGiftVoucherJob = (new CostaCoffeeJob($costaCoffeeData, $chainID))->onQueue("$queueNumber");
						dispatch($createDLFGiftVoucherJob);
					}
					break;
				default:
					# code...
					break;
			}

            // PUSH GIFT VOUCHER TO DLF
            if(!empty($giftVoucherDLF)){
                $queueNumber = queueNumber($chainID);
                foreach($giftVoucherDLF as $key => $gvf){
                    if($gvf["voucherCode"] != null && $gvf["GvNo"]!= null){
                        $createDLFGiftVoucherJob = (new CreateDLFGiftVoucherJob($chainID,$gvf))->onQueue("$queueNumber");
                        dispatch($createDLFGiftVoucherJob);
                        $tempCreateDLFGiftVoucherJob[$key]["dlfVoucherJob"]     = $createDLFGiftVoucherJob;
                        $tempCreateDLFGiftVoucherJob[$key]["dlfVoucherRequest"] = $gvf;
                        array_push(self::$createDLFGiftVoucherJobb, $tempCreateDLFGiftVoucherJob);
                    }
                }
            }

            //start purchase membership code
            if(!empty($membershipDetail) && is_array($membershipDetail))
            {
                // Global chain whatsapp credits table is already defined in the tables array from self::getOrderTables
                $creditsConsumedWA = count($membershipDetail);
                $checkCreditsWA = convertToArray(DB::table($globalChainWhatsappCreditsTable)->where('chainID', $chainID)->get());
                if (!empty($checkCreditsWA) && is_array($checkCreditsWA))
                {
                    $creditsLeftWA = $checkCreditsWA[0]['creditsLeft'];
                    $creditsConsumedTotalWA = $checkCreditsWA[0]['creditsConsumed'];
                    if ($creditsConsumedWA <= $creditsLeftWA)
                    {
                        $finalCreditsLeftWA = $creditsLeftWA - $creditsConsumedWA;
                        $finalCreditsConsumendWA = $creditsConsumedTotalWA + $creditsConsumedWA;
                        // Chain settings table is already defined in the tables array from self::getOrderTables
                        // $chainSettingsDetails = convertToArray(DB::Table($chainSettingsTable)->where('chainRefID',$chainID) ->select('GsWAUserID','GsWAPassword')->first());
                        if(!empty($getChainSettings))
                        {
                            $gsBase = env('GS_WA_BASE_URL');
                            $gsUserID = $getChainSettings[0]['GsWAUserID'];
                            $gsPassword = $getChainSettings[0]['GsWAPassword'];

                            $phone = $dataArray["customers"][0]["phone"];
                            $customersName  = $dataArray["customers"][0]["firstName"];

                            $waDetails = [];
                            $waDetails["GsWAUserID"] = $gsUserID;
                            $waDetails["GsWAPassword"] = $gsPassword;
                            Message::whatsappOptIn("91".$phone, $waDetails);

                            //purchase Membership Array
                            $purchaseMemeberShip = array();
                            $purchaseMemeberShip["requestType"]  =  'MembershipPurchase';
                            $purchaseMemeberShip["storeID"]  =  $storeID;
                            $purchaseMemeberShip["membershipDetail"] = $membershipDetail;
                            $purchaseMemeberShip["customersPhone"] = $phone;
                            $purchaseMemeberShip["customersName"]  = $customersName;
                            $purchaseMemeberShip["customerID"]     = $customer1;
                            $purchaseMemeberShip["brandName"]      = $brandName;
                            $purchaseMemeberShip["timezone"]       = $dataArray["timezone"];
                            //credit settlement
                            $purchaseMemeberShip["creditsConsumed"]  = $finalCreditsConsumendWA;
                            $purchaseMemeberShip["creditsLeft"]   = $finalCreditsLeftWA;

                            // $purchaseMembershipResponse = Message::PurchaseMembership($chainID,$purchaseMemeberShip);
                            // $order["purchaseMembershipJob"] = $purchaseMembershipResponse;

                            $queueNumberPurchaseMembership = queueNumber($chainID);
                            $purchaseMembershipJob = (new MembershipMessageJob($chainID, $purchaseMemeberShip))->onQueue($queueNumberPurchaseMembership)->delay(5);
                            dispatch($purchaseMembershipJob);
                            $order["purchaseMembershipJob"] = $purchaseMembershipJob;
                        }
                    }
                }
            }
            //end purchase membership code
            //start membership reedem point
            if((isset($memTracker)  && !empty($memTracker)))
            {
                // Global chain whatsapp credits table is already defined in the tables array from self::getOrderTables
                $creditsConsumedWA = count($memTracker);
                $checkCreditsWA = convertToArray(DB::table($globalChainWhatsappCreditsTable)->where('chainID', $chainID)->get());
                if (!empty($checkCreditsWA) && is_array($checkCreditsWA))
                {
                    $creditsLeftWA = $checkCreditsWA[0]['creditsLeft'];
                    $creditsConsumedTotalWA = $checkCreditsWA[0]['creditsConsumed'];
                    if ($creditsConsumedWA <= $creditsLeftWA)
                    {
                        $finalCreditsLeftWA = $creditsLeftWA - $creditsConsumedWA;
                        $finalCreditsConsumendWA = $creditsConsumedTotalWA + $creditsConsumedWA;

                        // Chain settings table is already defined in the tables array from self::getOrderTables
                        // $chainSettingsDetails = convertToArray(DB::Table($chainSettingsTable)->where('chainRefID',$chainID) ->select('GsWAUserID','GsWAPassword')->first());
                        if(!empty($getChainSettings))
                        {
                            $gsBase = env('GS_WA_BASE_URL');
                            $gsUserID   = $getChainSettings[0]['GsWAUserID'];
                            $gsPassword = $getChainSettings[0]['GsWAPassword'];

                            $phone = $dataArray["customers"][0]["phone"];
                            $customersName  = $dataArray["customers"][0]["firstName"];

                            $waDetails = [];
                            $waDetails["GsWAUserID"] = $gsUserID;
                            $waDetails["GsWAPassword"] = $gsPassword;
                            Message::whatsappOptIn("91".$phone, $waDetails);

                            //Reedem Membership Array
                            $membershipRedeemed = array();
                            $membershipRedeemed["requestType"]  =  'membershipRedeemed';
                            $membershipRedeemed["storeID"]  =  $storeID;
                            $membershipRedeemed["orderID"]  =  $dataArray["orderID"];
                            $membershipRedeemed["redeemedDetail"] = $memTracker;
                            $membershipRedeemed["customersPhone"] = $phone;
                            $membershipRedeemed["customerID"]     = $customer1;
                            $membershipRedeemed["brandName"]      = $brandName;
                            $membershipRedeemed["timezone"]       = $dataArray["timezone"];
                            //credit settlement
                            $membershipRedeemed["creditsConsumed"]  = $finalCreditsConsumendWA;
                            $membershipRedeemed["creditsLeft"]   = $finalCreditsLeftWA;

                            $queueNumberMembershipRedeemed = queueNumber($chainID);
                            $membershipRedeemedJob = (new MembershipMessageJob($chainID, $membershipRedeemed))->onQueue($queueNumberMembershipRedeemed)->delay(5);
                            dispatch($membershipRedeemedJob);
                            $order["membershipRedeemedJob"] = $membershipRedeemedJob;

                            // $membershipRedeemedResponse = Message::PurchaseMembership($chainID,$membershipRedeemed);
                            // $order["membershipRedeemedJob"] = $membershipRedeemedResponse;
                        }
                    }
                }
            }

            if ($merchantIntegrationModule != "beepkartModule") {
                $accountingCheck = Accounts::checkSumExchange($storeID, $dataArray["orderID"], $dataArray["deviceID"], $dataArray);
            }

            $finalGetOldQuantity = array();
            $finalGetOldBatchVarQuantity = array();
            $getOldCount = count($getOldQuantity);
            $getOldProdBatchCount = count($getOldBatchVarQuantity);
            for ($i=0; $i < $getOldCount; $i++) {
                if (!isset($getOldQuantity[$i]["newQuantity"])) {
                    continue;
                }

                $finalGetOldQuantity[] = $getOldQuantity[$i];
            }

            for ($i=0; $i < $getOldProdBatchCount; $i++) {
                if (!isset($getOldBatchVarQuantity[$i]["newQuantity"])) {
                    continue;
                }

                $finalGetOldBatchVarQuantity[] = $getOldBatchVarQuantity[$i];
            }

            if($enableIRN==1){
                if (!empty($IRNDetail) && $IRNDetail['status'] == true) {
                    $orderSummary['IRNNumber'] = $IRNDetail['IRN'];
                    $orderSummary['irnQRCode'] = $IRNDetail['irnQRCode'];
                    if(isset($IRNDetail['ewaybillsNo']) && $IRNDetail['ewaybillsNo']!=''){
                        $orderSummary['ewayBillNo'] = $IRNDetail['ewaybillsNo'];
                    }
                }
            }
            if($enableGST==1 && $enableIRN!=1 ){
                if(!empty($ewayDetail) && $ewayDetail['status']==true){
                    $orderSummary['ewayBillNo'] = $ewayDetail['ewaybillNo'];
                }
            }


            $discountCouponCheck = array();
            $discountCouponCheck["orderID"] = $orderSummary["orderID"];
            $discountCouponCheck["chainID"] = $chainID;
            $discountCouponCheck["storeID"] = $storeID;
            $discountCouponCheck["customersIDs"] = $orderSummary["customersIDs"];
            $discountCouponCheck["grossBill"] = $orderSummary["grossBill"];
            $discountCouponCheck["timezone"] = $orderSummary["timezone"];
            $orderSummary["couponCodes"] = OldOrder::checkDiscountsWithDistribution($discountCouponCheck);

            // check generate credit notes
            // Send SMS || Maybe add a job?
            // if (isset(self::$creditNotesGenerated) && !empty(self::$creditNotesGenerated)) {
            //     for ($i=0; $i < count(self::$creditNotesGenerated); $i++) {
            //         $creditNoteDetail = self::$creditNotesGenerated[$i];
            //     }
            // }

             //Beepkart Integration Start
            if($merchantIntegrationModule == "beepkartModule"){
                $beepkartArray = $dataArray;
                $queueNumber = queueNumber($chainID);
                $beepkartJob = (new beepkartNewOrderJob($beepkartArray,$chainID))->onQueue($queueNumber)->delay(30);
                Log::info("beepkartJobJSON Manual = ".json_encode($beepkartArray));
				try {
					// save the json to the database
					// jobProcessed = 0, jobJSON = json_encode($beepkartArray), table chain63304_PISummary
					$PINumber = $dataArray['PINumber'];
					if ($PINumber != '' && $PINumber != null) {
						DB::table($PISummaryTable)->where('proformaInvoiceID', $PINumber)->update(['jobProcessed' => 0, 'jobJSON' => json_encode($beepkartArray)]);
					}
				} catch (\Throwable $th) {
					Log::info("Error in saving beepkart jobJSON to the database: ");
					Log::info($th);
				}
                dispatch($beepkartJob);
            }

             //Beepkart Integration End

            /* dispatch general ledger job start */
            if($enableGeneralLedger == 1){
                $generalLedgerArray = array();
                $generalLedgerArray['orderID'] = $dataArray['orderID'];
                $generalLedgerArray['transactionType'] = 'SALES_INVOICE';

                $queueNumber = queueNumber($chainID);
                $generalLedgerJob = (new RecordGeneralLedgerJob($generalLedgerArray,$chainID))->onQueue($queueNumber);
                dispatch($generalLedgerJob);
            }
            /** Dispatch general ledger job end */

            if(isset($dataArray['salesOrderID']) && $dataArray['salesOrderID'] != "" && $dataArray['salesOrderID'] != null){
                DB::table($salesOrderConversionTrackerDetailTable)->where('salesOrderID',$dataArray['salesOrderID'])->where('orderID',$dataArray['orderID'])->update(['status' => 'COMPLETED']);
            }

            $status["status"] = "Created";
            $orderSummary["storeID"] = $storeID;
            $responseArray["orderCreationReport"] = $status;
            $responseArray["updateQuantity"] = $finalGetOldQuantity;
            $responseArray["invoiceNumber"] = self::$finalInvoiceNumber;
            $responseArray["updateProdBatchQuantity"] = $finalGetOldBatchVarQuantity;
            $responseArray["orderModArray"] = isset($orderModArray)?$orderModArray:[];
            $responseArray["getOldModQuantity"] = isset($getOldModQuantity)?$getOldModQuantity:[];
            $responseArray["prodQuantArray"] = isset($prodQuantArray)?$prodQuantArray:[];
            $responseArray["prodBatchArray"] = isset($prodBatchArray)?$prodBatchArray:[];
            $responseArray["batchArray"] = isset($batchArray)?$batchArray:[];
            $responseArray["prodListArray"] = isset($prodListArray)?$prodListArray:[];
            $responseArray['yieldTransactionID'] = self::$yieldTransactionID;
            // $responseArray["getOldQuantityQueryData"] = $getOldQuantityQueryData;
            $responseArray["chainID"] = $chainID;
            $responseArray["orderSummary"] = $orderSummary;
            $responseArray["orderPayment"] = $orderPayments;
            $responseArray["orderAccounting"] = $orderAccounting;
            $responseArray["finalCustArray"] = self::$finalCustArray;
            $responseArray["checkLoyalty"] = self::$checkLoyalty;
            $responseArray["productsList"] = $dataArray["productsList"];
            //$responseArray['reservationList'] = isset($reservationList)?$reservationList:[];
            //$responseArray['reservationList'] = self::$finalReservationDetails;
            $responseArray['reservationLists'] = $reservationDetailsList;
            $responseArray['membershipGUIDs']  = self::$finalMembershipGUID;
            $responseArray['createDLFGiftVoucherJobb']  = self::$createDLFGiftVoucherJobb;
            // $responseArray["oldQuantQuery"] = "SELECT A.variantID, A.inventory,A.inventoryCost,B.batchID FROM $catalogueTable AS A INNER JOIN $productPricesTable AS B ON A.variantID = B.variantID WHERE A.variantID IN ($prodList)";
            // $responseArray["getOldBatchQuantity"] = $getOldBatchQuantity;
            // $responseArray["productsList"] = $dataArray["productsList"];
            $responseArray["creditNotesGenerated"] = self::$creditNotesGenerated;
            $responseArray["customerCreditArray"] = self::$finalCustCredit;
            $responseArray['generalLedgerDispatch'] = $enableGeneralLedger == 1?$generalLedgerJob:NULL;
            $responseArray["inventoryAssetMappingArray"] = $inventoryAssetMappingArray;

            //Start Event For Socket
            if($responseArray["orderCreationReport"]["status"] === "Created"){
                $event['event'] = "SYNC";
                $body['type'] = "ORDER";
                $body['subType'] = "POST";
                $body['force'] = 0;
                $body['time'] = $currentTimeLocal;
                $body['chainID'] = $chainID;
                $body['storeID'] = $storeID;
                $event['body'] = $body;
                $redisResponse = Admin::postEvent($chainID,$event);
                $responseArray['eventMessage'] = $redisResponse['message'];
                //End Event For Socket

                //Start DMS
                if($enableDMS && isset($orderSummary["PINumber"]) && !empty($orderSummary["PINumber"])){
                    $checkPI = convertToArray(DB::table($PISummaryTable)->where("proformaInvoiceID",$orderSummary["PINumber"])->get());
                    $externalOrderID = $checkPI[0]["externalOrderID"];
                    $externalOrderIDArray = explode("-", $externalOrderID);
                    if($externalOrderIDArray[0] == "FC"){
                        $franchiseChainID = $externalOrderIDArray[1];
                        $PONumber = $externalOrderIDArray[2]."-".$externalOrderIDArray[3];
                        // PO summary table is already defined in the tables array from self::getOrderTables
                        $PODetails = convertToArray(DB::table($POSummaryTable)->where("purchaseOrderID",$PONumber)->get());
                        // Vendors table is already defined in the tables array from self::getOrderTables
                        $vendorsDetails = convertToArray(DB::table($vendorsTable)->where("refChainID",$chainID)->get());
                        $dataArray['distributorChainID'] = $chainID;
                        $dataArray['sourceID'] = $PODetails[0]['sourceID'];
                        $dataArray['sourceType'] = $PODetails[0]['sourceType'];
                        $dataArray['vendorID'] = $vendorsDetails[0]['vendorID'];
                        $vendorTransactionDetail = $orderDetail;
                        $vendorTransactionDetailCount = count($vendorTransactionDetail);
                        for($i=0;$i<$vendorTransactionDetailCount;$i++){
                            $vendorTransactionDetail[$i]['quantity'] = $vendorTransactionDetail[$i]['quantityOrdered'];
                        }
                        $vendorTransactionID = "";
                        $dataArray['salesInvoice'] = 1;
                        $queueNumber = queueNumber($franchiseChainID);
                        Log::info("vendorBillJob: ",['dataArray' => $dataArray,'PONumber' => $PONumber,'vendorTransactionDetail' => $vendorTransactionDetail,'vendorTransactionID' => $vendorTransactionID,'franchiseChainID' => $franchiseChainID,'queueNumber' => $queueNumber]);
                        $job = (new vendorBillJob($dataArray,$PONumber,$vendorTransactionDetail,$vendorTransactionID,$franchiseChainID))->onQueue($queueNumber);
                        $responseArray["vendorBillJob"] = "Vendor Bill Job Pushed";
                        dispatch($job);
                    }
                }

                //End DMS

            }

            // unblock coupons
            try {
                $responseArray['unblockCoupons'] = OldOrder::checkAndUnblockCoupon($dataArray, $chainID);
            } catch (\Throwable $th) {
                Log::info($th);
            }

            // unblock vouchers
            try {
                $responseArray['unblockVouchers'] = OldOrder::checkAndUnblockVoucher($dataArray, $chainID);
            } catch (\Throwable $th) {
                Log::info($th);
            }

            // moved to @JobDispatcher
            // // Razorpay Bill Job
            // $customerID = $orderSummary['customersIDs'];
            // if($enableBillMeIntegration==1  && !empty($customerID) && $dataArray['isNoCharge'] == 0){
            //     Log::info("create billme job triggering ".$enableBillMeIntegration);
            //     $queueNumber = queueNumber($chainID);
            //     $jobDataArray = array();
            //     $jobDataArray['orderID'] = $dataArray['orderID'];
            //     $jobDataArray['receipt_type'] = "tax_invoice";
            //     $jobDataArray['type'] = 'creation';
            //     $razorpayinvoiceJob = (new CreateRazorpayInvoice($jobDataArray,$chainID))->onQueue($queueNumber);
            //     dispatch($razorpayinvoiceJob);
            //     //razorPay::createRazorpayInvoice($jobDataArray, $chainID);
            // }


            return $responseArray;

        // }catch (\Exception $e){
        //     echo $e->getMessage();
        // }
    }

    /**
     * Get all tables needed for order processing
     *
     * @param int $storeID The store ID
     * @param int $chainID The chain ID
     * @return array Array of table names
     */
    public static function getOrderTables($storeID, $chainID)
    {
        $tables = [];

        // Store level tables
        $tables['orderDetailTable'] = TableRegistry::getStoreTable($storeID, "orderDetail");
        $tables['orderSummaryTable'] = TableRegistry::getStoreTable($storeID, "orderSummary");
        $tables['orderAccountingTable'] = TableRegistry::getStoreTable($storeID, "orderAccounting");
        $tables['orderPaymentsTable'] = TableRegistry::getStoreTable($storeID, "orderPayments");
        $tables['catalogueTable'] = TableRegistry::getStoreTable($storeID, "catalogue");
        $tables['customerAccountTable'] = TableRegistry::getStoreTable($storeID, "customerAccount");
        $tables['prodVariantCatalogueTable'] = TableRegistry::getStoreTable($storeID, "prodVariantCatalogue");
        $tables['orderModifiersTable'] = TableRegistry::getStoreTable($storeID, "orderModifiers");
        $tables['kotDetailTable'] = TableRegistry::getStoreTable($storeID, "kotDetail");
        $tables['kotVoidDetailTable'] = TableRegistry::getStoreTable($storeID, "kotVoidDetail");
        $tables['orderComboProductsTable'] = TableRegistry::getStoreTable($storeID, "orderComboProducts");

        // Chain level tables
        $tables['chainOrderSummaryTable'] = TableRegistry::getChainTable($chainID, "orderSummary");
        $tables['chainOrderDetailTable'] = TableRegistry::getChainTable($chainID, "orderDetail");
        $tables['chainOrderPaymentsTable'] = TableRegistry::getChainTable($chainID, "orderPayments");
        $tables['customersTable'] = TableRegistry::getChainTable($chainID, "customers");
        $tables['chainCustomerAccountTable'] = TableRegistry::getChainTable($chainID, "customerAccount");
        $tables['PISummaryTable'] = TableRegistry::getChainTable($chainID, "PISummary");
        $tables['chainOrderModifiersTable'] = TableRegistry::getChainTable($chainID, "orderModifiers");
        $tables['appointmentSummaryTable'] = TableRegistry::getChainTable($chainID, "appointmentSummary");
        $tables['appointmentLogsTable'] = TableRegistry::getChainTable($chainID, "appointmentLogs");
        $tables['chainKotDetailTable'] = TableRegistry::getChainTable($chainID, "kotDetail");
        $tables['chainKotVoidDetailTable'] = TableRegistry::getChainTable($chainID, "kotVoidDetail");
        $tables['creditNoteValidationLogsTable'] = TableRegistry::getChainTable($chainID, "creditNoteValidationLogs");
        $tables['creditPaymentSummaryTable'] = TableRegistry::getChainTable($chainID, "creditPaymentSummary");
        $tables['chainOrderComboProductsTable'] = TableRegistry::getChainTable($chainID, "orderComboProducts");
        $tables['membershipPurchaseSummaryTable'] = TableRegistry::getChainTable($chainID, "membershipPurchaseSummary");
        $tables['membershipPurchaseDetailTable'] = TableRegistry::getChainTable($chainID, "membershipPurchaseDetail");
        $tables['membershipTrackerTable'] = TableRegistry::getChainTable($chainID, "membershipTracker");
        $tables['loyaltyPointsLedgerTable'] = TableRegistry::getChainTable($chainID, "loyaltyPointsLedger");
        $tables['chainOrderAccountingTable'] = TableRegistry::getChainTable($chainID, "orderAccounting");
        $tables['chainInvoiceSummaryTable'] = TableRegistry::getChainTable($chainID, "invoiceSummary");
        $tables['chainInvoiceDetailTable'] = TableRegistry::getChainTable($chainID, "invoiceDetail");
        $tables['chainInvoicePaymentsTable'] = TableRegistry::getChainTable($chainID, "invoicePayments");
        $tables['chainInvoiceComboProductsTable'] = TableRegistry::getChainTable($chainID, "invoiceComboProducts");
        $tables['chainInvoiceModifiersTable'] = TableRegistry::getChainTable($chainID, "invoiceModifiers");
        $tables['serviceInfoTable'] = TableRegistry::getChainProductsTable($chainID, "serviceInfo");
        $tables['orderServicePricingSlabsTable'] = TableRegistry::getChainTable($chainID, "orderServicePricingSlabs");
        $tables['membershipInfoTable'] = TableRegistry::getChainProductsTable($chainID, "membershipInfo");
        $tables['salesOrderFulfillmentSummaryTable'] = TableRegistry::getChainTable($chainID, "salesOrderFulfillmentSummary");
        $tables['salesOrderSummaryTable'] = TableRegistry::getChainTable($chainID, "salesOrderSummary");
        $tables['salesOrderDetailTable'] = TableRegistry::getChainTable($chainID, "salesOrderDetail");
        $tables['salesOrderActivityTrackerTable'] = TableRegistry::getChainTable($chainID, "salesOrderActivityTracker");
        $tables['securityInvoiceSummaryTable'] = TableRegistry::getChainTable($chainID, "securityInvoiceSummary");
        $tables['customerLedgerTable'] = TableRegistry::getChainTable($chainID, "customerLedger");
        $tables['channelSalesOrderConfigurationTable'] = TableRegistry::getChainTable($chainID, "channelSalesOrderConfiguration");
        $tables['salesOrderConversionTrackerDetailTable'] = TableRegistry::getChainTable($chainID, "salesOrderConversionTrackerDetail");
        $tables['discountVoucherTable'] = TableRegistry::getChainTable($chainID, "discountVoucher");
        $tables['taxesTable'] = TableRegistry::getChainTable($chainID, "taxes");
        $tables['orderDeliveryTrackerTable'] = TableRegistry::getChainTable($chainID, "orderDeliveryTracker");
        $tables['thirdPartyPaymentIntentTable'] = TableRegistry::getChainTable($chainID, "thirdPartyPaymentIntent");
        $tables['vehicleTrackerTable'] = TableRegistry::getChainTable($chainID, "vehicleTracker");
        $tables['irdOrderSummaryTable'] = TableRegistry::getChainTable($chainID, "irdOrderSummary");
        $tables['userWalletTable'] = TableRegistry::getChainTable($chainID, "userWallet");
        $tables['walletActivityTrackerTable'] = TableRegistry::getChainTable($chainID, "walletActivityTracker");
        $tables['walletMappingTable'] = TableRegistry::getChainTable($chainID, "walletMapping");
        $tables['walletRequestTrackerTable'] = TableRegistry::getChainTable($chainID, "walletRequestTracker");
        $tables['wonderlaIntegrationTrackerTable'] = TableRegistry::getChainTable($chainID, "wonderlaIntegrationTracker");
        $tables['reservationSummaryTable'] = TableRegistry::getChainTable($chainID, "reservationSummary");
        $tables['reservationSlotsTable'] = TableRegistry::getChainTable($chainID, "reservationSlots");
        $tables['reservationTrackerTable'] = TableRegistry::getChainTable($chainID, "reservationTracker");
        $tables['reservationPricingTable'] = TableRegistry::getChainTable($chainID, "reservationPricing");
        $tables['POSummaryTable'] = TableRegistry::getChainTable($chainID, "purchaseOrderSummary");
        $tables['giftVoucherConfTable'] = TableRegistry::getChainTable($chainID,'giftVoucherConf');
        $tables['orderBatchDetailTable'] = TableRegistry::getChainTable($chainID,'orderBatchDetail');

        // Chain inventory tables
        $tables['stockBatchSummaryTable'] = TableRegistry::getChainInventoryTable($chainID, "stockBatchSummary");
        $tables['stockBatchDetailTable'] = TableRegistry::getChainInventoryTable($chainID, "stockBatchDetail");
        $tables['stockTransactionSummaryTable'] = TableRegistry::getChainInventoryTable($chainID, "stockTransactionSummary");
        $tables['stockTransactionDetailTable'] = TableRegistry::getChainInventoryTable($chainID, "stockTransactionDetail");
        $tables['vendorsTable'] = TableRegistry::getChainInventoryTable($chainID, "vendors");

        // Chain products tables
        $tables['productsTable'] = TableRegistry::getChainProductsTable($chainID, "products");
        $tables['productPricesTable'] = TableRegistry::getChainProductsTable($chainID, "productPrices");
        $tables['categoryTable'] = TableRegistry::getChainProductsTable($chainID, "category");
        $tables['brandsTable'] = TableRegistry::getChainProductsTable($chainID, "brands");
        $tables['measurementUnitsTable'] = TableRegistry::getChainTable($chainID, "measurementUnits");
        $tables['productBatchVariantsTable'] = TableRegistry::getChainProductsTable($chainID, "productBatchVariants");
        $tables['modifiersTable'] = TableRegistry::getChainProductsTable($chainID, "modifiers");
        $tables['inventoryAssetTagMappingTable'] = TableRegistry::getChainProductsTable($chainID, "inventoryAssetTagMapping");
        $tables['departmentSalesOrderConfigTable'] = TableRegistry::getChainProductsTable($chainID, "departmentSalesOrderConfig");
        $tables['attributesTable'] = TableRegistry::getChainProductsTable($chainID, "attributes");

        // Global tables
        $tables['listOfChainsTable'] = TableRegistry::getGlobalTable("listOfChains");
        $tables['listOfStoresTable'] = TableRegistry::getGlobalTable("listOfStores");
        $tables['globalTypeValuesTable'] = TableRegistry::getGlobalTable("globalTypeValues");
        $tables['globalEcomOrderSummaryTable'] = TableRegistry::getGlobalTable("globalEcomOrderSummary");
        $tables['usersTable'] = TableRegistry::getGlobalTable("users");
        $tables['chainSettingsTable'] = TableRegistry::getGlobalTable("chainSettings");
        $tables['chainModuleMapTable'] = TableRegistry::getGlobalTable("chainModuleMap");
        $tables['globalEcomStoreSettingsTable'] = TableRegistry::getGlobalTable("globalEcomStoreSettings");
        $tables['devicesInfoTable'] = TableRegistry::getGlobalTable("devicesInfo");
        $tables['globalResetInvoiceTable'] = TableRegistry::getGlobalTable("globalResetInvoice");
        $tables['crmSettingsTable'] = TableRegistry::getGlobalTable("crmSettings");
        $tables['globalChainWhatsappCreditsTable'] = TableRegistry::getGlobalTable("globalChainWhatsappCredits");

        return $tables;
    }


    /**
     * Create order summary array
     *
     * @param int $storeID The store ID
     * @param int $chainID The chain ID
     * @param array $dataArray The data array
     * @param array $tables The tables array
     * @param array $settings The settings array
     * @return array The order summary array
     */
	public static function createOrderSummary($storeID, $chainID, $dataArray, $tables, $settings)
	{
		$orderSummary                 = array();
		$orderSummary["orderID"]      = keyCheck($dataArray, "orderID");
		$orderSummary["currencyCode"] = keyCheck($dataArray, "currency");
		$orderSummary["timezone"]     = keyCheck($dataArray, "timezone");
		$orderSummary["userName"]     = keyCheck($dataArray, "userName");
		$orderSummary["deviceID"]     = keyCheck($dataArray, "deviceID");
		$orderSummary["netBill"]      = keyCheck($dataArray, "netBill");

		$timezone = $dataArray['timezone'];
		date_default_timezone_set($timezone);
		$orderLogTimeLocal = date("Y-m-d H:i:s");
		date_default_timezone_set('UTC');
		$orderLogTimeUTC = date("Y-m-d H:i:s");

		$orderSummary["orderLogTimeLocal"] = $orderLogTimeLocal;
		$orderSummary["orderLogTimeUTC"] = $orderLogTimeUTC;

		$enableOrderReturnWithoutInvoice  = $settings['chain']['enableOrderReturnWithoutInvoice'];
		$customGSTOrderPrefix = $settings['store']['customGSTOrderPrefix'];

		// get chain settings
		$getChainConf = $settings['chain'];
		$isManoramaIntegrationEnabled = $getChainConf['isManoramaIntegrationEnabled'];
		$enableSourceTax = $getChainConf['enableSourceTax'];
		$enableSalesOrder = $getChainConf['enableSalesOrder'];
		$noLoyaltyIfDiscount = 0;
		if (isset($getChainConf['noLoyaltyIfDiscount']) && $getChainConf['noLoyaltyIfDiscount'] != null) {
			$noLoyaltyIfDiscount = $getChainConf['noLoyaltyIfDiscount'];
		}

		if ($isManoramaIntegrationEnabled == 1) {
			if (isset($dataArray["subscriptionOrderType"]) && !empty($dataArray["subscriptionOrderType"])) {
				$orderSummary["subscriptionOrderType"] = $dataArray["subscriptionOrderType"];
			}
		}

		if (isset($dataArray["aircraftID"]) && !empty($dataArray["aircraftID"])) {
			$orderSummary["aircraftID"] = $dataArray["aircraftID"];
		}

		if (isset($dataArray["airlineID"]) && !empty($dataArray["airlineID"])) {
			$orderSummary["airlineID"] = $dataArray["airlineID"];
		}

		if (isset($dataArray["tripID"]) && !empty($dataArray["tripID"])) {
			$orderSummary["tripID"] = $dataArray["tripID"];
		}

		if (isset($dataArray["legID"]) && !empty($dataArray["legID"])) {
			$orderSummary["legID"] = $dataArray["legID"];
		}

		if (isset($dataArray["sectorID"]) && !empty($dataArray["sectorID"])) {
			$orderSummary["sectorID"] = $dataArray["sectorID"];
		}

		if (isset($dataArray["routeID"]) && !empty($dataArray["routeID"])) {
			$orderSummary["routeID"] = $dataArray["routeID"];
		}
		if (isset($dataArray["customers"][0]["seatNumber"]) && !empty($dataArray["customers"][0]["seatNumber"])) {
			$orderSummary["seatNumber"] = $dataArray["customers"][0]["seatNumber"];
		}

		if (isset($dataArray["flightNumber"]) && !empty($dataArray["flightNumber"])) {
			$orderSummary["flightNumber"] = $dataArray["flightNumber"];
		}

		if (isset($dataArray["airlineName"]) && !empty($dataArray["airlineName"])) {
			$orderSummary["airlineName"] = $dataArray["airlineName"];
		}

		if (isset($dataArray["ISRNumber"]) && !empty($dataArray["ISRNumber"])) {
			$orderSummary["ISRNumber"] = $dataArray["ISRNumber"];
		}

		if (isset($dataArray["tableID"])) {
			$orderSummary["tableID"] = $dataArray["tableID"];
		}

		if (isset($dataArray["tableName"])) {
			$orderSummary["tableName"] = $dataArray["tableName"];
		}

		if (isset($dataArray["serverName"])) {
			$orderSummary["serverName"] = $dataArray["serverName"];
		}

		if (isset($dataArray["paxSize"])) {
			$orderSummary["paxSize"] = $dataArray["paxSize"];
		}

		if (isset($dataArray["paymentStatus"])) {
			$orderSummary["paymentStatus"] = $dataArray["paymentStatus"];
		}else{
			$orderSummary["paymentStatus"] = "PAID";
		}

		if (isset($dataArray["orderType"])) {
			$orderSummary["orderType"] = $dataArray["orderType"];
		}else{
			$orderSummary["orderType"] = NULL;
		}

		if (isset($dataArray["orderSource"])) {
			$orderSummary["orderSource"] = $dataArray["orderSource"];
		}else{
			$orderSummary["orderSource"] = 'QB';
		}

		if (isset($dataArray["appointmentID"])) {
			$orderSummary["appointmentID"] = $dataArray["appointmentID"];
		}

		if (isset($dataArray["deliveryStatus"])) {
			$orderSummary["deliveryStatus"] = $dataArray["deliveryStatus"];
		}else{
			$orderSummary["deliveryStatus"] = "DELIVERED";
		}

		if (isset($dataArray["PINumber"]) && !empty($dataArray["PINumber"])) {
			$orderSummary["PINumber"] = $dataArray["PINumber"];
		}else{
			$orderSummary["PINumber"] = NULL;
		}

		if ($enableSalesOrder && isset($dataArray["salesOrderID"]) && !empty($dataArray["salesOrderID"])) {
			$orderSummary["salesOrderID"] = $dataArray["salesOrderID"];
		}else if($enableSalesOrder){
			$orderSummary["salesOrderID"] = NULL;
		}

		if (isset($dataArray["isOrderRounded"])) {
			$orderSummary["isOrderRounded"] = $dataArray["isOrderRounded"];
		}else{
			$orderSummary["isOrderRounded"] = 1;
		}

		if (isset($dataArray["billingUsername"])) {
			$orderSummary["billingUsername"] = $dataArray["billingUsername"];
		}else{
			$orderSummary["billingUsername"] = NULL;
		}

		if (isset($dataArray["loyaltyID"]) && $dataArray["loyaltyID"] != -1) {
			$orderSummary["loyaltyID"] = $dataArray["loyaltyID"];
		} else {
			$purchaseAmount = $settings['chain']['purchaseAmount'] ?? 0;
            $minimumOrderValueToEarnPoints = $settings['chain']['minimumOrderValueToEarnPoints'] ?? 0;
            $orderSummary["loyaltyPointsCollected"] = $settings['chain']['loyaltyPointsCollected'] ?? 0;
            $orderSummary["loyaltyID"] = $settings['chain']['loyaltyID'] ?? 0;
		}

		if (isset($dataArray["loyaltyPointsRedeemed"])) {
			$orderSummary["loyaltyPointsRedeemed"] = $dataArray["loyaltyPointsRedeemed"];
		} else {
			$orderSummary["loyaltyPointsRedeemed"] = 0.00;
		}

		if (isset($dataArray["posDate"])) {
			$orderSummary["posDate"] = $dataArray["posDate"];
		}

		if (isset($dataArray["batchID"])) {
			$orderSummary["batchID"] = $dataArray["batchID"];
		}

		if (isset($dataArray["invoiceNumber"])) {
			$orderSummary["invoiceNumber"] = $dataArray["invoiceNumber"];
		}

		if (isset($dataArray["discounts"])) {
			$discount["Discounts"] = $dataArray["discounts"];
			$discountCount         = count($discount["Discounts"]);
			$discountId            = array();
			$discountName          = array();
			$discountValue         = array();
			for ($j = 0; $j < $discountCount; $j++) {
				$discountId[$j]    = $discount["Discounts"][$j]["discountID"];
				$discountName[$j]  = $discount["Discounts"][$j]["discountName"];
				$discountValue[$j] = $discount["Discounts"][$j]["discountValue"];
			}

			$orderSummary["discountIDs"]        = keyImplode($discountId);
			$orderSummary["discountNames"]      = keyImplode($discountName);
			$orderSummary["discountValues"]     = keyImplode($discountValue);
		}

		if (isset($dataArray["discounts"])) {
			$discount["Discounts"] = $dataArray["discounts"];
			$discountCount         = count($discount["Discounts"]);
			$discountId            = array();
			$discountName          = array();
			$discountValue         = array();
			for ($j = 0; $j < $discountCount; $j++) {
				$discountId[$j]    = $discount["Discounts"][$j]["discountID"];
				$discountName[$j]  = $discount["Discounts"][$j]["discountName"];
				$discountValue[$j] = $discount["Discounts"][$j]["discountValue"];
			}

			$orderSummary["discountIDs"]        = keyImplode($discountId);
			$orderSummary["discountNames"]      = keyImplode($discountName);
			$orderSummary["discountValues"]     = keyImplode($discountValue);
		}
		
		$orderSummary["discountTotalValue"] = $dataArray["discountValue"];

		if (isset($dataArray['isOpenBill'])) {
			$orderSummary['isOpenBill'] = $dataArray['isOpenBill'];
		}else{
			$orderSummary['isOpenBill'] = 0;
		}

		if (isset($dataArray['openBill']['isPriceInclusive'])) {
			$orderSummary['isPriceInclusive'] = $dataArray['openBill']['isPriceInclusive'];
		}else{
			$orderSummary['isPriceInclusive'] = 0;
		}

		if (isset($dataArray['openBill']['openBillAmount'])) {
			$orderSummary['openBillAmount'] = $dataArray['openBill']['openBillAmount'];
		}else{
			$orderSummary['openBillAmount'] = 0.00;
		}

		if (isset($dataArray['isOpenBill'])) {
			$orderSummary['isOpenBill'] = $dataArray['isOpenBill'];
		}else{
			$orderSummary['isOpenBill'] = 0;
		}

		if (isset($dataArray['isQuickBill'])) {
			$orderSummary['isQuickBill'] = $dataArray['isQuickBill'];
		}else{
			$orderSummary['isQuickBill'] = 0;
		}

		if (isset($dataArray["openBill"]['openBillTaxes']) && !empty($dataArray["openBill"]['openBillTaxes'])) {
			$taxCount         = count($dataArray["openBill"]['openBillTaxes']);
			$taxID            = array();
			$taxName          = array();
			$taxValue         = array();
			$taxPercentage    = array();
			for ($j = 0; $j < $taxCount; $j++) {
				$taxID[$j]    = $dataArray["openBill"]['openBillTaxes'][$j]["taxID"];
				$taxName[$j]  = $dataArray["openBill"]['openBillTaxes'][$j]["taxName"];
				$taxPercentage[$j]  = $dataArray["openBill"]['openBillTaxes'][$j]["taxPercentage"];
				$taxValue[$j] = $dataArray["openBill"]['openBillTaxes'][$j]["taxValue"];
			}            
			$orderSummary["openBillTaxIDs"]        = keyImplode($taxID);
			$orderSummary["openBillTaxNames"]      = keyImplode($taxName);
			$orderSummary["openBillTaxPercentages"]     = keyImplode($taxPercentage);
			$orderSummary["openBillTaxValues"]     = keyImplode($taxValue);
		}

		if (isset($dataArray["additionalCharges"])) {
			$additionalCharges["Additional Charges"] = $dataArray["additionalCharges"];
			$additonalChargeCount                    = count($additionalCharges["Additional Charges"]);
			$additionalChargeId                       = array();
			$additonalChargeName                     = array();
			$additonalChargePrice                    = array();
			$additonalChargeValue                    = array();

			$orderTaxArray = array();
			for ($j = 0; $j < $additonalChargeCount; $j++) {
				$additionalChargeId[$j]    = $additionalCharges["Additional Charges"][$j]["additionalChargeID"];
				$additionalChargeName[$j]  = $additionalCharges["Additional Charges"][$j]["additionalChargeName"];
				$additionalChargePrice[$j] = $additionalCharges["Additional Charges"][$j]["additionalChargePrice"];
				$additionalChargeValue[$j] = $additionalCharges["Additional Charges"][$j]["additionalChargeValue"];

				if (isset($additionalCharges["Additional Charges"][$j]["taxes"]) && !empty($additionalCharges["Additional Charges"][$j]["taxes"])) {
					$orTaxCount  = count($additionalCharges["Additional Charges"][$j]["taxes"]);

					for ($k=0; $k < $orTaxCount; $k++) {
						$taxID = $additionalCharges["Additional Charges"][$j]["taxes"][$k]["taxID"]; 
						$orTaxKey = array_search($taxID, array_column($orderTaxArray, 'taxID'));
						if (is_int($orTaxKey)) {
							$orderTaxArray[$orTaxKey]["taxValue"]+= $additionalCharges["Additional Charges"][$j]["taxes"][$k]["taxValue"];
						} else {
							$tempTaxArray = array();
							$tempTaxArray["taxID"] = $taxID;
							$tempTaxArray["taxName"] = $additionalCharges["Additional Charges"][$j]["taxes"][$k]["taxName"];
							$tempTaxArray["taxPercentage"] = $additionalCharges["Additional Charges"][$j]["taxes"][$k]["taxPercentage"];

							$tempTaxArray["taxValue"] = $additionalCharges["Additional Charges"][$j]["taxes"][$k]["taxValue"];

							$orderTaxArray[] = $tempTaxArray;
						}
					}
				}

			}

			if(!empty($additionalChargeId)){
				$orderSummary["additionalChargeIDs"]    = keyImplode($additionalChargeId);    
			}else{
				$orderSummary["additionalChargeIDs"]    = NULL;
			}

			if(!empty($additionalChargeName)){
				$orderSummary["additionalChargeNames"]  = keyImplode($additionalChargeName);
			}else{
				$orderSummary["additionalChargeNames"]    = NULL;
			}

			if(!empty($additionalChargePrice)){
				$orderSummary["additionalChargePrices"] = keyImplode($additionalChargePrice);
			}else{
				$orderSummary["additionalChargePrices"]    = NULL;
			}

			if(!empty($additionalChargeValue)){
				$orderSummary["additionalChargeValue"] = keyImplode($additionalChargeValue);
			}else{
				$orderSummary["additionalChargeValue"]    = NULL;
			}
			
			if (!empty($orderTaxArray)) {
				$orTaxCount = count($orderTaxArray);
				$orderTaxIDArray = array();
				$orderTaxNameArray = array();
				$orderTaxPercentageArray = array();
				$orderTaxValueArray = array();
				$orderTotalTaxValue = 0;
				for ($i=0; $i < $orTaxCount; $i++) { 
					array_push($orderTaxIDArray, $orderTaxArray[$i]["taxID"]);
					array_push($orderTaxNameArray, $orderTaxArray[$i]["taxName"]);
					array_push($orderTaxPercentageArray, $orderTaxArray[$i]["taxPercentage"]);
					array_push($orderTaxValueArray, $orderTaxArray[$i]["taxValue"]);
					$orderTotalTaxValue +=$orderTaxArray[$i]["taxValue"];
				}

				$orderSummary["orderTaxIDs"] = rtrim(implode(",", $orderTaxIDArray));
				$orderSummary["orderTaxNames"] = rtrim(implode(",", $orderTaxNameArray));
				$orderSummary["orderTaxPercentages"] = rtrim(implode(",", $orderTaxPercentageArray));
				$orderSummary["orderTaxValues"] = rtrim(implode(",", $orderTaxValueArray));
				$orderSummary["orderTotalTaxValue"] = $orderTotalTaxValue;
			}
		}

		if (isset($dataArray["totalProductLevelAdditionalCharges"])) {
			$orderSummary["totalProductLevelAdditionalCharges"] = $dataArray["totalProductLevelAdditionalCharges"];
		}else{
			$orderSummary["totalProductLevelAdditionalCharges"] = 0.0000;
		}

		if (isset($dataArray["totalChargeValue"])) {
			$orderSummary["totalChargeValue"] = keyCheck($dataArray, "totalChargeValue");
		}else{
			$orderSummary["totalChargeValue"] = 0.0000;
		}
		
		$orderSummary['storeGstNumber'] = isset($dataArray['storeGstNumber']) ? $dataArray['storeGstNumber'] : NULL;
		$orderSummary['storeFssaiNo'] = isset($dataArray['storeFssaiNo']) ? $dataArray['storeFssaiNo'] : NULL;
		$orderSummary['storePanNumber'] = isset($dataArray['storePanNumber']) ? $dataArray['storePanNumber'] : NULL;
		$orderSummary['storeTinNumber'] = isset($dataArray['storeTinNumber']) ? $dataArray['storeTinNumber'] : NULL;
		/** handle TDS in advance payment start */
		if($enableSourceTax == 1 && isset($dataArray["TDSValue"]) && $dataArray["TDSValue"] > 0){
			if (isset($dataArray["TDSID"])) {
				$orderSummary["TDSID"] = keyCheck($dataArray, "TDSID");
			}else{
				$orderSummary["TDSID"] = 0;
			}
			
			if (isset($dataArray["TDSName"])) {
				$orderSummary["TDSName"] = keyCheck($dataArray, "TDSName");
			}else{
				$orderSummary["TDSName"] = 0;
			}

			if (isset($dataArray["TDSPercentage"])) {
				$orderSummary["TDSPercentage"] = keyCheck($dataArray, "TDSPercentage");
			}else{
				$orderSummary["TDSPercentage"] = 0;
			}

			if (isset($dataArray["TDSValue"])) {
				$orderSummary["TDSValue"] = keyCheck($dataArray, "TDSValue");
			}else{
				$orderSummary["TDSValue"] = 0;
			}
		}
		/** handle TDS in advance payment end */

		/** handle TCS in advance payment start */
		if($enableSourceTax == 1 && isset($dataArray["TCSValue"]) && $dataArray["TCSValue"] > 0) {
			if (isset($dataArray["TCSID"])) {
				$orderSummary["TCSID"] = keyCheck($dataArray, "TCSID");
			}else{
					$orderSummary["TCSID"] = 0;
			}
			
			if (isset($dataArray["TCSName"])) {
				$orderSummary["TCSName"] = keyCheck($dataArray, "TCSName");
			}else{
				$orderSummary["TCSName"] = 0;
			}

			if (isset($dataArray["TCSPercentage"])) {
				$orderSummary["TCSPercentage"] = keyCheck($dataArray, "TCSPercentage");
			}else{
				$orderSummary["TCSPercentage"] = 0;
			}

			if (isset($dataArray["TCSValue"])) {
				$orderSummary["TCSValue"] = keyCheck($dataArray, "TCSValue");
			}else{
				$orderSummary["TCSValue"] = 0;
			}
			
			$TCSAccountArray["orderID"]     = $creditTransactionID;
			$TCSAccountArray["timezone"]    = $timezone;
			$TCSAccountArray["orderSubID"]  = 0;
			$TCSAccountArray["transactionQuantity"]  = 0;                    
			$TCSAccountArray["accountType"] = "ACC_TCS";
			$TCSAccountArray["posDate"]     = $posDate;
			
			$TCSAccountArray["accountID"]            = $dataArray["TCSAccountID"];

			$TCSAccountArray["accountName"]          = $dataArray["TCSAccountName"];;
			$TCSAccountArray["debit"]                = 0.0000;
			$TCSAccountArray["credit"]               = $dataArray["TCSValue"];
			$TCSAccountArray["exchange"]             = $TCSAccountArray["debit"] - $TCSAccountArray["credit"];                
			$TCSAccountArray["transactionTypeID"]    = $addMoneyTransactionType;

			$TCSAccountArray["transactionTimeLocal"] = $transactionTimeLocal;
			$TCSAccountArray["transactionTimeUTC"]   = $transactionTimeUTC;
			$TCSAccountArray["accountLogTimeLocal"] = $orderLogTimeLocal;
			$TCSAccountArray["accountLogTimeUTC"] = localtoUTC($accountLogTimeLocal, $timezone);
			$TCSAccountArray["transactionUser"] = $userName;

			$orderAccountingArray[] = $TCSAccountArray;
		
		}
		/** handle TCS in advance payment end */

		
		if (isset($dataArray["isNoCharge"])) {
			$orderSummary["isNoCharge"] = $dataArray["isNoCharge"];
		}else{
			$orderSummary["isNoCharge"] = 0;
		}
		if($enableOrderReturnWithoutInvoice[0]['enableOrderReturnWithoutInvoice'] == 1){
			if (isset($dataArray["isNoInvoiceSale"])) {
				$orderSummary["isNoInvoiceSale"] = $dataArray["isNoInvoiceSale"];
			}else{
				$orderSummary["isNoInvoiceSale"] = 0;
			}
		}
		
		$orderSummary["taxes"] = keyCheck($dataArray, "taxes");


		$orderSummary["rounding"]       = keyCheck($dataArray, "rounding");
		$orderSummary["grossBill"]      = keyCheck($dataArray, "grossBill");
		if (isset($dataArray["payableAmount"])) {
			$orderSummary["payableAmount"] = $dataArray["payableAmount"];
		}else{
			$orderSummary["payableAmount"] = $dataArray["grossBill"];
		}

		if ($noLoyaltyIfDiscount == 1 && $orderSummary["discountTotalValue"] > 0) {
			// Do not give loyalty points if order has discount applied
			$orderSummary["loyaltyPointsCollected"] = 0;
		} else {
			/**
			 * This code block calculates the loyalty points collected for an order based on the purchase amount and minimum order value to earn points.
			 * It checks if the final customer ID is set and greater than 0, retrieves the purchase amount and minimum order value to earn points from the database.
			 * If the purchase amount and minimum order value are not found, the loyalty points collected is set to 0.
			 * If CRM is enabled and points earning is enabled for the store, it checks if the gross bill of the order is greater than or equal to the purchase amount and minimum order value.
			 * If the gross bill is less than the purchase amount or minimum order value, the loyalty points collected is set to 0.
			 * If the purchase amount is 0 or the order is marked as no charge, the loyalty points collected is set to 0.
			 * If a loyalty payment is found in the payment list, it subtracts the loyalty payment amount from the gross bill and calculates the loyalty points based on the new bill.
			 * If no loyalty payment is found, it calculates the loyalty points based on the original gross bill.
			 * Finally, if the customer IDs are set, it assigns the customer IDs to the order summary.
			 */
			if (isset($dataArray["finalCustomerID"]) && $dataArray["finalCustomerID"] > 0) {
				$crmEnabled = checkCRMEligibility($chainID, $storeID, $dataArray["finalCustomerID"], $settings['store']);
				if ($crmEnabled["enableCRM"] == 1 && $crmEnabled["enablePointsEarning"] == 1) {
					if ($orderSummary["grossBill"] < $purchaseAmount || $orderSummary["grossBill"] < $minimumOrderValueToEarnPoints) {
						$orderSummary["loyaltyPointsCollected"] = 0;
					} else {
						if ($purchaseAmount == 0 || $orderSummary["isNoCharge"] == 1) {
							$orderSummary["loyaltyPointsCollected"] = 0;
						}else{
							$loyaltyPaymentKey = array_search("PAYMENT_LOYALTY", array_column($dataArray["paymentList"], 'paymentType'));

							if (is_int($loyaltyPaymentKey)) {
								$newBill = $orderSummary["grossBill"] - $dataArray["paymentList"][$loyaltyPaymentKey]["amount"];

								$orderSummary["loyaltyPointsCollected"] = intval($newBill/$purchaseAmount);    
							}else{
								$orderSummary["loyaltyPointsCollected"] = intval($orderSummary["grossBill"]/$purchaseAmount);    
							}
											
						}
					}   
				}
			}
		}
		if (isset($dataArray["customerIDs"])) {
			$orderSummary["customerIDs"]    = keyCheck($dataArray, "customerIDs");    
		}
		
		if (isset($dataArray["serverName"])) {
			$orderSummary["serverName"] = $dataArray["serverName"];
		}
		
		$orderSummary["orderCreationTimeLocal"] = keyCheck($dataArray, "orderCreationTimeLocal");
		
		if (isset($orderSummary["orderCreationTimeLocal"])) {
			$orderSummary["orderCreationTimeUTC"] = localToUTC($orderSummary["orderCreationTimeLocal"], $orderSummary["timezone"]);
		}

		if (isset($dataArray["pineLabsClientID"]) && !empty($dataArray["pineLabsClientID"])) {
			$orderSummary["dummy1"] = $dataArray["pineLabsClientID"];
		}

		if (isset($customGSTOrderPrefix) && !empty($customGSTOrderPrefix) && !empty($orderSummary["invoiceNumber"])) {
			$orderSummary["gstOrderNumber"] = $customGSTOrderPrefix."-".$orderSummary["invoiceNumber"];
		}

		if(isset($dataArray['IRNNumber']) && $dataArray['IRNNumber']!='' && $getChainConf['enableIRN'] ==1){
			$orderSummary['IRNNumber'] = $dataArray['IRNNumber'];
		}
		if(isset($dataArray['irnQRCode']) && $dataArray['irnQRCode']!='' && $getChainConf['enableIRN'] ==1){
			$orderSummary['irnQRCode'] = $dataArray['irnQRCode'];
		}

		if(isset($dataArray['ewayBillNo']) && $dataArray['ewayBillNo']!=''){
			$orderSummary['ewayBillNo'] = $dataArray['ewayBillNo'];
		}

		if(isset($dataArray['CANumber']) && $dataArray['CANumber']!=''){
			$orderSummary['dummy2'] = $dataArray['CANumber'];
		}

		$enableCounterManagement = $getChainConf['enableCounterManagement'];
		if(isset($dataArray['counterID']) && $dataArray['counterID'] > 0 && $enableCounterManagement == 1){
			$orderSummary['counterID'] = $dataArray['counterID'];
		}
				
		$orderSummary["cashTendered"] = keyCheck($dataArray, "cashTendered");
		$orderSummary["changeAmount"] = keyCheck($dataArray, "changeAmount");
		
		$orderSummary["orderRemarks"]     = keyCheck($dataArray, "orderRemarks");
		$orderSummary["numReceiptPrints"] = keyCheck($dataArray, "numReceiptPrints");
		$orderSummary["Status"]           = $dataArray["Status"];

		if(isset($dataArray['departmentConfigID']))
		{
			$orderSummary["departmentConfigID"]   = $dataArray['departmentConfigID'];
		}

		// check if municiapl enable 
		$enableMunicipalIntegration = $settings['store']['enableMunicipalIntegration'];
		$municipalType = $settings['store']['municipalType'];
		if($enableMunicipalIntegration == 1 && strtoupper($municipalType) == 'PUNJAB'){
			$orderSummary['MUNBillNumber'] = $dataArray['billDetailForPunjabMunciple'];
			$orderSummary['MUNChallanNumber'] = $dataArray['challanNumberForPunjabMunciple'];
		}

		// enableChannelManagement
		if ($getChainConf['enableChannelManagement'] == 1) {
			if (isset($dataArray['channelId']) && intval($dataArray['channelId']) > 0) {
				$orderSummary['channelID'] = intval($dataArray['channelId']);
			}
			if (isset($dataArray['channelID']) && intval($dataArray['channelID']) > 0) {
				$orderSummary['channelID'] = intval($dataArray['channelID']);
			}
		}

		//sales person details
		if(isset($dataArray['salesPersonID']) && $dataArray['salesPersonID'] > 0){
			$orderSummary['salesPersonID'] = $dataArray['salesPersonID'];
		}

		if(isset($dataArray['salesPersonName']) && $dataArray['salesPersonName'] != ''){
			$orderSummary['salesPersonName'] = $dataArray['salesPersonName'];
		}

		if(isset($dataArray['salesPersonPhone']) && $dataArray['salesPersonPhone'] != ''){
			$orderSummary['salesPersonPhone'] = $dataArray['salesPersonPhone'];
		}

		if(isset($dataArray['salesPersonEmail']) && $dataArray['salesPersonEmail'] != ''){
			$orderSummary['salesPersonEmail'] = $dataArray['salesPersonEmail'];
		}

		return $orderSummary;
	}

}
