<?php
ini_set("memory_limit", -1);
use Illuminate\Support\Facades\Log AS Log;

trait Constants {
    private static $merchantTablesArray = array('devices' => 'devices',
                    'catalogue' => 'catalogue',
                    'prodVariantCatalogue' => 'prodVariantCatalogue',
                    'batchCatalogue' => 'batchCatalogue',
                    'accountIDs' => 'accountIDs',
                    'accounting' => 'accounting',
                    'orderAccounting' => 'orderAccounting',
                    'orderSummary' => 'orderSummary',
                    'cashierSummary' => 'cashierSummary',
                    'orderDetail' => 'orderDetail',
                    'orderModifiers' => 'orderModifiers',
                    'orderComboProducts' => 'orderComboProducts',
                    'taxes' => 'taxes',
                    'usersPermissionsMapping' => 'usersPermissionsMapping',
                    'usersPermissionsGroupsMap' => 'usersPermissionsGroupsMap',
                    'additionalCharges' => 'additionalCharges',
                    'role' => 'role',
                    'modifiers' => 'modifiers',
                    'modifierProdMap' => 'modifierProdMap',
                    'modifierGroupsMap' => 'modifierGroupsMap',
                    'modifierGroups' => 'modifierGroups',
                    'modifierGroupProductMap' => 'modifierGroupProductMap',
                    'products' => 'products',
                    'productPrices' => 'productPrices',
                    'menu' => 'menu',
                    'customerAccount' => 'customerAccount',
                    'listOfMenu' => 'listOfMenu',
                    'category' => 'category',
                    'subCategory' => 'subCategory',
                    'catSubCatMap' => 'catSubCatMap',
                    'combos' => 'combos',
                    'comboGroups' => 'comboGroups',
                    'menuCombos' => 'menuCombos',
                    'discountOutletMap' => 'discountOutletMap',
                    'discountVoucher' => 'discountVoucher',
                    'discountSlots' => 'discountSlots',
                    'paymentSettings' => 'paymentSettings',
                    'recipeList' => 'IM_recipeList',
                    'recipeDetails' => 'IM_recipeDetails',
                    'inventoryOrderDetail' =>'IM_inventoryOrderDetail',
                    'courseType' => 'courseType',
                    'daysInfo' => 'daysInfo',
                    'posDateLog' => 'posDateLog',
                    'kotDetail' => 'kotDetail',
                    'kotVoidDetail' => 'kotVoidDetail',
                    'voidOrderSummary' => 'voidOrderSummary',
                    'receipt' => 'receipt',
                    'partyMaster' => 'partyMaster',                    
                    'printerAssignment' => 'printerAssignment',
                    'bankingSummary' => 'bankingSummary',
                    'bankingDetail' => 'bankingDetail',
                    'productCharges' => 'productCharges',
                    'orderPayments' => 'orderPayments',
                    'deviceProdMap' => 'deviceProdMap',
                    'batchInfo' => 'cashierSummary',
                    'voidOrderSummary' => 'voidOrderSummary',
                    'kotVoidDetail' => 'kotVoidDetail',
                    'logs' => 'logs',
                    'eposApiCallLogs' => 'eposApiCallLogs',
                    'currentTableStatus'=> 'currentTableStatus',
                    'categoryMapping' => 'categoryMapping'
                    );

  private static $chainTablesArray = array('customers' => 'customers',
                      'visitorConfig'=>'visitorConfig','visitorConfigDiscountMap'=>'visitorConfigDiscountMap','visitors'=>'visitors','visitorLogs'=>'visitorLogs','visitorRewardTracker'=>'visitorRewardTracker',
                      'wonderlaStockSummary'=>'wonderlaStockSummary',
                      'wonderlaStockDetail'=>'wonderlaStockDetail',
                      'wonderlaRevenueSummary'=>'wonderlaRevenueSummary',
                      'wonderlaRevenueDetail'=>'wonderlaRevenueDetail',
                      'deviceOrderSettlementLogs' => 'deviceOrderSettlementLogs',
                      'reservationFilterGroups' => 'reservationFilterGroups',
                      'reservationFilters' => 'reservationFilters',
                      'invoiceSummary' => 'invoiceSummary',
                      'invoiceDetail' => 'invoiceDetail',
                      'shipments' => 'shipments',
                      'invoicePayments' => 'invoicePayments',
                      'invoiceComboProducts' => 'invoiceComboProducts',
                      'invoiceModifiers' => 'invoiceModifiers',
                      'MQLoyaltyPointLedger' => 'MQLoyaltyPointLedger',
                      'easyRewardz' => 'easyRewardz',
                      'giftVoucherConf' => 'giftVoucherConf',                  
                      'stockLedgerSummary' => 'stockLedgerSummary',
                      'stockLedgerDetail' => 'stockLedgerDetail',   
                      'customerOrderMapping' => 'customerOrderMapping',
                      'customersAddress' => 'customersAddress',
                      'rawProducts' => 'rawProducts',
                      'sizes' => 'sizes',
                      'colours' => 'colours',
                      'styles' => 'styles',
                      'brands' => 'brands',
                      'customerAccount'=>'customerAccount',
                      'vendors' => 'vendors',
                      'taxes' => 'taxes',
                      'taxGroups' => 'taxGroups',
                      'taxTracker' => 'taxTracker',
                      'additionalCharges' => 'additionalCharges',
                      'accountIDs' => 'accountIDs',
                      'accountList' => 'accountList',
                      'accounting' => 'accounting',
                      'kotDetail' => 'kotDetail',
                      'kotVoidDetail' => 'kotVoidDetail',                      
                      'creditPaymentSummary' => 'creditPaymentSummary',
                      'creditPaymentDetail' => 'creditPaymentDetail',
                      'purchaseCreditPaymentSummary' => 'purchaseCreditPaymentSummary',
                      'purchaseCreditPaymentDetail' => 'purchaseCreditPaymentDetail',
                      'expenseCreditPaymentSummary' => 'expenseCreditPaymentSummary',
                      'expenseCreditPaymentDetail' => 'expenseCreditPaymentDetail',
                      'PISummary' => 'PISummary',
                      'PIDetail' => 'PIDetail',
                      'PIPayments' => 'PIPayments',
                      'floor'=>'floor',
                      'room' =>'room',
                      'table' =>'table',                      
                      'PIModifiers' => 'PIModifiers',
                      'PIComboProducts' => 'PIComboProducts',
                      'modifiers' => 'modifiers',
                      'modifierProdMap' => 'modifierProdMap',
                      'modifierGroupsMap' => 'modifierGroupsMap',
                      'modifierGroups' => 'modifierGroups',
                      'modifierGroupProductMap' => 'modifierGroupProductMap',                      
                      'vendorInvoiceMgmt' => 'vendorInvoiceMgmt',
                      'vendorInvoicePayment' => 'vendorInvoicePayment',
                      'warehouse' => 'restaurantWarehouseMap',
                      'warehouses' => 'warehouses',
                      'discountMaster' => 'discountMaster',
                      'discountVoucher' => 'discountVoucher',
                      'discountOutletMap' => 'discountOutletMap',
                      'taxOutletMap' => 'taxOutletMap',
                      'additionalChargeOutletMap' => 'additionalChargeOutletMap',
                      'discountProductMap' => 'discountProductMap',
                      'discountSlots' => 'discountSlots',
                      'discountTargetProductList' => 'discountTargetProductList',
                      'creditNoteValidationLogs' => 'creditNoteValidationLogs',
                      'orderSummary' => 'orderSummary',
                      'orderDetail' => 'orderDetail',
                      'orderPayments' => 'orderPayments',
                      'orderAccounting' => 'orderAccounting',
                      'orderModifiers' => 'orderModifiers',
                      'orderComboProducts' => 'orderComboProducts',
                      'purchaseOrderSummary' => 'purchaseOrderSummary',
                      'purchaseOrderDetail' => 'purchaseOrderDetail',
                      'purchaseOrderPayments' => 'purchaseOrderPayments',
                      'purchaseOrderAccounting' => 'purchaseOrderAccounting',
                      'expenseOrderSummary' => 'expenseOrderSummary',
                      'expenseOrderDetail' => 'expenseOrderDetail',
                      'expenseOrderPayments' => 'expenseOrderPayments',
                      'expenseOrderAccounting' => 'expenseOrderAccounting',
                      'products' => 'products',
                      'usersPermissionsMapping' => 'usersPermissionsMapping',
                      'usersPermissionsGroupsMap' => 'usersPermissionsGroupsMap',
                      'productPrices' => 'productPrices',
                      'productAttributes' => 'productAttributes',
                      'productBatchVariants' => 'productBatchVariants',
                      'category' => 'category',
                      'subCategory' => 'subCategory',
                      'catSubCatMap' => 'catSubCatMap',
                      'comboGroups' => 'comboGroups',
                      'comboGroupProducts' => 'comboGroupProducts',
                      'courseType' => 'courseType',
                      'partyMaster' => 'partyMaster',
                      'partyMasterLedger' => 'partyMasterLedger',
                      'partyTypes' => 'partyTypes',
                      "partyMasterLedger" => "partyMasterLedger",
                      "partyPaymentSummary" => "partyPaymentSummary",
                      "partyMasterCustomerMapping" => "partyMasterCustomerMapping",
                      'partyMasterRegionMapping' => 'partyMasterRegionMapping',
                      'recipeList' => 'recipeList',
                      'recipeDetails' => 'recipeDetails',
                      'generalSettings' => 'generalSettings',
                      'remarks' => 'remarks',
                      'incomeHead' => 'incomeHead',
                      'measurementUnits' => 'measurementUnits',
                      'unitConversions' => 'unitConversions',
                      'userWarehouseMap' => 'userWarehouseMap',
                      'stockBatchSummary' => 'stockBatchSummary',
                      'stockBatchDetail' => 'stockBatchDetail',
                      'orderBatchDetail' => 'orderBatchDetail',
                      'stockTransactionSummary' => 'stockTransactionSummary',
                      'stockTransactionDetail' => 'stockTransactionDetail',
                      'stockDiscrepancyDetail' => 'stockDiscrepancyDetail',
                      'stockRequisitionSummary' => 'stockRequisitionSummary',
                      'stockRequisitionDetail' =>'stockRequisitionDetail',
                      'logs' => 'logs',
                      'roles' => 'roles',
                      'rolePermissionsMapping' => 'rolePermissionsMapping',
                      'campaigns' => 'campaigns',
                      'campaignCustomerMap' => 'campaignCustomerMap',
                      'sentMessagesHistory' => 'sentMessagesHistory',
                      'prescription' => 'prescription',
                      'appointmentSummary' => 'appointmentSummary',
                      'appointmentDetail' => 'appointmentDetail',
                      'serviceInfo' => 'serviceInfo',
                      'servicePricingSlabs' => 'servicePricingSlabs',
                      'appointmentLogs' => 'appointmentLogs',
                      'flightTrips' => 'flightTrips',
                      'flightLegs' => 'flightLegs',
                      'tripPeriod' => 'tripPeriod',
                      'membershipInfo' => 'membershipInfo',
                      'membershipProducts' => 'membershipProducts',
                      'regions' => 'regions',
                      'regionStoreMap' => 'regionStoreMap',
                      'currencyConversions' => 'currencyConversions',
                      'tenderCurrencyValidation' => 'tenderCurrencyValidation',
                      'membershipPurchaseSummary' => 'membershipPurchaseSummary',
                      'membershipPurchaseDetail' => 'membershipPurchaseDetail',
                      'membershipTracker' => 'membershipTracker',
                      'orderDeliveryTracker' => 'orderDeliveryTracker',
                      'incomeHead' => 'incomeHead',
                      'stockAuditSummary' => 'stockAuditSummary',
                      'stockAuditDetail' => 'stockAuditDetail',
                      'userAttendance' => 'userAttendance',
                      'cashManagement' => 'cashManagement',
                      'feedbackTracker' => 'feedbackTracker',
                      'receivedStockHistory' => 'receivedStockHistory',
                      'stockConsumptionLedger' => 'stockConsumptionLedger',
                      'banners' => 'banners',
                      'bannerOutletMap' => 'bannerOutletMap',
                      'attributes' => 'attributes',
                      'systemAttributes' => 'systemAttributes',
                      'reservationSummary' => 'reservationSummary',
                      'reservationSettings' => 'reservationSettings',
                      'reservationPricing' => 'reservationPricing',
                      'reservationSlots' => 'reservationSlots',
                      'reservationTracker' => 'reservationTracker',
                      'requestTracker' => 'requestTracker',
                      'customerLedger' => 'customerLedger',
                      'customerLedgerSettlementLogs' => 'customerLedgerSettlementLogs',
                      'customerLedgerAccounting' => 'customerLedgerAccounting',
                      'customerLedgerBalance' => 'customerLedgerBalance',
                      'localityOrderingSlots' => 'localityOrderingSlots',
                      'storeLocalityMapping' => 'storeLocalityMapping',
                      'orderDispatchSummary' => 'orderDispatchSummary',
                      'orderDispatchDetail' => 'orderDispatchDetail',
                      'customerLedgerImages' => 'customerLedgerImages',
                      'stockTransferConf' => 'stockTransferConf',
                      'vendorProductMapping' => 'vendorProductMapping',
                      'vendorSourceMapping' => 'vendorSourceMapping',
                      'activityLogs' => 'activityLogs',
                      'irdOrderSummary' => 'irdOrderSummary',
                      'ewaybills' => 'ewaybills',
                      'salesAreas' => 'salesAreas',
                      'salesZones' => 'salesZones',
                      'salesmanCustomerMapping' => 'salesmanCustomerMapping',
                      'salesmanCategoryMapping' => 'salesmanCategoryMapping',
                      'salesAssessmentPeriod' => 'salesAssessmentPeriod',
                      'salesBeatPeriod' => 'salesBeatPeriod',
                      'salesBeatCustomerMap' => 'salesBeatCustomerMap',
                      'salesmanTripTracker' => 'salesmanTripTracker',
                      'salesmanBeatTargetProductLevel' => 'salesmanBeatTargetProductLevel',
                      'customerTargetProductLevelTemplate' => 'customerTargetProductLevelTemplate',
                      'salesmanBeatTargetInvoiceLevel' => 'salesmanBeatTargetInvoiceLevel',
                      'manufacturers' => 'manufacturers',
                      'loyaltyPointsLedger' => 'loyaltyPointsLedger' ,
                      'thirdPartyPaymentIntent' => 'thirdPartyPaymentIntent',
                      'paymentIntentStatus' => 'paymentIntentStatus',
                      'orderAccounting' => 'orderAccounting',
                      'discountCoupons' => 'discountCoupons',
                      'storeCounters' => 'storeCounters',
                      'counterProductMap' => 'counterProductMap',
                      'userCounterMap' => 'userCounterMap',
                      'reservationStoreMap' => 'reservationStoreMap',                    
                      'PIDeliveryInfo' =>'PIDeliveryInfo',
                      'PIOrderRating' =>'PIOrderRating',
                      'PICustomerOrderComplaints' =>'PICustomerOrderComplaints',
                      'PICustomerComplaintsOrderItems' =>'PICustomerComplaintsOrderItems',
                      'PICustomerComplaintsOrderRefund' =>'PICustomerComplaintsOrderRefund',
                      'reservationStoreMap' => 'reservationStoreMap',
                      'batchInfo' => 'cashierSummary',                    
                      'unicommerceOrderTracker' => 'unicommerceOrderTracker',                   
                      'unicommerceCancelOrder' => 'unicommerceCancelOrder',
                      'productBundles' => 'productBundles',
                      'productBundleMap' => 'productBundleMap',
                      'bundleGroups' => 'bundleGroups',
                      'stockLedgerSummary' => 'stockLedgerSummary',
                      'stockLedgerDetail' => 'stockLedgerDetail',    
                      'salesAssessmentPeriods' => 'salesAssessmentPeriods',
                      'salesTargetConfigurations' => 'salesTargetConfigurations',
                      'salesTargets' => 'salesTargets',
                      'sentWhatsappMessagesHistory' =>  'sentWhatsappMessagesHistory',
                      'customPriceListSummary' => 'customPriceListSummary',
                      'customPriceListSlots' => 'customPriceListSlots',
                      'customRegionPriceListMapping' => 'customRegionPriceListMapping',
                      'customPriceListDetail' => 'customPriceListDetail',
                      'customPriceListSlabDetail' => 'customPriceListSlabDetail',
                      'priceListFilterGroups' => 'priceListFilterGroups',
                      'priceListFilters' => 'priceListFilters',
                      'channels' => 'channels',
                      'reservationChannelPricing' => 'reservationChannelPricing',
                      'fileTracker' => 'fileTracker',
                      'purchaseOrderReturnSummary' => 'purchaseOrderReturnSummary',
                      'purchaseOrderReturnDetail' => 'purchaseOrderReturnDetail',
                      'easyEcomOrderTracker' => 'easyEcomOrderTracker',
                      'easyEcomCancelOrder' => 'easyEcomCancelOrder',
                      'vendorBillSummary' => 'vendorBillSummary',
                      'vendorBillDetail' => 'vendorBillDetail',
                      'vendorBillPayments' => 'vendorBillPayments',
                      'vendorBillAccounting' => 'vendorBillAccounting',
                      'packageSummary' => 'packageSummary',
                      'packageDetail' => 'packageDetail',
                      'shipments' => 'shipments',
                      'vendorCreditPaymentSummary' => 'vendorCreditPaymentSummary',
                      'vendorCreditPaymentDetail' => 'vendorCreditPaymentDetail',
                      'shopifyOrderTracker'=>'shopifyOrderTracker',
                      'shopifyCancelOrder'=>'shopifyCancelOrder',
                      'shopifyStoreSettings'=>'shopifyStoreSettings',
                      'unicommerceOrderReturnTracker'=>'unicommerceOrderReturnTracker',
                      'vehicleTracker' => 'vehicleTracker',
                      'easyEcomOrderReturnTracker' => 'easyEcomOrderReturnTracker',
                      'invoiceSummary' => 'invoiceSummary',
                      'invoiceDetail' => 'invoiceDetail',
                      'invoicePayments' => 'invoicePayments',
                      'invoiceModifiers' => 'invoiceModifiers',
                      'invoiceComboProducts' => 'invoiceComboProducts',
                      'reportTracker' => 'reportTracker',
                      'catalogue' => 'catalogue',
                      'prodVariantCatalogue' => 'prodVariantCatalogue',
                      'customerPIMapping' => 'customerPIMapping',
                      'deviceLogs' => 'deviceLogs',
                      'syncSummary' => 'syncSummary',
                      'syncDetail' => 'syncDetail',
                      'vendorPaymentIntent' => 'vendorPaymentIntent',
                      'pendingCreditSettlements' => 'pendingCreditSettlements',
                      'orderServicePricingSlabs' => 'orderServicePricingSlabs',
                      'packageTypes' => 'packageTypes',
                      'channelStoreMap' => 'channelStoreMap',
                      'stateList'=>'stateList',
                      'channelPaymentMap' => 'channelPaymentMap',
                      'attributeMapping' => 'attributeMapping',
                      'userSettings' => 'userSettings',
                      'transactionTenderDeclaration' => 'transactionTenderDeclaration',
                      "bankingSummary" => "bankingSummary",
                      "bankAccounting" => "bankAccounting",
                      "userSettings" => "userSettings",
                      'RRAInvoiceTracker' => 'RRAInvoiceTracker',
                      'departments' => 'departments',                      
                      'departmentUserConfig' => 'departmentUserConfig',
                      'departmentSalesOrderConfig' => 'departmentSalesOrderConfig',                                                                  
                      'customerGroups' => 'customerGroups',
                      'channelCustomerGroupMapping' => 'channelCustomerGroupMapping',
                      "department" => "department",
                      "channelSalesOrderConfiguration" => "channelSalesOrderConfiguration",
                      'salesOrderSummary' => 'salesOrderSummary',
                      'salesOrderDetail' => 'salesOrderDetail',
                      'salesOrderPayments' => 'salesOrderPayments',
                      'salesOrderFulfillmentSummary' => 'salesOrderFulfillmentSummary',
                      'salesOrderFulfillmentDetail' => 'salesOrderFulfillmentDetail',
                      'salesOrderFulfillmentTracker' => 'salesOrderFulfillmentTracker',
                      'userWallet' => 'userWallet',
                      "walletTypes" => "walletTypes",
                      "walletMapping" => "walletMapping",
                      "walletActivityTracker" => "walletActivityTracker",
                      "walletRequestTracker" => "walletRequestTracker",
                      "inventoryAssetConfig" => "inventoryAssetConfig",
                      "inventoryAssetTagMapping" => "inventoryAssetTagMapping",
                      "inventoryLogs" => "inventoryLogs",
                      "priceListFilterDates" => "priceListFilterDates",
                      'catalogue' => 'catalogue',
                      'prodVariantCatalogue' => 'prodVariantCatalogue',
                      "merchantDeviceList" => "merchantDeviceList",
                      "userDeviceMapping" => "userDeviceMapping",
                      "merchantDeviceTypes" => "merchantDeviceTypes",
                      'securityInvoiceSummary' => 'securityInvoiceSummary',
                      'channelDepartmentConfig' => 'channelDepartmentConfig',
                      "sourceTax" => "sourceTax",
                      "sourceTaxOutletMap" => "sourceTaxOutletMap",
                      'salesOrderActivityTracker' => 'salesOrderActivityTracker',
                      'returnOrderSummary' => 'returnOrderSummary',
                      'returnOrderDetail' => 'returnOrderDetail',
                      'securityInvoicePayments' => 'securityInvoicePayments',
                      'salesControlConfig' => 'salesControlConfig',
                      'salesControlTracker' => 'salesControlTracker',
                      "customerCreditMgmt" => "customerCreditMgmt",
                      'creditNoteRefundLimitConfig' => 'creditNoteRefundLimitConfig',
                      'creditNoteRedemptionLimitConfig' => 'creditNoteRedemptionLimitConfig',
                      'sourceTax' => 'sourceTax',
                      'generalLedger' => 'generalLedger',
                      'taxFiling' => 'taxFiling',
                      'walletActivityTracker' => 'walletActivityTracker',                      
                      'salesOrderComboProducts' => 'salesOrderComboProducts',
                      'salesOrderModifiers' => 'salesOrderModifiers',
                      'returnOrderSummary' => 'returnOrderSummary',
                      'returnOrderDetail' => 'returnOrderDetail',
                      'sourceTax' => 'sourceTax',
                      'bulkSalesOrderSummary' => 'bulkSalesOrderSummary',
                      'salesOrderRequestTracker' => 'salesOrderRequestTracker',
                      'accountTransferTracker' => 'accountTransferTracker',
                      'paymentConfig'=>'paymentConfig',
                      'storeERPConfig' => 'storeERPConfig',
                      'wonderlaRevenueSummaryLog' => 'wonderlaRevenueSummaryLog',
                      'wonderlaRevenueDetailLog' => 'wonderlaRevenueDetailLog',
                      "inventoryAssetTagMappingTracker" => "inventoryAssetTagMappingTracker",
                      'salesQuotationSummary' => 'salesQuotationSummary', 
                      'salesQuotationDetail' => 'salesQuotationDetail',
                      'salesQuotationPayments' => 'salesQuotationPayments',
                      'salesQuotationComboProducts' => 'salesQuotationComboProducts',
                      'salesQuotationModifiers' => 'salesQuotationModifiers',
                      'customerAdvanceCreditMgmt' => 'customerAdvanceCreditMgmt',
                      'returnSOSummary' => 'returnSOSummary',
                      'returnSODetail' => 'returnSODetail',
                      'returnSOPayments' => 'returnSOPayments',
                      'returnSOFulfillmentTracker' => 'returnSOFulfillmentTracker',
                      'wonderlaIntegrationTracker' => 'wonderlaIntegrationTracker',
                      'departmentERPConfig' => 'departmentERPConfig',
                      'salesOrderConversionTracker' => 'salesOrderConversionTracker',
                      'salesOrderConversionTrackerDetail' => 'salesOrderConversionTrackerDetail',
                      'transactionTenderDeclaration' => 'transactionTenderDeclaration',
                      "bankingSummary" => "bankingSummary",
                      "bankAccounting" => "bankAccounting",
                      "inventoryAssetTagMappingTracker" => "inventoryAssetTagMappingTracker",
                      "userCounterSession" => "userCounterSession",
                      'discountFilterMap' => 'discountFilterMap',
                      'discountChannelMap' => 'discountChannelMap',
                      'discountCustomerGroupMap' => 'discountCustomerGroupMap',
                      'changeLogs' => 'changeLogs'
                      );
  private static $wareHouseTablesArray = array('stockDetail' => 'stockDetail',
                      'stockJournal' => 'stockJournal',
                      'stockSummary' => 'stockSummary',
                      'periods' => 'periods',
                      'periodStockSummary' => 'periodStockSummary',
                      'warehouses' => 'warehouses',
                      'batchCatalogue' => 'batchCatalogue'
                      );

  private static $OAuthTablesArray = array('clients' => 'oauth_clients',
                    'accessToken' => 'oauth_access_tokens');

  private static $GlobalTablesArray = array('users' => 'users',
                        'userStoreMap' => 'userStoreMap',
                        'globalEtislatInfo' => 'globalEtislatInfo',
                        'devicePaymentConfiguration' => 'devicePaymentConfiguration',
                        'globalTransactionIdempotencyKeys' => 'globalTransactionIdempotencyKeys',
                        'globalMobiQuestAPICallLogs' => 'globalMobiQuestAPICallLogs',
                        'userWarehouseMap' => 'userWarehouseMap',
                        'gcmUsers' => 'gcmUsers',
                        'gcmUsersForReportApp' => 'gcmUsersForReportApp',
                        'remarks' => 'globalRemarks',
                        'cityList' => 'cityList',
                        'countryList' => 'countryList',
                        'listOfStates' => 'listOfStates',
                        'eoi' => 'eoi',
                        'thirdPartyClients' => 'thirdPartyClients',
                        'thirdPartyTokens' => 'thirdPartyTokens',
                        'thirdPartyIntegrations' => 'thirdPartyIntegrations',
                        'listOfThirdPartyChains' => 'listOfThirdPartyChains',
                        'thirdPartyChainConf' => 'thirdPartyChainConf',
                        'thirdPartyRegions' => 'thirdPartyRegions',
                        'thirdPartyRegionDeviceMap' => 'thirdPartyRegionDeviceMap',
                        'thirdPartyChainMap' => 'thirdPartyChainMap',
                        'thirdPartyChainCategory' => 'thirdPartyChainCategory',
                        'thirdPartyChainStoreLocation' => 'thirdPartyChainStoreLocation',
                        'thirdPartyChainStoreMap' => 'thirdPartyChainStoreMap',
                        'userThirdPartyChainMap' => 'userThirdPartyChainMap',
                        'globalThirdPartyPermissions' => 'globalThirdPartyPermissions',
                        'globalThirdPartyRoles' => 'globalThirdPartyRoles',
                        'globalThirdPartyRolePermissionsMapping' => 'globalThirdPartyRolePermissionsMapping',
                        'globalThirdPartyUsersPermissionsMapping' => 'globalThirdPartyUsersPermissionsMapping',
                        'globalResetInvoice' => 'globalResetInvoice',
                        'signupTracker' => 'signupTracker',
                        'qbAppVersions' => 'qbAppVersions',
                        'globalQBSettings' => 'globalQBSettings',
                        'globalSettings' => 'globalSettings',
                        'globalRoles' => 'globalRoles',
                        'globalRolePermissionsMapping' => 'globalRolePermissionsMapping',
                        'globalChainInsights' => 'globalChainInsights',
                        'globalAppRequest' => 'globalAppRequest',
                        'globalCustomers' => 'globalCustomers',
                        'globalCustomerKhataTransactions' => 'globalCustomerKhataTransactions',
                        'globalCustomerOrderSummary' => 'globalCustomerOrderSummary',
                        'globalCustomerOrderPayments' => 'globalCustomerOrderPayments',
                        'globalCustomersLoyaltyInfo' => 'globalCustomersLoyaltyInfo',
                        'globalDeviceMgmt' => 'globalDeviceMgmt',
                        'globalDeviceMgmtNew' => 'globalDeviceMgmt',
                        'globalLicensePayment' => 'globalLicensePayment',
                        'globalLicenseImport' => 'globalLicenseImport',
                        'globalLicenseTransactionSummary' => 'globalLicenseTransactionSummary',
                        'globalLicenseTransactionDetail' => 'globalLicenseTransactionDetail',
                        'globalLicenseExtensionSummary' => 'globalLicenseExtensionSummary',
                        'globalLicenseExtensionDetail' => 'globalLicenseExtensionDetail',
                        'globalLicenseUpdationSummary' => 'globalLicenseUpdationSummary',
                        'globalPartnerAccessClients' => 'globalPartnerAccessClients',
                        'globalPartnerAccessTokens' => 'globalPartnerAccessTokens',
                        'globalPartnerRolePermissionsMapping' => 'globalPartnerRolePermissionsMapping',
                        'globalLicenseRates' => 'globalLicenseRates',
                        'globalDeviceFingerprint' => 'globalDeviceFingerprint',
                        'globalAPITracker' => 'globalAPITracker',
                        'globalErrorTracker' => 'globalErrorTracker',
                        'globalErrorEvents' => 'globalErrorEvents',
                        'globalUsersFeatureDemo' => 'globalUsersFeatureDemo',
                        'globalChainBillingAddress' => 'globalChainBillingAddress',
                        'globalZonePricing' => 'globalZonePricing',
                        'globalPartnerActivityList' => 'globalPartnerActivityList',
                        'globalPartnerActivityLogs' => 'globalPartnerActivityLogs',
                        'globalPartnerAppointments' => 'globalPartnerAppointments',
                        'globalPartnerCallLogs' => 'globalPartnerCallLogs',
                        'globalPartnerDeviceBatchesInfo' => 'globalPartnerDeviceBatchesInfo',
                        'globalPartnerDevicesInfo' => 'globalPartnerDevicesInfo',
                        'globalPartnerLeads' => 'globalPartnerLeads',
                        'globalPartnerRoles' => 'globalPartnerRoles',
                        'globalPartnerTasks' => 'globalPartnerTasks',
                        'globalPartnerNotes' => 'globalPartnerNotes',
                        'globalPartnerPermissions' => 'globalPartnerPermissions',
                        'globalPartnerUsersPermissionsMapping' => 'globalPartnerUsersPermissionsMapping',
                        'globalPartnerLicenseAccounting' => 'globalPartnerLicenseAccounting',
                        'globalPartnerLicenseClaims' => 'globalPartnerLicenseClaims',
                        'globalPartnerDeviceBatchTransferSummary' => 'globalPartnerDeviceBatchTransferSummary',
                        'globalPartnerDeviceBatchTransferDetail' => 'globalPartnerDeviceBatchTransferDetail',
                        'globalPartnerDeviceOwnership' => 'globalPartnerDeviceOwnership',
                        'globalResellerLicensePayment' => 'globalResellerLicensePayment',
                        'resellerDevicesInfo' => 'resellerDevicesInfo',
                        'globalSentMessagesHistory' => 'globalSentMessagesHistory',
                        'globalMerchantReportCategory' => 'globalMerchantReportCategory',
                        'globalMerchantReportList' => 'globalMerchantReportList',
                        'globalAutoMailers' => 'globalAutoMailers',
                        'globalAutoMailerLogs' => 'globalAutoMailerLogs',
                        'globalStockAlerts' => 'globalStockAlerts',
                        'globalStockAlertProducts' => 'globalStockAlertProducts',
                        'globalIndustryList' => 'globalIndustryList',
                        'globalIndustrySubTypeList' => 'globalIndustrySubTypeList',
                        'globalCreditPackages' => 'globalCreditPackages',
                        'globalChainCreditPackages' => 'globalChainCreditPackages',
                        'chatRooms' => 'chatRooms',
                        'errorOrderTracker' => 'errorOrderTracker',
                        'chatRoomUserMap' => 'chatRoomUserMap',
                        'messages' => 'messages',
                        'campaignSettings' => 'campaignSettings',
                        'measurementUnits' => 'globalMeasurementUnits',
                        'masterProducts' => 'masterProducts',
                        'masterProductPrices' => 'masterProductPrices',
                        'masterCategory' => 'masterCategory',
                        'masterBrands' => 'masterBrands',                        
                        'unitConversions' => 'globalUnitConversions',
                        'globalAppSettings' => 'globalAppSettings',
                        'globalType' => 'globalType',
                        'globalOrderCardPayment' => 'globalOrderCardPayments',
                        'globalTypeValues' => 'globalTypeValues',
                        'globalOrderSummary' => 'globalOrderSummary',
                        'globalOrderPaymentsSummary' => 'globalOrderPaymentsSummary',
                        'globalPaymentAccountMapping' => 'globalTypePaymentAccountIDMapping',
                        'globalAccountGroups' => 'globalAccountGroups',
                        'globalAccountSubGroups' => 'globalAccountSubGroups',
                        'listOfStores' => 'listOfStores',
                        'listOfResellers' => 'listOfResellers',
                        'listOfWarehouses' => 'listOfWarehouses',
                        'listOfChains' => 'listOfChains',
                        'chainModuleMap' => 'chainModuleMap',
                        'permissions' => 'permissions',
                        'permissionGroups' => 'permissionGroups',
                        'permissionGroupsPermissionsMap' => 'permissionGroupsPermissionsMap',
                        'rolePermissionsMapping' => 'rolePermissionsMapping',
                        'rolesPermissionGroupsMap' => 'rolesPermissionGroupsMap',
                        'roles' => 'roles',
                        'rolesHierarchy' => 'rolesHierarchy',
                        'notifications' => 'notifications',
                        'orderStatus' => 'orderStatus',
                        'devicesInfo' => 'devicesInfo',
                        'plutusDevicesInfo' => 'plutusDevicesInfo',
                        'globalDevicesInfo' => 'globalDevicesInfo',
                        'printerInfo' => 'printerInfo',
                        'installedDevicesInfo' => 'installedDevicesInfo',
                        'baseApk' => 'baseApkVersion',
                        'globalMarketPlace' => 'globalMarketPlaceApps',
                        'globalRestaurantAppVersions' => 'globalRestaurantAppVersions',
                        'appDependency' => 'appDependency',
                        'globalMarketPlaceApps' => 'globalMarketPlaceApps',
                        'globalAppVersion' => 'globalAppVersion',
                        'globalAppVersionOld' => 'globalAppVersionOld',
                        'globalStoreLastSync' => 'globalStoreLastSync',
                        'globalPulseSync' => 'globalDevicePulse',
                        'thirdPartyIDForRestaurant' => 'thirdPartyIDForRestaurant',
                        'globalAppInstallations' => 'globalAppInstallations',
                        'globalSystemUpdates' => 'globalSystemUpdates',
                        'globalGeneralSettings' => 'globalGeneralSettings',
                        'usersPermissionsMapping' => 'usersPermissionsMapping',
                        'globalChainMessages' => 'globalChainMessages',
                        'globalChainMessageCredits' => 'globalChainMessageCredits',
                        'crmSettings' => 'crmSettings',
                        'currencyMaster' => 'currencyMaster',
                        'chainComplianceInfo' => 'chainComplianceInfo',
                        'globalChainSMSRates' => 'globalChainSMSRates',
                        'globalSMSCreditTransactions' => 'globalSMSCreditTransactions',
                        'globalSMSTemplates' => 'globalSMSTemplates',
                        'globalPrintReceiptSettings' => 'globalPrintReceiptSettings',
                        'globalPartnerResellersList' => 'globalPartnerResellersList',
                        'pladaTokens' => 'pladaTokens',
                        'pladaLogs' => 'pladaLogs',
                        'treatmentTypeGroups' => 'treatmentTypeGroups',
                        'treatmentTypes' => 'treatmentTypes',
                        'globalAirlinesList' => 'globalAirlinesList',
                        'globalAirportList' => 'globalAirportList',
                        'globalAirlineRoutes' => 'globalAirlineRoutes',
                        'globalAirlineRouteSectors' => 'globalAirlineRouteSectors',
                        'globalAircraftsList' => 'globalAircraftsList',
                        'inflightCurrencyOrder' => 'inflightCurrencyOrder',
                        'urbanpiperStoreSettings' => 'urbanpiperStoreSettings',
                        'urbanpiperAPIHistory' => 'urbanpiperAPIHistory',
                        'firstDataMCCList' => 'firstDataMCCList',
                        'globalOnlineOrderTransactions'=>'globalOnlineOrderTransactions',
                        'globalChainOnlineOrderCredits'=>'globalChainOnlineOrderCredits',
                        'globalChainLicenseRates'=>'globalChainLicenseRates',
                        'globalLicenseRateLogs' => 'globalLicenseRateLogs',
                        'userFingerprintMap' => 'userFingerprintMap',
                        'globalChainOnlineOrderRates' => 'globalChainOnlineOrderRates',
                        'chainLicenseSupportTracker' => 'chainLicenseSupportTracker',
                        'globalEcomStoreSettings' => 'globalEcomStoreSettings',
                        'globalChainEcomOrderCredits' => 'globalChainEcomOrderCredits',
                        'globalChainEcomOrderRates' => 'globalChainEcomOrderRates',
                        'globalEcomOrderSummary' => 'globalEcomOrderSummary',
                        'globalEcomOrderDetail' => 'globalEcomOrderDetail',
                        'globalEcomInvoiceSummary' => 'globalEcomInvoiceSummary',
                        'globalEcomInvoiceDetail' => 'globalEcomInvoiceDetail',
                        'globalEcomInvoicePayment' => 'globalEcomInvoicePayment',
                        'globalEcomCustomers' => 'globalEcomCustomers',
                        'globalEcomCustomerDeviceMapping' => 'globalEcomCustomerDeviceMapping',
                        'globalEcomCustomerAccessTokens' => 'globalEcomCustomerAccessTokens',
                        'globalEcomCustomersAddresses' => 'globalEcomCustomersAddresses',
                        'globalEcomPaymentIntent' => 'globalEcomPaymentIntent',
                        'globalEcomPaymentRefunds' => 'globalEcomPaymentRefunds',
                        'globalEcomOrderPayments' => 'globalEcomOrderPayments',
                        'globalEcomPinelabsPaymentHash' => 'globalEcomPinelabsPaymentHash',
                        'thirdPartyChainCategory' => 'thirdPartyChainCategory',
                        'thirdPartyChainIndustries' => 'thirdPartyChainIndustries',
                        'globalChainInquiry' => 'globalChainInquiry',
                        'globalEcomCustomerReservationSummary' => 'globalEcomCustomerReservationSummary',
                        'globalLicensePromotions' => 'globalLicensePromotions',
                        'globalChainSalesTracker' => 'globalChainSalesTracker',
                        'globalChainPaymentsTracker' => 'globalChainPaymentsTracker',
                        'globalQBFeaturePackages' => 'globalQBFeaturePackages',
                        'masterCategoryElectronic' => 'masterCategoryElectronic',
                        'masterSubCategoryElectronic' => 'masterSubCategoryElectronic',
                        'masterProductsElectronic'  => 'masterProductsElectronic',
                        'masterProductPricesElectronic'  => 'masterProductPricesElectronic',
                        'masterBrandsElectronic' => 'masterBrandsElectronic',
                        'listOfIndianSocieties' => 'listOfIndianSocieties',
                        'globalEcomCustomerLocalityMapping'=>'globalEcomCustomerLocalityMapping',
                        'restrictedQBHandles' =>  'restrictedQBHandles',
                        'globalThirdPartyDeviceSessions' => 'globalThirdPartyDeviceSessions',
                        'globalThirdPartyDeviceSessionInsights' => 'globalThirdPartyDeviceSessionInsights',
                        'globalThirdPartyDeviceSessionLogs' => 'globalThirdPartyDeviceSessionLogs',
                        'mysquareCustomerOrderSummary' => 'mysquareCustomerOrderSummary', 
                        'mysquareCustomerOrderDetail' => 'mysquareCustomerOrderDetail',
                        'mysquareCustomerOrderPayments' => 'mysquareCustomerOrderPayments',
                        'mysquareCardActivityTracker' => 'mysquareCardActivityTracker',
                        'mysquareCustomers' => 'mysquareCustomers',
                        'globalEcomPayuPaymentHash' => 'globalEcomPayuPaymentHash',
                        'globalThirdPartyDiscountVouchers' => 'globalThirdPartyDiscountVouchers',
                        'globalThirdPartyDiscountVoucherStoreMap' => 'globalThirdPartyDiscountVoucherStoreMap',
                        'globalThirdPartyAPIPackages' => 'globalThirdPartyAPIPackages',
                        'globalThirdPartyAPIUsageTracker' => 'globalThirdPartyAPIUsageTracker',
                        'globalThirdPartyAPICallLogs' => 'globalThirdPartyAPICallLogs','globalEcomChainPolicyTemplates' => 'globalEcomChainPolicyTemplates','globalBillMeAPICallLogs'=>'globalBillMeAPICallLogs',
                        'globalEcomThirdPartyChainPolicyTemplates' => 'globalEcomThirdPartyChainPolicyTemplates',
                        'globalPolicyTemplates' => 'globalPolicyTemplates',
                        'masterCategoryGrocery' => 'masterCategoryGrocery',
                        'masterSubCategoryGrocery' => 'masterSubCategoryGrocery',
                        'masterBrandsGrocery' => 'masterBrandsGrocery',
                        'masterProductsGrocery' => 'masterProductsGrocery',
                        'masterProductPricesGrocery' => 'masterProductPricesGrocery',
                        'masterSubCategory' => 'masterSubCategory',
                        'groceryCatalogueDump' => 'groceryCatalogueDump',
                        'shopifyUnsyncedOrderLogs' => 'shopifyUnsyncedOrderLogs',
                        'bijlipayPaymentIntent' => 'bijlipayPaymentIntent',
                        'globalQBWebhooks' => 'globalQBWebhooks',
                        'globalChainWebhookMapping' => 'globalChainWebhookMapping',
                        'globalQBSalesInvoiceTracker' => 'globalQBSalesInvoiceTracker',
                        'globalUrbanpiperOrderLogs' => 'globalUrbanpiperOrderLogs',
                        'globalMunicipalLogs' => 'globalMunicipalLogs',
                        'countries' => 'countries',
                        'states' => 'states',
                        'cities' => 'cities',
                        'globalResellerCommissionPayout' => 'globalResellerCommissionPayout',
                        'globalResellerLeadLogs' => 'globalResellerLeadLogs',
                        'globalResellerCommissionSchemes' => 'globalResellerCommissionSchemes',
                        'globalResellerCommissionSlabs' => 'globalResellerCommissionSlabs',
                        'globalChainWhatsappRates' => 'globalChainWhatsappRates',
                        'globalChainWhatsappCredits'  =>  'globalChainWhatsappCredits',
                        'QBDailyDigest' => 'QBDailyDigest',
                        'QBDailyDigestTransactionData' => 'QBDailyDigestTransactionData',
                        'zomatoStoreSettings' => 'zomatoStoreSettings',
                        'zomatoOrderRatingTracker' => 'zomatoOrderRatingTracker',
                        'zomatoAPIHistory' => 'zomatoAPIHistory',
                        'zomatoRemarks'=>'zomatoRemarks',
                        'merchantDeviceInfo'=>'merchantDeviceInfo',
                        'kdsInfo' => 'kdsInfo',
                        'failed_jobs' => 'failed_jobs',
                        'globalUnicommerceAPIUsageTracker'=>'globalUnicommerceAPIUsageTracker',
                        'globalUnicommerceAPICallLogs'=>'globalUnicommerceAPICallLogs',
                        'demoRequests' => 'demoRequests',
                        'websites' => 'websites',
                        'teams' => 'teams',
                        'updateSchemaRoutes' => 'updateSchemaRoutes',
                        'updateSchemaTracker' => 'updateSchemaTracker',
                        'payphiPaymentIntent' => 'payphiPaymentIntent',
                        'updateSchemaHistory' => 'updateSchemaHistory',
                        'chainSettings' =>  'chainSettings',
                        'engageRegistrations' =>  'engageRegistrations',
                        'engageNominations' =>  'engageNominations',
                        'engageInvestorRounds' =>  'engageInvestorRounds',
                        'engageExhibitors' =>  'engageExhibitors',
                        'globalEasyEcomAPIUsageTracker'=>'globalEasyEcomAPIUsageTracker',
                        'globalEasyEcomAPICallLogs'=>'globalEasyEcomAPICallLogs',
                        'HSNCodeListIndia' => 'HSNCodeListIndia',
                        'HSNCodeTaxSlabMapping' => 'HSNCodeTaxSlabMapping',
                        'globalEasyRewardzAPICallLogs' => 'globalEasyRewardzAPICallLogs',
                        'careers' => 'careers',
                        'globalShopifyAPIUsageTracker' =>'globalShopifyAPIUsageTracker',
                        'globalShopifyAPICallLogs' =>'globalShopifyAPICallLogs',
                        'chainWhatsappTemplate' =>'chainWhatsappTemplate',
                        'thirdPartyChainLogs' => 'thirdPartyChainLogs',
                        'storePrintReceiptSettings' => 'storePrintReceiptSettings',
                        'utapPaymentIntent' => 'utapPaymentIntent',
                        'weraStoreSettings' => 'weraStoreSettings',
                        'weraAPIHistory' => 'weraAPIHistory',
                        'globalWeraOrderLogs' => 'globalWeraOrderLogs',
                        'codeClassification' => 'codeClassification',
                        'unitQuantity' => 'unitQuantity',
                        'packagingUnit' => 'packagingUnit',
                        'distributorFranchiseMap' => 'distributorFranchiseMap',
                        'franchiseStoreMap' => 'franchiseStoreMap',
                        'internationalisation' => 'internationalisation',
                        'userAuthentication'=> 'userAuthentication',
                        'jarvisAPICallLogs' => 'jarvisAPICallLogs',
                        'globalLicenseSuspensionRequests' => 'globalLicenseSuspensionRequests',
                        'globalWhatsappCreditTransactions' => 'globalWhatsappCreditTransactions',
                        'clearDataRequests' => 'clearDataRequests',
                        'snapmintPaymentIntent' => 'snapmintPaymentIntent',
                        'fiscalYearPrefixes' => 'globalFiscalYearPrefixes',
                        'globalBillMeAPICallLogs' => 'globalBillMeAPICallLogs',
                        'globalSchemaTracker' => 'globalSchemaTracker',
                        'globalInvoiceIdempotencyKeys' => 'globalInvoiceIdempotencyKeys'
                    );

 private static $expenseTablesArray = array('expenseCategory' => 'expenseCategory',
                    'expenseSubCategory' => 'expenseSubCategory',
                    'categoryStoreMap' => 'categoryStoreMap'
                    );

 private static $GlobalMacroArray = array('ACC_SALES' => 'ACC_SALES',
                      'ACC_ROUNDING_OFF' => 'ACC_ROUNDING_OFF',
                      'ACC_TAX' => 'ACC_TAX',
                      'ACC_ADD_CHARGE' => 'ACC_ADD_CHARGE',
                      'ACC_CASH_RECD' => 'ACC_CASH_RECD',
                      'ACC_CARD_RECD' => 'ACC_CARD_RECD',
                      'ACC_OTHER_RECD' => 'ACC_OTHER_RECD',
                      'ORST_DELIVERED' => 'ORST_DELIVERED',
                      'ORST_CANCELLED' => 'ORST_CANCELLED',
                      'ORST_IN_PROGRESS' => 'ORST_IN_PROGRESS',
                      'ORST_IN_TRANSIT' => 'ORST_IN_TRANSIT',
                      'TRN_ORDER' => 'TRN_ORDER',
                      'TRN_ORDER_CANCEL' => 'TRN_ORDER_CANCEL',
                      'TRN_CASH_IMPREST' => 'TRN_CASH_IMPREST',
                      'TRN_CASH_DRAWN' => 'TRN_CASH_DRAWN',
                      'DEL_DINE_IN' => 'DEL_DINE_IN',
                      'DEL_HOME_DELIVERY' => 'DEL_HOME_DELIVERY',
                      'DEL_TAKE_AWAY' => 'DEL_TAKE_AWAY',
                      'DEL_FROM_TABLE' => 'DEL_FROM_TABLE',
                      'DEL_CATERING' => 'DEL_CATERING',
                      'DEL_OTHER' => 'DEL_OTHER',
                      'ACC_VOUCHER_RECD' => 'ACC_VOUCHER_RECD',
                      'ACC_MEAL_COUPON_RECD' => 'ACC_MEAL_COUPON_RECD',
                      'ACC_CHEQUE_RECD' => 'ACC_CHEQUE_RECD',
                      'ACC_TIP' => 'ACC_TIP',
                      'PAYMENT_CASH' => 'PAYMENT_CASH',
                      'PAYMENT_CARD' => 'PAYMENT_CARD',
                      'PAYMENT_VOUCHER' => 'PAYMENT_VOUCHER',
                      'HARD_LIMIT' => 200
                    );
 private static $SchemaModelArray = array('POST_ORDER' => 'orderSchema.json',
                      'POST_DEVICES' => 'createDevices.json',
                      'POST_ORDER_CUSTOMERS' => 'customersSchema.json',
                      'POST_ORDER_DISCOUNTS' => 'discountSchema.json',
                      'POST_ORDER_TAX' => 'taxSchema.json',
                      'POST_ORDER_PAYMENTS' => 'paymentSchema.json',
                      'POST_ORDER_PRODUCTSLIST' => 'productListSchema.json',
                      'POST_ORDER_MODIFIERS' => 'modifiersSchema.json',
                      'POST_ORDER_ADDITIONAL_CHARGES' => 'additionalChargesSchema.json',
                      'PUT_ORDER' => 'updateOrder.json',
                      'POST_TRANSACTION' => 'transactionSchema.json',
                      'POST_DEVICES' => 'createDevices.json',
                      'POST_BATCH' => 'batchPost.json',
                      'POST_DAY_OPEN' => 'postDayOpen.json',
                      'POST_DAY_CLOSE' => 'postDayClose.json',
                      'POST_DAY_REVERT' => 'postDayRevert.json',
                      'APP_INSTALL' => 'appInstall.json',
                      'APP_UNINSTALL' => 'appUninstall.json',
                      'POST_STOCK_IN' => 'stockInSchema.json',
                      'POST_STOCK_IN_RAW_PRODUCTS' => 'rawProducts.json',
                      'POST_ORDER_VOID_DETAILS' => 'voidOrder.json',
                      'OYO_POST_ORDER' => 'oyoPostOrderSchema.json',
                      'PUT_PRINTER' => 'printerProductMap.json',
                      'TEST_SCHEMA' => 'testSchema.json',
                      'ADD_PRODUCTS' => 'productsAdd.json',
                      'POST_STOCK_TRANSACTION' =>'stockTransaction.json',
                      'POST_STOCK_BATCH_TRANSACTION' =>'stockBatchTransaction.json',
                      'SIGN_UP' => 'signUp.json',
                      'IMPORT_PRODUCTS' => 'importGlobalProds.json',
                      'ADD_MODIFIERS_AS_PROD' => 'importGlobalProds.json',
                      'POST_PACKAGE' => 'packageSchema.json',
                      'POST_PACKAGE_PRODUCTSLIST' => 'packageListSchema.json',
                      "POST_PRICELIST_DETAIL_LIST" => "priceListSchema.json"
                    );

  private static $currencySymbols = array(
                                            'AED' => '&#1583;.&#1573;', // ?
                                            'AFN' => '&#65;&#102;',
                                            'ALL' => '&#76;&#101;&#107;',
                                            'AMD' => '',
                                            'ANG' => '&#402;',
                                            'AOA' => '&#75;&#122;', // ?
                                            'ARS' => '&#36;',
                                            'AUD' => '&#36;',
                                            'AWG' => '&#402;',
                                            'AZN' => '&#1084;&#1072;&#1085;',
                                            'BAM' => '&#75;&#77;',
                                            'BBD' => '&#36;',
                                            'BDT' => '&#2547;', // ?
                                            'BGN' => '&#1083;&#1074;',
                                            'BHD' => '.&#1583;.&#1576;', // ?
                                            'BIF' => '&#70;&#66;&#117;', // ?
                                            'BMD' => '&#36;',
                                            'BND' => '&#36;',
                                            'BOB' => '&#36;&#98;',
                                            'BRL' => '&#82;&#36;',
                                            'BSD' => '&#36;',
                                            'BTN' => '&#78;&#117;&#46;', // ?
                                            'BWP' => '&#80;',
                                            'BYR' => '&#112;&#46;',
                                            'BZD' => '&#66;&#90;&#36;',
                                            'CAD' => '&#36;',
                                            'CDF' => '&#70;&#67;',
                                            'CHF' => '&#67;&#72;&#70;',
                                            'CLF' => '', // ?
                                            'CLP' => '&#36;',
                                            'CNY' => '&#165;',
                                            'COP' => '&#36;',
                                            'CRC' => '&#8353;',
                                            'CUP' => '&#8396;',
                                            'CVE' => '&#36;', // ?
                                            'CZK' => '&#75;&#269;',
                                            'DJF' => '&#70;&#100;&#106;', // ?
                                            'DKK' => '&#107;&#114;',
                                            'DOP' => '&#82;&#68;&#36;',
                                            'DZD' => '&#1583;&#1580;', // ?
                                            'EGP' => '&#163;',
                                            'ETB' => '&#66;&#114;',
                                            'EUR' => '&#8364;',
                                            'FJD' => '&#36;',
                                            'FKP' => '&#163;',
                                            'GBP' => '&#163;',
                                            'GEL' => '&#4314;', // ?
                                            'GHS' => '&#162;',
                                            'GIP' => '&#163;',
                                            'GMD' => '&#68;', // ?
                                            'GNF' => '&#70;&#71;', // ?
                                            'GTQ' => '&#81;',
                                            'GYD' => '&#36;',
                                            'HKD' => '&#36;',
                                            'HNL' => '&#76;',
                                            'HRK' => '&#107;&#110;',
                                            'HTG' => '&#71;', // ?
                                            'HUF' => '&#70;&#116;',
                                            'IDR' => '&#82;&#112;',
                                            'ILS' => '&#8362;',
                                            'INR' => '&#8377;',
                                            'IQD' => '&#1593;.&#1583;', // ?
                                            'IRR' => '&#65020;',
                                            'ISK' => '&#107;&#114;',
                                            'JEP' => '&#163;',
                                            'JMD' => '&#74;&#36;',
                                            'JOD' => '&#74;&#68;', // ?
                                            'JPY' => '&#165;',
                                            'KES' => '&#75;&#83;&#104;', // ?
                                            'KGS' => '&#1083;&#1074;',
                                            'KHR' => '&#6107;',
                                            'KMF' => '&#67;&#70;', // ?
                                            'KPW' => '&#8361;',
                                            'KRW' => '&#8361;',
                                            'KWD' => '&#1583;.&#1603;', // ?
                                            'KYD' => '&#36;',
                                            'KZT' => '&#1083;&#1074;',
                                            'LAK' => '&#8365;',
                                            'LBP' => '&#163;',
                                            'LKR' => '&#8360;',
                                            'LRD' => '&#36;',
                                            'LSL' => '&#76;', // ?
                                            'LTL' => '&#76;&#116;',
                                            'LVL' => '&#76;&#115;',
                                            'LYD' => '&#1604;.&#1583;', // ?
                                            'MAD' => '&#1583;.&#1605;.', //?
                                            'MDL' => '&#76;',
                                            'MGA' => '&#65;&#114;', // ?
                                            'MKD' => '&#1076;&#1077;&#1085;',
                                            'MMK' => '&#75;',
                                            'MNT' => '&#8366;',
                                            'MOP' => '&#77;&#79;&#80;&#36;', // ?
                                            'MRO' => '&#85;&#77;', // ?
                                            'MUR' => '&#8360;', // ?
                                            'MVR' => '.&#1923;', // ?
                                            'MWK' => '&#77;&#75;',
                                            'MXN' => '&#36;',
                                            'MYR' => '&#82;&#77;',
                                            'MZN' => '&#77;&#84;',
                                            'NAD' => '&#36;',
                                            'NGN' => '&#8358;',
                                            'NIO' => '&#67;&#36;',
                                            'NOK' => '&#107;&#114;',
                                            'NPR' => '&#8360;',
                                            'NZD' => '&#36;',
                                            'OMR' => '&#65020;',
                                            'PAB' => '&#66;&#47;&#46;',
                                            'PEN' => '&#83;&#47;&#46;',
                                            'PGK' => '&#75;', // ?
                                            'PHP' => '&#8369;',
                                            'PKR' => '&#8360;',
                                            'PLN' => '&#122;&#322;',
                                            'PYG' => '&#71;&#115;',
                                            'QAR' => '&#65020;',
                                            'RON' => '&#108;&#101;&#105;',
                                            'RSD' => '&#1044;&#1080;&#1085;&#46;',
                                            'RUB' => '&#1088;&#1091;&#1073;',
                                            'RWF' => '&#1585;.&#1587;',
                                            'SAR' => '&#65020;',
                                            'SBD' => '&#36;',
                                            'SCR' => '&#8360;',
                                            'SDG' => '&#163;', // ?
                                            'SEK' => '&#107;&#114;',
                                            'SGD' => '&#36;',
                                            'SHP' => '&#163;',
                                            'SLL' => '&#76;&#101;', // ?
                                            'SOS' => '&#83;',
                                            'SRD' => '&#36;',
                                            'STD' => '&#68;&#98;', // ?
                                            'SVC' => '&#36;',
                                            'SYP' => '&#163;',
                                            'SZL' => '&#76;', // ?
                                            'THB' => '&#3647;',
                                            'TJS' => '&#84;&#74;&#83;', // ? TJS (guess)
                                            'TMT' => '&#109;',
                                            'TND' => '&#1583;.&#1578;',
                                            'TOP' => '&#84;&#36;',
                                            'TRY' => '&#8356;', // New Turkey Lira (old symbol used)
                                            'TTD' => '&#36;',
                                            'TWD' => '&#78;&#84;&#36;',
                                            'TZS' => '',
                                            'UAH' => '&#8372;',
                                            'UGX' => '&#85;&#83;&#104;',
                                            'USD' => '&#36;',
                                            'UYU' => '&#36;&#85;',
                                            'UZS' => '&#1083;&#1074;',
                                            'VEF' => '&#66;&#115;',
                                            'VND' => '&#8363;',
                                            'VUV' => '&#86;&#84;',
                                            'WST' => '&#87;&#83;&#36;',
                                            'XAF' => '&#70;&#67;&#70;&#65;',
                                            'XCD' => '&#36;',
                                            'XDR' => '',
                                            'XOF' => '',
                                            'XPF' => '&#70;',
                                            'YER' => '&#65020;',
                                            'ZAR' => '&#82;',
                                            'ZMK' => '&#90;&#75;', // ?
                                            'ZWL' => '&#90;&#36;',
);

  private static $tokenArray = array('gcm' => 'AIzaSyDJWBh5dxAygnvdjzeyS0jfhYN5lvkhoJo',
                                     'payuMerchantID' => '5788086',
                                     'payuKey' => 'hdyrcHdi',
                                     'payuSalt' => 'UqAbXi4rn9',
                                     'payuHashSequence' => 'key|txnid|amount|productinfo|firstname|email|udf1|udf2|udf3|udf4|udf5|udf6|udf7|udf8|udf9|udf10',
                                     'payuBaseUrl' => 'https://secure.payu.in',
                                     'payuServiceProvider' => 'payu_paisa');

  private static $tpcTablesArray = array('cardActivityTracker' => 'cardActivityTracker',
                                         'customerOrderSummary' => 'customerOrderSummary',
                                         'customerOrderPayments' => 'customerOrderPayments',
                                         'globalType' => 'globalType',
                                         'globalTypeValues' => 'globalTypeValues',
                                         'orderAccounting' => 'orderAccounting',
                                         'accountIDs' => 'accountIDs',
                                         'customCardsInfo' => 'customCardsInfo',
                                         'cashierSummary' => 'cashierSummary',
                                         'daysInfo' => 'daysInfo',
                                         'customers' => 'customers',
                                         'roles' => 'roles',
                                         'permissions' => 'permissions',
                                         'rolePermissionsMapping' => 'rolePermissionsMapping',
                                         'usersPermissionsMapping' => 'usersPermissionsMapping',
                                         'requestTracker' => 'requestTracker');
  public static function getSchemaName($schemaName)
  {
    return $schemaName != NULL ? self::$SchemaModelArray[$schemaName] : NULL ;

  }

  public static function getChainTableArray(){
    return self::$chainTablesArray;
  }

  public static function getStoreTableArray(){
    return self::$merchantTablesArray;
  }

  public static function getGlobalTableArray(){
    return self::$GlobalTablesArray;
  }

  public static function getCurrency($index){
    return isset(self::$currencySymbols[$index]) ? html_entity_decode(self::$currencySymbols[$index]) :false;
  }
  public static function getToken($index = false) {
        return $index !== false ? (self::$tokenArray[$index]) : self::$tokenArray;
    }

  public static function getTableName($restID, $index = false) {
        $lowercaseDB = env("LOWERCASE_DB", false);
        if ($lowercaseDB) {
            return $index !== false ? strtolower("store".$restID."_".self::$merchantTablesArray[$index]) : self::$merchantTablesArray;
        }
        return $index !== false ? ("store".$restID."_".self::$merchantTablesArray[$index]) : self::$merchantTablesArray;
    }

  public static function getWareTableName($restID, $index = false) {
        $lowercaseDB = env("LOWERCASE_DB", false);
        if ($lowercaseDB) {
            return $index !== false ? strtolower("ware".$restID."_".self::$merchantTablesArray[$index]) : self::$merchantTablesArray;
        }
        return $index !== false ? ("ware".$restID."_".self::$merchantTablesArray[$index]) : self::$merchantTablesArray;
    }

  public static function getChainTableName($chainID, $index = false) {
        $lowercaseDB = env("LOWERCASE_DB", false);
        if ($lowercaseDB) {
            return $index !== false ? strtolower("chain".$chainID."_".self::$chainTablesArray[$index]) : self::$chainTablesArray;
        }
        return $index !== false ? ("chain".$chainID."_".self::$chainTablesArray[$index]) : self::$chainTablesArray;
    }

  public static function getOAuthTableName($index = false) {
        $lowercaseDB = env("LOWERCASE_DB", false);
        if ($lowercaseDB) {
            return $index !== false ? strtolower(self::$OAuthTablesArray[$index]) : self::$OAuthTablesArray;
        }
        return $index !== false ? self::$OAuthTablesArray[$index] : self::$OAuthTablesArray;
    }

  public static function getGlobalTableName($index = false) {
        $lowercaseDB = env("LOWERCASE_DB", false);
        if ($lowercaseDB) {
            return $index !== false ? strtolower(self::$GlobalTablesArray[$index]) : self::$GlobalTablesArray;
        }
        return $index !== false ? self::$GlobalTablesArray[$index] : self::$GlobalTablesArray;
    }

  public static function getChainInventoryTableName($chainID, $index = false) {
        $lowercaseDB = env("LOWERCASE_DB", false);
        if ($lowercaseDB) {
            return $index !== false ? strtolower("chain".$chainID."_IM_".self::$chainTablesArray[$index]) : self::$chainTablesArray;
        }
        return $index !== false ? ("chain".$chainID."_IM_".self::$chainTablesArray[$index]) : self::$chainTablesArray;
    }

  public static function getChainLevelProductsName($chainID,$index = false)
  {
      $lowercaseDB = env("LOWERCASE_DB", false);
      if ($lowercaseDB) {
          return $index !== false ? strtolower("chain".$chainID."_MM_".self::$chainTablesArray[$index]) : self::$chainTablesArray;
      }
      return $index !== false ? ("chain".$chainID."_MM_".self::$chainTablesArray[$index]) : self::$chainTablesArray;
  }

  public static function getWarehouseTableName($chainID,$warehouseId, $index = false) {
        $lowercaseDB = env("LOWERCASE_DB", false);
        if ($lowercaseDB) {
            return $index !== false ? strtolower("chain".$chainID."_IM_wh".$warehouseId."_".self::$wareHouseTablesArray[$index]) : self::$wareHouseTablesArray;
        }
        return $index !== false ? ("chain".$chainID."_IM_wh".$warehouseId."_".self::$wareHouseTablesArray[$index]) : self::$wareHouseTablesArray;
    }

  public static function getReportsBodyString() {
        return "This mailbox is not monitored. \n Please do not reply to this email.";
    }

    public static function getExpenseTableName($chainID, $index = false) {
        $lowercaseDB = env("LOWERCASE_DB", false);
        if ($lowercaseDB) {
            return $index !== false ? strtolower("chain".$chainID."_EM_".self::$expenseTablesArray[$index]) : self::$chainTablesArray;
        }
        return $index !== false ? ("chain".$chainID."_EM_".self::$expenseTablesArray[$index]) : self::$chainTablesArray;
    }

    public static function getTpcTableName($tpcID, $index = false) {
      $lowercaseDB = env("LOWERCASE_DB", false);
      if ($lowercaseDB) {
          return $index !== false ? strtolower("tpc".$tpcID."_".self::$tpcTablesArray[$index]) : self::$tpcTablesArray;
      }
      return $index !== false ? ("tpc".$tpcID."_".self::$tpcTablesArray[$index]) : self::$tpcTablesArray;
    }

    public static function getChainTimezone($chainID){
      $listOfChainsTable = self::getGlobalTableName('listOfChains');

      $getTimezone = convertToArray(DB::table($listOfChainsTable)->where('chainID', $chainID)->select('timezone')->get());

      if (empty($getTimezone)) {
        $timezone = NULL;
      }else{
        $timezone = $getTimezone[0]["timezone"];
      }

      return $timezone;

    }

    public static function getChainIndustry($chainID){
      $listOfChainsTable = self::getGlobalTableName('listOfChains');

      $getIndustry = convertToArray(DB::table($listOfChainsTable)->where('chainID', $chainID)->select('industry')->get());

      if (empty($getIndustry)) {
        $industry = NULL;
      }else{
        $industry = $getIndustry[0]["industry"];
      }

      return $industry;

    }

    public static function getChainCountry($chainID){
      $listOfChainsTable = self::getGlobalTableName('listOfChains');

      $getCountry = convertToArray(DB::table($listOfChainsTable)->where('chainID', $chainID)->select('regAddCountry')->get());

      if (empty($getCountry)) {
        $country = NULL;
      }else{
        $country = $getCountry[0]["regAddCountry"];
      }

      return $country;

    }

    public static function getChainCurrency($chainID){
      $listOfChainsTable = self::getGlobalTableName('listOfChains');

      $curr = convertToArray(DB::table($listOfChainsTable)->where('chainID', $chainID)->select('currencyCode')->get());

      if (empty($curr)) {
        $currencyCode = NULL;
      }else{
        $currencyCode = $curr[0]["currencyCode"];
      }

      return $currencyCode;

    }

    public static function getChainID($storeID){
      $listOfStoresTable = self::getGlobalTableName('listOfStores');

      $getChain = convertToArray(DB::table($listOfStoresTable)->where('storeID', $storeID)->select('chainID')->get());

      if (empty($getChain)) {
        $chainID = NULL;
      }else{
        $chainID = $getChain[0]["chainID"];
      }

      return $chainID;

    }

    public static function getChainSuperUser($chainID){
      $usersTable = Constants::getGlobalTableName("users");
      $userStoreMapTable = Constants::getGlobalTableName("userStoreMap");

      $getUserID = convertToArray(DB::table($userStoreMapTable)->where("chainID", $chainID)->get());

      if (empty($getUserID)) {
        return [];
      }
      $userID = $getUserID[0]["userID"];

      $getUserInfo = convertToArray(DB::table($usersTable)->where("ID",$userID)->get());

      return $getUserInfo;
    }

    public static function getChainLoyaltyID($chainID){
      $crmSettingsTable = Constants::getGlobalTableName("crmSettings");

      $getLoyaltyID = convertToArray(DB::table($crmSettingsTable)->where("chainID",$chainID)->where("isActive",1)->get());

      if (empty($getLoyaltyID)) {
        return 0;
      }

      return $getLoyaltyID[0]["loyaltyID"];
    }

    public static function getChainDefaultSMS($chainID){
      $listOfChainsTable = self::getGlobalTableName('listOfChains');
      $getSMSGateway = convertToArray(DB::table($listOfChainsTable)->where('chainID', $chainID)->select('defaultSMSGateway')->get());

      if (empty($getSMSGateway)) {
        $defaultSMSGateway = NULL;
      }else{
        $defaultSMSGateway = $getSMSGateway[0]["defaultSMSGateway"];
      }

      return $defaultSMSGateway;

    }

    public static function getGSTInvoiceOption($type = null){
      $subType = array("B2B","SEZWP","SEZWOP","EXPWP","EXPWOP","DEXP");
      $regRev = array("Y","N");
      $IGSTonIntra = array("Y","N");
      $docType = array("INV","CRN","DBN");
      $isServc = array("Y,N");
      $paymentMode = array("Cash","Credit","Direct Transfer");
      $transportMode = array(1 => 'Road',2 => 'Rail',3 => 'Air',4 => 'Ship');
      $vehicleType = array('O'=>'ODC','R'=>'Regular');
      $irnOptions = array();
      $irnOptions['subType'] = $subType;
      $irnOptions['regRev'] = $regRev;
      $irnOptions['IGSTonIntra'] = $IGSTonIntra;
      $irnOptions['docType'] = $docType;
      $irnOptions['isServc'] = $isServc;
      $irnOptions['paymentMode'] = $paymentMode;
      $irnOptions['transportMode'] = $transportMode;
      $irnOptions['vehicleType'] = $vehicleType;
      $ewayBillSupplyType = array('O'=>'Out','I'=>'In');
      $ewayBillSubSupplyType = array('1' => 'Supply','2' => 'Import','3' => 'Export', '4' => 'Job Work','5' => 'For Own Use','6' => 'Job work Returns','7' => 'Sales Return','8' => 'Others', '9' => 'SKD/CKD/Lots', '10' => 'Line Sales', '11' => 'Recipient Not Known', '12' => 'Exhibition or Fairs');
      $ewayBillDocType = array("INV","CHL","BIL","BOE","OTH");
      $ewayBillOptions = array();
      $ewayBillOptions['supplyType'] = $ewayBillSupplyType;
      $ewayBillOptions['subSupplyType'] = $ewayBillSubSupplyType;
      $ewayBillOptions['docType'] = $ewayBillDocType;
      $ewayBillOptions['transportMode'] = $transportMode;
    
      $status = array();
      if(strtolower($type) == 'ewaybill' || $type == ''){
        $status['ewayBillOptions'] = $ewayBillOptions;
      }
      if(strtolower($type) == 'irn' || $type == ''){
        $status['IRNOptions'] = $irnOptions;
      }
    
      return $status;
    
    }

    public static function cleanString($string){
      return preg_replace('/[^A-Za-z0-9\-]/', '', $string); // Removes special chars.
    }

}

function verifyRequiredParams($requiredFields, $dataArray) {
for($i=0;$i < count($requiredFields);$i++)
{
  $flag = 1;
  if(!isset($dataArray[$requiredFields[$i]]))
  {
     $flag-- ; ;
  }
}
if($flag < 1)
return true;
else
return false;
}

function verifyRequiredParamsLength($requiredFields, $dataArray) {
for($i=0;$i < count($requiredFields);$i++)
{
  $flag = 1;
  if(!isset($dataArray[$requiredFields[$i]]) || strlen($dataArray[$requiredFields[$i]]) == 0)
  {
     $flag-- ; ;
  }
}
if($flag < 1)
return true;
else
return false;
}

function queueNumber($chainId)
{
  if ($chainId == "global") {
    return "global";
  }
  if ($chainId == "async") {
    return "async";
  }
  $number = $chainId % 7;
  switch ($number) {
    case 1 : return "one";
    case 2 : return "two";
    case 3 : return "three";
    case 4 : return "four";
    case 5 : return "five";
    case 6 : return "six";
    case 0 : return "seven";
    default: return "default";
  }
}

function getLocalTime($timeZone)
{
  if (!isset($timeZone) || $timeZone == null || $timeZone == "") {
    $timeZone = "Asia/Kolkata";
  }
  date_default_timezone_set($timeZone);

  $time = date('Y-m-d H:i:s');

  return $time;
}

//Used for array returned from convertToArray call on DB query output.
function removeNullValues($tempArray)
{
  for ($i = 0; $i < count($tempArray); $i++) {
            // Unset whatever is null
            foreach ($tempArray[$i] as $key => $value) {
                if ($tempArray[$i][$key] === NULL) {
                    unset($tempArray[$i][$key]);
                }
            }
        }
        return $tempArray ;
}

function getAppId($accessToken)
{
  $oauthClientsTable = Constants::getOAuthTableName("clients");
  $oauthTokenTable = Constants::getOAuthTableName("accessToken");
  $results = convertToArray(DB::table($oauthTokenTable)->where('access_token',$accessToken)->get());
  $clientId = $results[0]['client_id'] ;
  $res = convertToArray(DB::table($oauthClientsTable)->where('client_id',$clientId)->get());
  return $res[0]['appID'];
}

function removeNullKeys($tempArray)
{
            foreach ($tempArray as $key => $value) {
                if ($tempArray[$key] === NULL) {
                    unset($tempArray[$key]);
                }
            }
        return $tempArray ;
}

function generateRandomString($length){

  $stringPool = 'ABCDEFGHIJKLMOPQRSTUVXWYZ0123456789';
  $stringPoolCount = strlen($stringPool);
  $stringPoolCount--;

  $transactionID=NULL;
  
  for($x=1;$x<=$length;$x++){
      $randomID = rand(0,$stringPoolCount);
      $transactionID .= substr($stringPool,$randomID,1);
  }

  return $transactionID;
}

function my_explode($delimiter, $string, $limit = null) {
    $array = call_user_func_array('explode', func_get_args());
    if (count($array) === 1 && strlen($array[0]) === 0) {
        return array();
    }
    return $array;
}

function sanitize ($str){

  //replace &
  $str1 = str_replace("&amp;", "&", $str);
  $str2 = str_replace("&quot;", '"', $str1);
  $str3 = str_replace("&#039;", "'", $str2);

  return $str3;
}

function convertToArray($data)
{
    $result = json_decode(json_encode($data),true);

    // $countElements = sizeof($result,1);

    if (empty($result)) {
      return $result;
    }

    if (!is_array($result)) {
      return $result;
    }
    
    foreach ($result as $key => $value) {
     if(!is_array($value)){
        if (!is_numeric($value)) {
          $result[$key] = sanitize(htmlentities(html_entity_decode($value)));  
        }
        
     }else{
        
      foreach($value as $key1 => $value1){
        if(!is_array($value1)){
          if (!is_numeric($value1)) {
            $result[$key][$key1] = sanitize(htmlentities(html_entity_decode($value1)));
          }
          
        }else{
          foreach($value1 as $key2 => $value2){
            if(!is_array($value2)){
              if (!is_numeric($value2)) {
                $result[$key][$key1][$key2] = sanitize(htmlentities(html_entity_decode($value2)));
              }
              
            }else{
              foreach($value2 as $key3 => $value3){
                if (!is_array($value3)) {
                  if (!is_numeric($value3)) {
                    $result[$key][$key1][$key2][$key3] = sanitize(htmlentities(html_entity_decode($value3)));
                  }

                  
                }else{
                  foreach($value3 as $key4 => $value4){
                    if (!is_array($value4)) {
                      if (!is_numeric($value4)) {
                        $result[$key][$key1][$key2][$key3][$key4] = sanitize(htmlentities(html_entity_decode($value4)));  
                      }

                      
                    }else{
                      foreach($value4 as $key5 => $value5){
                        if (!is_array($value5)) {
                          if (!is_numeric($value5)) {
                            $result[$key][$key1][$key2][$key3][$key4][$key5] = sanitize(htmlentities(html_entity_decode($value5)));
  
                          }

                        }
                      }
                    }
                    
                  }
                }
              }
            }
          }
        }      
       }
     }
              
    }

    return $result ;
}

//function to modify array returned from convertToArray function with DB query. The new array has $key as the key and rest of array items as value.
function createArrayKey($array, $key)
{
  $count = count($array);

  $newArray = array();

  for($i = 0; $i < $count; $i++)
  {
    $newArray[$array[$i][$key]] = $array[$i];
    unset($newArray[$array[$i][$key]][$key]);
  }

  return $newArray;
}

//function to modify array returned from convertToArray function with DB query.
//The new array has $key as the key and rest of array items as value.
// THe key may be comma separated here
function createArrayKeyCSV($array, $key)
{
  $count = count($array);

  $newArray = array();

  for($i = 0; $i < $count; $i++)
  {
    $csv = explode(',', $array[$i][$key]);
    $csvCount = count($csv);

    for($j = 0; $j < $csvCount; $j++)
    {
      $newArray[$csv[$j]] = $array[$i];
      unset($newArray[$csv[$j]][$key]);
    }
  }

  return $newArray;
}

function checkNull($value) {
            if ($value == null) {
                  return '';
            } else {
                  return $value;
            }
      }

function localToUTC($sm, $local)
{
	if (!isset($local) || $local == null || $local == "") {
		$local = "Asia/Kolkata";
	}

	// Check if $sm is a valid date/time string
	if (!isset($sm) || $sm == null || $sm == "" || !is_string($sm) || is_numeric($sm)) {
		$sm = date('Y-m-d H:i:s');
	}

	try {
		$date = new DateTime($sm, new DateTimeZone($local));
		$date->setTimezone(new DateTimeZone('UTC'));
		return $date->format('Y-m-d H:i:s');
	} catch (Exception $e) {
		// Handle the exception (log it if needed)
		return $sm;
	}
}

function utcToLocal($sm,$local)
{
    $date = new DateTime($sm, new DateTimeZone('UTC'));
    $date->setTimezone(new DateTimeZone("$local"));
    return $date->format('Y-m-d H:i:s');
}

function isJson($string)
{
    if (!is_string($string)) {
        return false;
    }

    $string = trim($string);
    $firstChar = substr($string, 0, 1);
    $lastChar = substr($string, -1);
    if (!$firstChar || !$lastChar) {
        return false;
    }
    if ($firstChar !== '{' && $firstChar !== '[') {
        return false;
    }
    if ($lastChar !== '}' && $lastChar !== ']') {
        return false;
    }
    json_decode($string);
    $isValid = json_last_error() === JSON_ERROR_NONE;
    return $isValid;
}

function lengthCheck($fieldString,$lengthValue)
{
    if(strlen($fieldString) < $lengthValue)
    {
        return true ;
    }
    else
    {
        return false ;
    }
}

function validDateTime($date) {
    return (preg_match("/^([0-9]{4})-([0-9]{2})-([0-9]{2})[[:space:]]([0-9]{2}):([0-9]{2}):([0-9]{2})$/", $date));
}


function validTime($time) {
    return (preg_match("/^([0-9]{2}):([0-9]{2}):([0-9]{2})$/", $time));
}

function validEmail($email) {
    //return (preg_match("/^\S+@\S+\.\S+$/", $email));
  return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function keyCheck($dataArray,$keyString)
{
    if(array_key_exists($keyString,$dataArray))
      return $dataArray[$keyString] ;
    else
      return NULL ;
}


function restaurantInfoCheck($data)
{
    foreach($data as $key => $value)
    {
    switch($key)
    {
    case "restName" : if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "shortCode" :if(lengthCheck($value,30)==false)
                       return false ;
                       break;
    case "companyName":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "TIN":if(lengthCheck($value,99)==false)
                       return false ;
                       break;
     case "CIN":if(lengthCheck($value,99)==false)
                       return false ;
                       break;
    case "serviceTaxNum":if(lengthCheck($value,99)==false)
                       return false ;
                       break;
    case "currencyCode":if(lengthCheck($value,10)==false)
                       return false ;
                       break;
    case "currency":if(lengthCheck($value,50)==false)
                       return false ;
                       break;
    case "currencyOnBill":if(lengthCheck($value,10)==false)
                       return false ;
                       break;
    case "brandPhone":if(lengthCheck($value,15)==false)
                       return false ;
                       break;
    case "brandEmail":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "regAddLine1":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "regAddLine2":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "regAddCity":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "regAddState":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "regAddCountry":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "regAddPincode":if(is_int($value))
                       return false ;
                       break;
    case "outletAddLine1":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "outletAddLine2":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "outletAddCity":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "outletAddState":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "outletAddCountry":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "outletAddPincode":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "outletPhone":if(is_int($value))
                       return false ;
                       break;
    case "outletEmail":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "managerName":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "managerPhone":if(is_int($value))
                       return false ;
                       break;
    case "startDateUTC":if(validDateTime($value))
                       return false ;
                       break;
    case "startDateLocal":if(validDateTime($value))
                       return false ;
                       break;
    case "endDateUTC":if(validDateTime($value))
                       return false ;
                       break;
    case "endDateLocal":if(validDateTime($value))
                       return false ;
                       break;
    case "timezone":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "registeredPhone":if(lengthCheck($value,30)==false)
                       return false ;
                       break;
    case "registeredEmail":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "password":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "isActive":if(lengthCheck($value,256)==false)
                       return false ;
                       break;
    case "openingHoursUTC":if(validTime($value)==false)
                       return false ;
                       break;
    case "openingHoursLocal":if(validTime($value)==false)
                       return false ;
                       break;
    case "closingHoursLocal":if(validTime($value)==false)
                       return false ;
                       break;
    case "closingHoursUTC":if(validTime($value)==false)
                       return false ;
                       break;
    default : $flag = 1;
              break ;
    }
    }
        return true ;
}


function keyImplode($data)
{
    if(count($data) > 1)
    {
    $str = implode(',',$data);
  }
  else
  {
    $str = $data[0];
  }
  return $str ;
}

function swap(&$x,&$y) {
    $tmp=$x;
    $x=$y;
    $y=$tmp;
}

function checkDateSize($toDate,$fromDate)
{
   $fromDate                              = strftime("%Y-%m-%d", strtotime("$fromDate +33 day"));
   if($fromDate <= $toDate)
   {
    return false ;
   }else{
    return true ;
   }
}

function checkForNULL($dataArray)
{
if($dataArray != NULL)
  return $dataArray;
else
  return 0.0000;
}

function aasort (&$array, $key) {
    $sorter=array();
    $ret=array();
    reset($array);
    foreach ($array as $ii => $va) {
        $sorter[$ii]=$va[$key];
    }
    asort($sorter);
    foreach ($sorter as $ii => $va) {
        $ret[$ii]=$array[$ii];
    }
    $array=$ret;

    return $array;
}

/**
 * Checks if IRD Integration is enabled for a chain. Returns boolean
 *
 * @param  int  $chainID
 * @return bool
 */
function isIRDEnabled ($chainID) {
	$irdEnabled = false;
	// Check environment variable
    if (env("IRD_ENABLED") != null && env("IRD_ENABLED") == true) {
        $irdEnabled = true;
    } else {
        // Check chain settings
        $listOfChainsTable = Constants::getGlobalTableName('listOfChains');
        $checkIRD = DB::table($listOfChainsTable)->where("chainID", $chainID)->value("enableIRDIntegration");
        if ($checkIRD != null && $checkIRD == 1) {
            $irdEnabled = true;
        }
    }
	return $irdEnabled;
}

/**
 * Get chains currency symbol
 *
 * @param  int  $chainID
 * @return bool
 */
function getCurrencySymbol($chainID = null, $currencyCode = null)
{
    $symbol = "";
    if ($currencyCode == null && $chainID != null) {
        // Check chain settings
        $listOfChainsTable = Constants::getGlobalTableName('listOfChains');
        $currencyCode = DB::table($listOfChainsTable)->where("chainID", $chainID)->value("currencyCode");
    }
    if ($currencyCode != null && $currencyCode != "") {
        // Number format currency code
        $locale = 'en-US';
        $fmt = new \NumberFormatter($locale . "@currency=$currencyCode", \NumberFormatter::CURRENCY);
        $symbol = $fmt->getSymbol(\NumberFormatter::CURRENCY_SYMBOL);
        if ($symbol == null || $symbol == "") {
            $symbol = $currencyCode;
        }
    }
    return $symbol;
}

trait TestHelper{

private static function getAccessToken(){

    return "46134da382448328bd6ad0e9943f2a0079b57dbb";
}

private static function getMerchantID(){

    return "16";
}

/**
  *Parameters: array with fields to be checked
  *Expected Output: none
  *returns : error code and error string
  */

  public static function checkArrayKeyExists($keyArray, $dataArray)
  {
            $error = array();

            $error['error'] = false;
            $error['errorString'] = "";

            foreach ($keyArray as $key => $value)
            {
              if(array_key_exists($key, $dataArray))
              {
                 $error['error'] = true;
                 $error['errorString'] = $error['errorString'] . $key. " exists.";
              }
            }

            return $error;
  }

}


trait APIMonitor
{
	private static $URILimits = array('GeneralGetApkVersion'=>array("time"=>"15","rate"=>"30"),
					'postGetMerchantId'=>array("time"=>"15","rate"=>"30"),
					'GeneralGetPermissions'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerGetRestaurant'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerGetTR'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerGetAccounts'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerGetAdditionalCharges'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerGetAddress'=>array("time"=>"15","rate"=>"30"),
					'MenuControllerGetCategories'=>array("time"=>"15","rate"=>"30"),
					'MenuControllerPostCategories'=>array("time"=>"15","rate"=>"30"),
					'MenuControllerPostCategoryMapSubCategory'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerGetClosingTime'=>array("time"=>"15","rate"=>"30"),
					'CustomerControllerGetCustomers'=>array("time"=>"15","rate"=>"30"),
					'CustomerControllerGetCustomersById'=>array("time"=>"15","rate"=>"30"),
					'CustomerControllerGetCustomersAddress'=>array("time"=>"15","rate"=>"30"),
					'CustomerControllerGetCustomersEmail'=>array("time"=>"15","rate"=>"30"),
					'CustomerControllerGetCustomersPhone'=>array("time"=>"15","rate"=>"30"),
					'DeviceControllerPostDevices'=>array("time"=>"15","rate"=>"30"),
					'DeviceControllerGetDevices'=>array("time"=>"15","rate"=>"30"),
					'DeviceControllerDeleteDevices'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerGetDiscounts'=>array("time"=>"15","rate"=>"30"),
					'EmployeeControllerGetEmployees'=>array("time"=>"15","rate"=>"30"),
					'EmployeeControllerPostEmployee'=>array("time"=>"15","rate"=>"30"),
					'EmployeeControllerDeleteEmployee'=>array("time"=>"15","rate"=>"30"),
					'EmployeeControllerGetEmployeesById'=>array("time"=>"15","rate"=>"30"),
					'EmployeeControllerResetEmployee'=>array("time"=>"15","rate"=>"30"),
					'GeneralGetGlobals'=>array("time"=>"15","rate"=>"30"),
					'DashboardControllerGetGlobalOrderDetails'=>array("time"=>"15","rate"=>"30"),
					'GeneralGetGlobalTypes'=>array("time"=>"15","rate"=>"30"),
					'GeneralGetGlobalTypesById'=>array("time"=>"15","rate"=>"30"),
					'MenuControllerPostMenu'=>array("time"=>"15","rate"=>"30"),
					'MenuControllerGetMenus'=>array("time"=>"15","rate"=>"30"),
					'MenuControllerGetMenu'=>array("time"=>"15","rate"=>"30"),
					'MenuControllerGetModifiers'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllergetRestaurantName'=>array("time"=>"15","rate"=>"30"),
					'NotificationControllerGetNotificationsByAppId'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerGetOfficialTime'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerGetOpeningTime'=>array("time"=>"15","rate"=>"30"),
					'OrderControllerPostOrd'=>array("time"=>"15","rate"=>"30"),
					'ReportsControllerGetKOT'=>array("time"=>"15","rate"=>"30"),
					'OrderControllerUpdateOrd'=>array("time"=>"15","rate"=>"30"),
					'ReportsControllerVoidOrderReport'=>array("time"=>"15","rate"=>"30"),
					'ReportsControllerGetProductsDay'=>array("time"=>"15","rate"=>"30"),
					'ReportsControllerGetDailySalesHourly'=>array("time"=>"15","rate"=>"30"),
					'ReportsControllerGetDailySalesUserWise'=>array("time"=>"15","rate"=>"30"),
					'ReportsControllerGetOrder'=>array("time"=>"15","rate"=>"30"),
					'ReportsControllerGetOrderId'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerGetPayments'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerDelPayments'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerPostUsersPermissions'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerGetUsersPermissions'=>array("time"=>"15","rate"=>"30"),
					'MenuControllerPostProducts'=>array("time"=>"15","rate"=>"30"),
					'MenuControllerGetPCS'=>array("time"=>"15","rate"=>"30"),
					'MenuControllerPutProductPrices'=>array("time"=>"15","rate"=>"30"),
					'TestControllerTestf'=>array("time"=>"60","rate"=>"5"),
					'AccountControllerGetTransac'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerGetRoles'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerPostRoles'=>array("time"=>"15","rate"=>"30"),
					'OrderControllerGetOrderStatus'=>array("time"=>"15","rate"=>"30"),
					'MenuControllerGetSubCategories'=>array("time"=>"15","rate"=>"30"),
					'MenuControllerPostSubCategories'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerGetTaxes'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerGetTimezone'=>array("time"=>"15","rate"=>"30"),
					'AccountControllerPostTransaction'=>array("time"=>"15","rate"=>"30"),
					'AccountControllerGetTransaction'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerPostAddress'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerPostRestClosingTime'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerPostRestOfficialTime'=>array("time"=>"15","rate"=>"30"),
					'RestaurantControllerPostRestOpeningTime'=>array("time"=>"15","rate"=>"30"),
					'MailControllerSendMail'=>array("time"=>"15","rate"=>"30"),
					'postGetToken'=>array("time"=>"15","rate"=>"30"));

    static function getAPITimeLimit($URIName)
    {
    	$URIDetails = self::$URILimits[$URIName];
    	return $URIDetails["time"];
    }

    public static function getAPIWindowLimit($URIName)
    {
            $URIDetails = self::$URILimits[$URIName];
            return $URIDetails["rate"];
    }

}

trait PostOrderJSON
{
  private static $outerSummary =  array("Status" => "ORST_DELIVERED",
                                        "additionalChargeValue" => 0,
                                        "billSettledTimeLocal" => "2016-05-04 13:22:04",
                                        "cardPayment" => 0,
                                        "cashPayment" => 0,
                                        "cashTendered" => 0,
                                        "changeAmount" => 0,
                                        "currency" => "INR",
                                        "deviceID" => "1",
                                        "grossBill" => 0,
                                        "hasCombo" => 0,
                                        "invoiceNumber" => "1",
                                        "isNoCharge" => 0,
                                        "isOpenBill" => 0,
                                        "lastModifiedTimeLocal" => "2016-05-04 13:22:04",
                                        "netBill" => 0,
                                        "numReceiptPrints" => 1,
                                        "orderCreationTimeLocal" => "2016-05-04 13:21:46",
                                        "orderID" => "ORDER-54-780-2016-05-05-111",
                                        "orderType" => "DEL_DINE_IN",
                                        "partySize" => 1,
                                        "paymentType" => "PAYMENT_CASH",
                                        "posDate" => "2016-05-05",
                                        "rounding" => -0.04,
                                        "serverName" => "Maplegraph Data Migration",
                                        "tableID" => 1,
                                        "taxes" => 0,
                                        "timezone" => "Asia/Kolkata",
                                        "tipAmount" => 0,
                                        "userName" => "Maplegraph Data Migration",
                                        "voucherPayment" => 0
                                      );

  private static $payments =      array( "accountId" => 2,
                                        "accountName" => "CashReceived",
                                        "amount" => 1041,
                                        "cardType" => "",
                                        "lastFourDigits" => "",
                                        "transactionID" => ""
                                      );

  private static $productList =   array("Status" => "ORST_DELIVERED",
                                        "additionalChargeValues" => 0,
                                         "incomeHead" => "4",
                                         "isNoCharge" => 0,
                                         "isOpenBill" => 0,
                                         "isOpenProduct" => 0,
                                         "timezone" => "Asia/Kolkata",
                                         "taxableValue" => 375, // BasePrice- Discount + Modifier multiplied by quantity
                                         "orderTimeLocal" => "2016-05-04 13:21:50",
                                         "sizeVariantID" => 35,
                                         "productName" => "The Italian Job",
                                         "productValue" => 496.23, //Final gross value of product
                                         "quantityOrdered" => 1,
                                         "modifierValue" => 0,
                                         "productBasePrice" => 375, //without discount
                                         "orderSubID" => 1,
                                         "numKOTPrint" => 1,
                                         "productID" => 35,
                                         "sizeVariantID" => 1,
                                         "kotNumber" => 1,
                                         "isCombo" => 0,
                                         "hasModifiers" => 0
                                        );

  private static $additionalCharges = array("additionalChargeID" => 1,
                                            "additionalChargePrice" => 37.5,
                                            "additionalChargeValue" => 10,
                                            "additionalChargeName" => "Service Charge @ 10%"
                                        );

  private static $taxes =             array("taxName" => "VAT @ Food 14.5%",
                                        "taxID" => 1,
                                        "taxValue" => 59.81,
                                        "taxPercentage" => 14.5
                                        );

  private static $discounts =   array(
                                        "discountName" => "Discount",
                                        "discountPercentage" => 0,
                                        "discountPrice" => 147.5,
                                        "discountRemarks" => "",
                                        "discountType" => "Percentage",
                                        "discountValue" => 50,
                                        "discountID" => 999
    );

private static $paymentsForNC =      array( "accountId" => 2,
                                        "accountName" => "CashReceived",
                                        "amount" => 0,
                                        "cardType" => "",
                                        "lastFourDigits" => "",
                                        "transactionID" => ""
                                      );

  public static function getOuterSummaryArray()
  {
    return self::$outerSummary;
  }

  public static function getPaymentsArray()
  {
    return self::$payments;
  }

  public static function getPaymentsArrayForNC()
  {
    return self::$paymentsForNC;
  }

  public static function getProductListArray()
  {
    return self::$productList;
  }

  public static function getAdditionalChargesArray()
  {
    return self::$additionalCharges;
  }

  public static function getTaxesArray()
  {
    return self::$taxes;
  }

  public static function getDiscountsArray()
  {
    return self::$discounts;
  }
}


function postToSlack($urls, $data, $restId = 0)
{
      $body = json_encode($data);

      for($i = 0; $i < count($urls); $i++)
      {
        $ch = curl_init($urls[$i]);

        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Content-Length: ' . strlen($body))
        );

        $status = curl_exec($ch);
        curl_close($ch);

        return $status;
    }
}


function encrypt_e($input, $ky) {
  $key = $ky;
  $method = "AES-128-CBC";
  $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length($method));
  $input = pkcs5_pad_e($input, openssl_cipher_iv_length($method));
  $data = openssl_encrypt($input, $method, $key, OPENSSL_RAW_DATA, $iv);
  $data = base64_encode($data . '::' . $iv);
  return $data;
}

function decrypt_e($crypt, $ky) {

  $crypt = base64_decode($crypt);
  $key = $ky;
  $iv = "@@@@&&&&####$$$$";
  $decrypted_data = openssl_decrypt($crypt, 'AES-128-CBC', $key, OPENSSL_RAW_DATA, $iv);
  $decrypted_data = pkcs5_unpad_e($decrypted_data);
  $decrypted_data = rtrim($decrypted_data);
  return $decrypted_data;
}

function pkcs5_pad_e($text, $blocksize) {
  $pad = $blocksize - (strlen($text) % $blocksize);
  return $text . str_repeat(chr($pad), $pad);
}

function pkcs5_unpad_e($text) {
  $pad = ord($text(strlen($text) - 1));
  if ($pad > strlen($text))
    return false;
  return substr($text, 0, -1 * $pad);
}

function generateSalt_e($length) {
  $random = "";
  srand((double) microtime() * 1000000);

  $data = "AbcDE123IJKLMN67QRSTUVWXYZ";
  $data .= "aBCdefghijklmn123opq45rs67tuv89wxyz";
  $data .= "0FGH45OP89";

  for ($i = 0; $i < $length; $i++) {
    $random .= substr($data, (rand() % (strlen($data))), 1);
  }

  return $random;
}

function checkString_e($value) {
  $myvalue = ltrim($value);
  $myvalue = rtrim($myvalue);
  if ($myvalue == 'null')
    $myvalue = '';
  return $myvalue;
}

function getChecksumFromArray($arrayList, $key, $sort=1) {
  if ($sort != 0) {
    ksort($arrayList);
  }
  $str = getArray2Str($arrayList);
  $salt = generateSalt_e(4);
  $finalString = $str . "|" . $salt;
  $hash = hash("sha256", $finalString);
  $hashString = $hash . $salt;
  $checksum = encrypt_e($hashString, $key);
  return $checksum;
}
function getChecksumFromString($str, $key) {
  
  $salt = generateSalt_e(4);
  $finalString = $str . "|" . $salt;
  $hash = hash("sha256", $finalString);
  $hashString = $hash . $salt;
  $checksum = encrypt_e($hashString, $key);
  return $checksum;
}

function verifychecksum_e($arrayList, $key, $checksumvalue) {
  $arrayList = removeCheckSumParam($arrayList);
  ksort($arrayList);
  $str = getArray2Str($arrayList);
  $paytm_hash = decrypt_e($checksumvalue, $key);
  $salt = substr($paytm_hash, -4);

  $finalString = $str . "|" . $salt;

  $website_hash = hash("sha256", $finalString);
  $website_hash .= $salt;

  $validFlag = "FALSE";
  if ($website_hash == $paytm_hash) {
    $validFlag = "TRUE";
  } else {
    $validFlag = "FALSE";
  }
  return $validFlag;
}

function verifychecksum_eFromStr($str, $key, $checksumvalue) {
  $paytm_hash = decrypt_e($checksumvalue, $key);
  $salt = substr($paytm_hash, -4);

  $finalString = $str . "|" . $salt;

  $website_hash = hash("sha256", $finalString);
  $website_hash .= $salt;

  $validFlag = "FALSE";
  if ($website_hash == $paytm_hash) {
    $validFlag = "TRUE";
  } else {
    $validFlag = "FALSE";
  }
  return $validFlag;
}

function getArray2Str($arrayList) {
  $findme   = 'REFUND';
  $findmepipe = '|';
  $paramStr = "";
  $flag = 1;  
  foreach ($arrayList as $key => $value) {
    $pos = strpos($value, $findme);
    $pospipe = strpos($value, $findmepipe);
    if ($pos !== false || $pospipe !== false) 
    {
      continue;
    }
    
    if ($flag) {
      $paramStr .= checkString_e($value);
      $flag = 0;
    } else {
      $paramStr .= "|" . checkString_e($value);
    }
  }
  return $paramStr;
}

function redirect2PG($paramList, $key) {
  $hashString = getchecksumFromArray($paramList,$key);
  $checksum = encrypt_e($hashString, $key);
}

function removeCheckSumParam($arrayList) {
  if (isset($arrayList["CHECKSUMHASH"])) {
    unset($arrayList["CHECKSUMHASH"]);
  }
  return $arrayList;
}

function getTxnStatus($requestParamList) {
  return callAPI(env('PAYTM_STATUS_QUERY_URL'), $requestParamList);
}

function getTxnStatusNew($requestParamList) {
  return callNewAPI(env('PAYTM_STATUS_QUERY_NEW_URL'), $requestParamList);
}

function initiateTxnRefund($requestParamList) {
  $CHECKSUM = getChecksumFromArray($requestParamList,env('PAYTM_MERCHANT_KEY'),0);
  $requestParamList["CHECKSUM"] = $CHECKSUM;
  return callAPI(env('PAYTM_REFUND_URL'), $requestParamList);
}

function callAPI($apiURL, $requestParamList) {
  $jsonResponse = "";
  $responseParamList = array();
  $JsonData =json_encode($requestParamList);
  $postData = 'JsonData='.urlencode($JsonData);
  $ch = curl_init($apiURL);
  curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");                                                                     
  curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);                                                                  
  curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); 
  curl_setopt ($ch, CURLOPT_SSL_VERIFYHOST, 0);
  curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER, 0);
  curl_setopt($ch, CURLOPT_HTTPHEADER, array(                                                                         
  'Content-Type: application/json', 
  'Content-Length: ' . strlen($postData))                                                                       
  );  
  $jsonResponse = curl_exec($ch);   
  $responseParamList = json_decode($jsonResponse,true);
  return $responseParamList;
}

function callNewAPI($apiURL, $requestParamList) {
  $jsonResponse = "";
  $responseParamList = array();
  $JsonData =json_encode($requestParamList);
  $postData = 'JsonData='.urlencode($JsonData);
  $ch = curl_init($apiURL);
  curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");                                                                     
  curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);                                                                  
  curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); 
  curl_setopt ($ch, CURLOPT_SSL_VERIFYHOST, 0);
  curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER, 0);
  curl_setopt($ch, CURLOPT_HTTPHEADER, array(                                                                         
  'Content-Type: application/json', 
  'Content-Length: ' . strlen($postData))                                                                       
  );  
  $jsonResponse = curl_exec($ch);   
  $responseParamList = json_decode($jsonResponse,true);
  return $responseParamList;
}

function getPayMethod($accType){
    $payMethod = "";

    switch ($accType) {
        case 'ACC_CASH_RECD':
            $payMethod = 'PAYMENT_CASH';
            break;

        case 'ACC_CARD_RECD':
            $payMethod = 'PAYMENT_CARD';
            break;

        case 'ACC_PARTY_MASTER':
            $payMethod = 'PAYMENT_PARTY';
            break;

        case 'ACC_NO_CHARGE_RECD':
            $payMethod = 'PAYMENT_NO_CHARGE';
            break;

        case 'ACC_VOUCHER_RECD':
            $payMethod = 'PAYMENT_VOUCHER';
            break;

        case 'ACC_WALLET_RECD':
            $payMethod = 'PAYMENT_WALLET';
            break;

        case 'ACC_CREDIT_SALE':
            $payMethod = 'PAYMENT_CREDIT';
            break;

        case 'ACC_CHEQUE_RECD':
            $payMethod = 'PAYMENT_CHEQUE';
            break;
        
        default:
            # code...
            break;

    }
    return $payMethod;
}

function getPayMethodName($payType){
    $payMethod = "";

    switch ($payType) {
        case 'PAYMENT_CASH':
            $payMethod = 'Cash';
            break;

        case 'PAYMENT_CARD':
            $payMethod = 'Card';
            break;

        case 'PAYMENT_PARTY':
            $payMethod = 'Party';
            break;

        case 'PAYMENT_NO_CHARGE':
            $payMethod = 'No Charge';
            break;

        case 'PAYMENT_VOUCHER':
            $payMethod = 'Voucher';
            break;

        case 'PAYMENT_WALLET':
            $payMethod = 'Wallet';
            break;

        case 'PAYMENT_CREDIT':
            $payMethod = 'Credit';
            break;

        case 'PAYMENT_CHEQUE':
            $payMethod = 'Cheque';
            break;

        case 'PAYMENT_UPI':
            $payMethod = 'UPI';
            break;  

        case 'PAYMENT_BHARATQR':
            $payMethod = 'Bharat QR';
            break;        
        
        default:
            # code...
            break;

    }
    return $payMethod;
}

function generateBarcode($chainID, $productID, $variantID){
              $barcode = "";
    $counter = 0;
    $limit = 13;
    $chainLength = strlen($chainID);
    $prodLength = strlen($productID);
    $varLength = strlen($variantID);
    $counter = $chainLength + $prodLength + $varLength;
    $nullCounter = "";

    if ($counter >= 13) {

        for ($i=$counter; $i >12; $i--) { 
            $chainID = substr($chainID, 1);
               
            $chainLength = strlen($chainID);    
        }
    }elseif ($counter < 12) {

        for ($i=$counter; $i < 12; $i++) { 
            $nullCounter.="0";
        }
    }
      
    $barcode.=$chainID.$nullCounter.$productID.$variantID;

    $evenSum = 0;
    $oddSum = 0;
    for ($i=0; $i < 12; $i++) { 
        $num = substr( $barcode, $i, 1 );
        
        if ($i%2 == 0) {
            $oddSum += $num;
        }else{
            $evenSum +=$num;
        }
    }

    $evenSum*=3;

    $finalSum = $evenSum + $oddSum;

    $roundUpVal = ceil($finalSum/10)*10;

    $finalNum = $roundUpVal - $finalSum;

    $barcode.=$finalNum;

    return $barcode;
}

function validateDate($date, $format = 'Y-m-d')
{
    $d = DateTime::createFromFormat($format, $date);
    // The Y ( 4 digits year ) returns TRUE for any integer with any number of digits so changing the comparison from == to === fixes the issue.
    return $d && $d->format($format) === $date;
}


function getCardType($cardType){
    $payMethod = "";

    switch ($cardType) {
        case 'CARD_VISA':
            $cardName = 'Visa';
            break;

        case 'CARD_MASTER':
            $cardName = 'Master Card';
            break;

        case 'CARD_MAESTRO':
            $cardName = 'Maestro Card';
            break;

        case 'CARD_AMEX':
            $cardName = 'Amex';
            break;

        case 'CARD_OTHER':
            $cardName = 'Other';
            break;

        case 'WALLET_PAYTM':
            $cardName = 'Paytm';
            break;

        case 'WALLET_MOBIKWIK':
            $cardName = 'Mobikwik';
            break;

        case 'WALLET_FREECHARGE':
            $cardName = 'Freecharge';
            break;

        case 'WALLET_OXIGEN':
            $cardName = 'Oxigen';
            break;

        case 'WALLET_PHONEPE':
            $cardName = 'PhonePe';
            break;

        case 'WALLET_GPAY':
            $cardName = 'Gpay';
            break;

        case 'WALLET_BHARATPE':
            $cardName = 'BharatPe';
            break;

        case 'WALLET_AMAZON':
            $cardName = 'Amazon Pay';
            break;

        case 'WALLET_OTHER':
            $cardName = 'Other';
            break;
        
        default:
            $cardName = '';
            break;

    }
    return $cardName;
}

function getPaymentModes($storeID = null, $onlyActive = 0, $fromDate = null, $toDate = null, $chainID = null, $isSalesOrder = false) {
	$customCondition = "";
	if ($storeID != null && $storeID > 0 && $storeID != "" && $storeID != "ALL" && $isSalesOrder == false) {
		if ($fromDate != null && $toDate != null) {
			$storeOrderPaymentsTable = Constants::getTableName($storeID,"orderPayments");
			// get payment types
			$paymentTypes = [];
			$fetchPaymentTypes = convertToArray(DB::select(DB::raw("SELECT DISTINCT paymentType FROM $storeOrderPaymentsTable WHERE paymentPosDate BETWEEN '$fromDate' AND '$toDate'")));
			for ($i=0; $i < count($fetchPaymentTypes); $i++) { 
				array_push($paymentTypes, "'" . $fetchPaymentTypes[$i]["paymentType"] . "'");
			}
			if (count($paymentTypes) > 0) {
				$paymentTypes = implode(",", $paymentTypes);
					$customCondition = "AND value IN ($paymentTypes)";
			}
		} else {
			$paymentSettingsTable = Constants::getTableName($storeID, "paymentSettings");
			$activeCondition = "";
			if ($onlyActive == 1) {
				$activeCondition = " WHERE isActive = 1";
			}
			$customCondition = " AND ID IN (SELECT DISTINCT paymentTypeID FROM $paymentSettingsTable $activeCondition)";
		}
	}
	if ($chainID != null && $chainID != "" && $chainID != "ALL") {
		if ($fromDate != null && $toDate != null) {
			if ($isSalesOrder) {
				$chainOrderPaymentsTable = Constants::getChainTableName($chainID, "salesOrderPayments");
			} else {
				$chainOrderPaymentsTable = Constants::getChainTableName($chainID,'orderPayments');
			}
				
			// get payment types
			$paymentTypes = [];
			$fetchPaymentTypes = convertToArray(DB::select(DB::raw("SELECT DISTINCT paymentType FROM $chainOrderPaymentsTable WHERE paymentPosDate BETWEEN '$fromDate' AND '$toDate'")));
			for ($i=0; $i < count($fetchPaymentTypes); $i++) { 
				array_push($paymentTypes, "'" . $fetchPaymentTypes[$i]["paymentType"] . "'");
			}
			if (count($paymentTypes) > 0) {
				$paymentTypes = implode(",", $paymentTypes);
					$customCondition = "AND value IN ($paymentTypes)";
			}
		}
	}
	$allModesQuery = "SELECT typeID, value, printName, description FROM globalTypeValues WHERE typeID IN (7, 8, 32) AND isActive = 1 $customCondition ORDER BY typeID, ID;";
	$allModes = convertToArray(DB::select(DB::raw($allModesQuery)));

	$paymentModes = "";
	$allValues = array();
	$allPaymentModes = array();
	$paymentModeNames = array();
	$cardNames = array();
	$walletNames = array();
	$valueNameMap = array();
	for ($i=0; $i < count($allModes); $i++) {
		$currentMode = $allModes[$i];
		$typeID = $currentMode["typeID"];
		$value = $currentMode["value"];
		$printName = $currentMode["printName"];
		$description = $currentMode["description"];
		array_push($allValues, $value);
		array_push($allPaymentModes, [
			"value" => $value,
			"printName" => $printName,
			"typeID" => $typeID
		]);
    	$valueNameMap[$value] = $printName;
		switch ($typeID) {
			case 7:
				$paymentModes .= "sum(CASE WHEN A.paymentType = '{$value}' THEN round(A.amount,2) ELSE 0.00 END) AS '{$value}',";
				array_push($paymentModeNames, $printName);
				break;
			case 8:
				$paymentModes .= "round(sum(CASE WHEN A.cardType = '{$value}' AND A.paymentType = 'PAYMENT_CARD' THEN A.amount ELSE 0 END),2) AS '{$value}',";
				array_push($cardNames, $printName);
				break;
			case 32:
				$paymentModes .= "round(sum(CASE WHEN (A.cardType = '{$value}' OR A.cardType = '{$description}') AND A.paymentType = 'PAYMENT_WALLET' THEN A.amount ELSE 0 END),2) AS '{$value}',";
				array_push($walletNames, $printName);
				break;
			default:
				# code...
				break;
		}
	}

	// Total
	$paymentModes .= "sum(CASE WHEN A.paymentType != 'PAYMENT_NO_CHARGE' THEN round(A.amount,2) ELSE 0.00 END) AS Total,";
	array_push($allValues, "Total");
	array_push($allPaymentModes, [
		"value" => "Total",
		"printName" => "Total",
		"typeID" => 7
	]);

	$response["modes"] = $paymentModes;
	$response["allValues"] = $allValues;
	// $response["paymentModeNames"] = $paymentModeNames;
	// $response["cardNames"] = $cardNames;
	// $response["walletNames"] = $walletNames;
	$response["allPaymentModes"] = $allPaymentModes;
	$response["valueNameMap"] = $valueNameMap;
	return $response;
}

function getSMSTemplate ($templateID) {
	if ($templateID == null || $templateID == "") {
		return [];
	}
	$file = storage_path("smsTexts.json"); // storage_path() . '/smsTexts.json';
	if(!File::exists($file)) {
    	Log::info("SMS File not found");
		$smsTemplates = json_decode('[{"senderID":"BUSTER","templateHead":"Signup OTP","templateKey":"signup_otp_qb","templateID":"1207162097190997995","messageEN":"One Time Password for QueueBuster registration is {#var1#}","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"Post Signup Login Details","templateKey":"signup_pwd_qb","templateID":"1207162097185326476","messageEN":"Thank you for signing up! Log in to QueueBuster and start engaging with your customers Username: {#var1#} Password: {#var2#} www.queuebuster.co","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"eStore Created","templateKey":"estore_new_qb","templateID":"1207162097176480292","messageEN":"Congratulations! Your store is now online. Visit shop.queuebuster.co/{#var1#}","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"QB Order Details","templateKey":"order_details_point_qb","templateID":"1207162097165890525","messageEN":"Order ID : {#var1#} Amount : {#var2#} Points Earned : {#var3#} Points Redeemed : {#var4#} Available Points : {#var5#} Receipt : https://tinyurl.com/{#var6#} Sent via QueueBuster.","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"Credit Note Issued","templateKey":"cn_issue_qb","templateID":"1207162097152860155","messageEN":"Dear Customer, We have credited {#var1#} to your account at {#var2#}. Regards, {#var3#} Sent via QueueBuster.","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"eStore Order Rejected","templateKey":"estore_rej_qb","templateID":"1207162097196169248","messageEN":"Sadly, {#var1#} was unable to accept your order {#var2#}. If paid, your refund will be processed in 5-7 working days. We hope to serve you better next time. Sent via QueueBuster.","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"Loyalty Points Expiry","templateKey":"loyalty_exp_qb","templateID":"1207162097160329806","messageEN":"Dear Customer, your points with {#var1#} will soon get expired in {#var2#} days. Kindly redeem your points and get amazing discounts on your shopping. Sent via QueueBuster.","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"eStore Order Placed","templateKey":"estore_ord_qb","templateID":"1207162097201279375","messageEN":"Your order {#var1#} from {#var2#}, amounting to {#var3#} has been placed. Check email for more details. Sent via QueueBuster.","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"OTP for Credit Note","templateKey":"cn_add_qb","templateID":"1207162097143822615","messageEN":"OTP is {#var1#} to add {#var2#} at {#var3#}. Regards, {#var4#} Sent via QueueBuster.","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"OTP to Approve Transaction","templateKey":"cn_redeem_qb","templateID":"1207162607305545627","messageEN":"OTP to approve transaction of {#var1#} to {#var2#} is {#var3#}. Sent via QueueBuster.","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"Khata Payment Reminder","templateKey":"khata_rem_qb","templateID":"1207162607265111049","messageEN":"Hi {#var1#}, this is a payment reminder from {#var2#}. Balance due: INR {#var3#}. Kindly make the payment soon. Sent via QueueBuster.","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"Loyalty Points Earned","templateKey":"loyalty_bal_qb","templateID":"1207162607205243605","messageEN":"Thank you for shopping at {#var1#}. You have earned {#var2#} points. Your current balance is {#var3#} points. Sent via QueueBuster.","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"OTP Login","templateKey":"login_qb","templateID":"1207162624382920754","messageEN":"One Time Password for Login is {#var1#} - Sent via QueueBuster","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"OTP Forgot Password","templateKey":"forgot_qb","templateID":"1207162624388456221","messageEN":"One Time Password for Forgot Password is {#var1#} - Sent via QueueBuster","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"OTP eStore Signup","templateKey":"estore_signup_qb","templateID":"1207162624397083520","messageEN":"One Time Password for eStore Signup is {#var1#} - Sent via QueueBuster","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"OTP Password Change","templateKey":"password_qb","templateID":"1207162624403318557","messageEN":"One Time Password for Password Change Request is {#var1#} - Sent via QueueBuster","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"SMS Order Details","templateKey":"order_details_old_qb","templateID":"1207162637890491485","messageEN":"Thanks for shopping at {#var1#}. Order No: {#var2#} Amount: {#var3#} Receipt: {#var4#} Sent via QueueBuster","peID":"1201159187823864253"},{"senderID":"BUSTER","templateHead":"SMS Order Details New","templateKey":"order_details_qb","templateID":"1207162701926315140","messageEN":"Thanks for shopping at {#var1#}. Order No: {#var2#} Amount: {#var3#} Receipt: {#var4#} Sent via QueueBuster","peID":"1201159187823864253"},{"senderID":"PLGENI","templateHead":"Genie Signup OTP","templateKey":"signup_otp_genie","templateID":"1207166117686211524","messageEN":"OTP for Pine Labs Genie registration is {#var1#} - Sent by QueueBuster","peID":"1201159187823864253"},{"senderID":"PLGENI","templateHead":"Genie Post Signup SMS","templateKey":"signup_pwd_genie","templateID":"1207166117679104810","messageEN":"Thanks for signing up! Login to Pine Labs Genie app - Username: {#var1#} Password: {#var2#} pinelabs.com/genie - Sent by QueueBuster","peID":"1201159187823864253"},{"senderID":"PLGENI","templateHead":"Genie eStore Created","templateKey":"estore_new_genie","templateID":"1207162609133771738","messageEN":"Congratulations! Your online store is now ready. Visit https://shop.queuebuster.co/{#var1#} - Sent by Pine Labs","peID":"1201159187823864253"},{"senderID":"PLGENI","templateHead":"OTP Login","templateKey":"login_genie","templateID":"1207162624382920754","messageEN":"One Time Password for Login is {#var1#} - Sent via QueueBuster","peID":"1201159187823864253"},{"senderID":"PLGENI","templateHead":"OTP Forgot Password","templateKey":"forgot_genie","templateID":"1207162624388456221","messageEN":"One Time Password for Forgot Password is {#var1#} - Sent via QueueBuster","peID":"1201159187823864253"},{"senderID":"PLGENI","templateHead":"OTP eStore Signup","templateKey":"estore_signup_genie","templateID":"1207162624397083520","messageEN":"One Time Password for eStore Signup is {#var1#} - Sent via QueueBuster","peID":"1201159187823864253"},{"senderID":"PLGENI","templateHead":"OTP Password Change","templateKey":"password_genie","templateID":"1207162624403318557","messageEN":"One Time Password for Password Change Request is {#var1#} - Sent via QueueBuster","peID":"1201159187823864253"},{"senderID":"PLGENI","templateHead":"SMS Order Details","templateKey":"order_details_old_genie","templateID":"1207162637890491485","messageEN":"Thanks for shopping at {#var1#}. Order No: {#var2#} Amount: {#var3#} Receipt: {#var4#} Sent via QueueBuster","peID":"1201159187823864253"},{"senderID":"PLGENI","templateHead":"SMS Order Details New","templateKey":"order_details_genie","templateID":"1207162701926315140","messageEN":"Thanks for shopping at {#var1#}. Order No: {#var2#} Amount: {#var3#} Receipt: {#var4#} Sent via QueueBuster","peID":"1201159187823864253"},{"senderID":"QUEUEB","templateHead":"Signup OTP","templateKey":"signup_otp_qb","templateID":"1707165476323561777","messageEN":"One Time Password for QueueBuster registration is {#var1#} {#var2#}","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"OTP Reset Password","templateKey":"forgot_qb","templateID":"1707165501940178291","messageEN":"OTP to Reset Password is {#var1#} - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"OTP Login","templateKey":"login_qb","templateID":"1707165501946459024","messageEN":"OTP for Login is {#var1#} - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"OTP Password Change","templateKey":"password_qb","templateID":"1707165501951483238","messageEN":"OTP for Password Change Request is {#var1#} - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"OTP Approve Transaction","templateKey":"cn_redeem_qb_old","templateID":"1707165501959381349","messageEN":"OTP to Approve Transaction of {#var1#} to {#var2#} is {#var3#} - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"OTP Credit Note Redemption","templateKey":"cn_redeem_qb","templateID":"1707165501966437089","messageEN":"OTP to Redeem {#var1#} at {#var2#} is {#var3#} - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"OTP eStore Signup","templateKey":"estore_signup_qb","templateID":"1707165501972542131","messageEN":"OTP for eStore Signup is {#var1#} - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"OTP for Credit Note Issue","templateKey":"cn_add_qb","templateID":"1707165501984649148","messageEN":"OTP is {#var1#} to add {#var2#} at {#var3#}. Regards, {#var4#} - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"SMS Order Details","templateKey":"order_details_qb","templateID":"1707165502598560859","messageEN":"Thanks for shopping at {#var1#}. Order No: {#var2#} Amount: {#var3#} Receipt: {#var4#} - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"Post Signup Login Details","templateKey":"signup_pwd_qb","templateID":"1707165502622978646","messageEN":"Thanks for signing! Your QueueBuster login details are - Username: {#var1#} and Password: {#var2#} https://queuebuster.co","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"Khata Payment Reminder","templateKey":"khata_rem_qb","templateID":"1707165502690230118","messageEN":"Hi {#var1#}, this is a payment reminder from {#var2#}. Balance Due: {#var3#}. Kindly make the payment - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"Loyalty Points Earned","templateKey":"loyalty_bal_qb","templateID":"1707165502697535961","messageEN":"Thanks for shopping at {#var1#}. You have earned {#var2#} points. Your current balance is {#var3#} points - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"Credit Note Issued","templateKey":"cn_issue_qb","templateID":"1707165502707993214","messageEN":"Dear Customer, We have credited {#var1#} to your account at {#var2#}. Regards {#var3#} - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"eStore Created","templateKey":"estore_new_qb","templateID":"1707165502723454830","messageEN":"Congratulations! Your store is now online. Your eStore link is: shop.queuebuster.co/{#var1#}","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"eStore Order Placed","templateKey":"estore_ord_qb","templateID":"1707165502735297103","messageEN":"Your order {#var1#} from {#var2#}, amount {#var3#} has been placed. Check email for details - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"eStore Order Rejected","templateKey":"estore_rej_qb","templateID":"1707165502744242606","messageEN":"Sadly, {#var1#} was unable to accept your order {#var2#}. If paid, your refund will be processed in 5-7 working days. Sorry for the inconvenience - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"eStore Order Dispatched","templateKey":"estore_ship_qb","templateID":"1707165502750727115","messageEN":"Your order {#var1#} from {#var2#}, amount {#var3#} has been dispatched. Check email for details - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"eStore Order Delivered","templateKey":"estore_del_qb","templateID":"1707165502755737629","messageEN":"Your order {#var1#} from {#var2#}, amount {#var3#} has been delivered. Check email for details - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"Loyalty Points Expiry","templateKey":"loyalty_exp_qb","templateID":"1707165502774156346","messageEN":"Dear Customer, your {#var1#} loyalty points with {#var2#} will expire in {#var3#} days. Kindly redeem soon - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"QUEUEB","templateHead":"Customer OTP Verification","templateKey":"customer_otp_verification","templateID":"1707166117544251977","messageEN":"OTP for verification at {#var1#} is {#var2#} - Sent via QueueBuster","peID":"1701165363932144974","smsProvider":"GUPSHUP"},{"senderID":"Mugushop","templateHead":"Signup OTP","templateKey":"signup_otp_mugu","templateID":"9999999476323561777","messageEN":"One Time Password for Mugu Shop registration is {#var1#} {#var2#}","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"OTP Reset Password","templateKey":"forgot_mugu","templateID":"9999999501940178291","messageEN":"OTP to Reset Password is {#var1#} - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"OTP Login","templateKey":"login_mugu","templateID":"9999999501946459024","messageEN":"OTP for Login is {#var1#} - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"OTP Password Change","templateKey":"password_mugu","templateID":"9999999501951483238","messageEN":"OTP for Password Change Request is {#var1#} - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"OTP Approve Transaction","templateKey":"cn_redeem_mugu_old","templateID":"9999999501959381349","messageEN":"OTP to Approve Transaction of {#var1#} to {#var2#} is {#var3#} - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"OTP Credit Note Redemption","templateKey":"cn_redeem_mugu","templateID":"9999999501966437089","messageEN":"OTP to Redeem {#var1#} at {#var2#} is {#var3#} - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"OTP eStore Signup","templateKey":"estore_signup_mugu","templateID":"9999999501972542131","messageEN":"OTP for eStore Signup is {#var1#} - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"OTP for Credit Note Issue","templateKey":"cn_add_mugu","templateID":"9999999501984649148","messageEN":"OTP is {#var1#} to add {#var2#} at {#var3#}. Regards, {#var4#} - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"SMS Order Details","templateKey":"order_details_mugu","templateID":"9999999502598560859","messageEN":"Thanks for shopping at {#var1#}. Order No: {#var2#} Amount: {#var3#} Receipt: {#var4#} - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"Post Signup Login Details","templateKey":"signup_pwd_mugu","templateID":"9999999502622978646","messageEN":"Thanks for signing! Your MuguShop login details are - Username: {#var1#} and Password: {#var2#} https://mugu.shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"Khata Payment Reminder","templateKey":"khata_rem_mugu","templateID":"9999999502690230118","messageEN":"Hi {#var1#}, this is a payment reminder from {#var2#}. Balance Due: {#var3#}. Kindly make the payment - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"Loyalty Points Earned","templateKey":"loyalty_bal_mugu","templateID":"9999999502697535961","messageEN":"Thanks for shopping at {#var1#}. You have earned {#var2#} points. Your current balance is {#var3#} points - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"Credit Note Issued","templateKey":"cn_issue_mugu","templateID":"9999999502707993214","messageEN":"Dear Customer, We have credited {#var1#} to your account at {#var2#}. Regards {#var3#} - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"eStore Created","templateKey":"estore_new_mugu","templateID":"9999999502723454830","messageEN":"Congratulations! Your store is now online. Your eStore link is: store.mugu.shop/{#var1#}","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"eStore Order Placed","templateKey":"estore_ord_mugu","templateID":"9999999502735297103","messageEN":"Your order {#var1#} from {#var2#}, amount {#var3#} has been placed. Check email for details - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"eStore Order Rejected","templateKey":"estore_rej_mugu","templateID":"9999999502744242606","messageEN":"Sadly, {#var1#} was unable to accept your order {#var2#}. If paid, your refund will be processed in 5-7 working days. Sorry for the inconvenience - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"eStore Order Dispatched","templateKey":"estore_ship_mugu","templateID":"9999999502750727115","messageEN":"Your order {#var1#} from {#var2#}, amount {#var3#} has been dispatched. Check email for details - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"eStore Order Delivered","templateKey":"estore_del_mugu","templateID":"9999999502755737629","messageEN":"Your order {#var1#} from {#var2#}, amount {#var3#} has been delivered. Check email for details - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"Loyalty Points Expiry","templateKey":"loyalty_exp_mugu","templateID":"9999999502774156346","messageEN":"Dear Customer, your {#var1#} loyalty points with {#var2#} will expire in {#var3#} days. Kindly redeem soon - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"Mugushop","templateHead":"Customer OTP Verification","templateKey":"customer_otp_verification_mugu","templateID":"9999999476323561888","messageEN":"OTP for verification at {#var1#} is {#var2#} - Sent via Mugu Shop","peID":"********99","smsProvider":"SPARROW"},{"senderID":"***********","templateHead":"Signup OTP","templateKey":"signup_otp_tw","templateID":"7777777476323561777","messageEN":"One Time Password for QueueBuster registration is {#var1#} {#var2#}","peID":"**********","smsProvider":"TWILIO"},{"senderID":"***********","templateHead":"OTP Reset Password","templateKey":"forgot_tw","templateID":"7777777501940178291","messageEN":"OTP to Reset Password is {#var1#} - Sent via QueueBuster","peID":"**********","smsProvider":"TWILIO"}]', true);
	} else {
		$data = file_get_contents($file);
		$smsTemplates = json_decode($data, true);
	}
	for ($i=0; $i < count($smsTemplates); $i++) {
		if ($smsTemplates[$i]["templateID"] == $templateID) {
			return $smsTemplates[$i];
		}
	}
	return [];
}

function getPaymentAccounts($storeID = null, $onlyActive = 0, $fromDate = null, $toDate = null)
{
	$customCondition = "AND ID IN (SELECT accountTypeValueID FROM globalTypePaymentAccountIDMapping) ";
	if ($storeID != null && $storeID > 0 && $storeID != "" && $storeID != "ALL") {
		if ($fromDate != null && $toDate != null) {
			$orderAccountingTable = Constants::getTableName($storeID, "orderAccounting");
			$storeOrderPaymentsTable = Constants::getTableName($storeID,"orderPayments");
			// get account types
			$accountTypes = [];
			$fetchAccountTypes = convertToArray(DB::select(DB::raw("SELECT DISTINCT accountType FROM $orderAccountingTable WHERE transactionPaymentType IN (SELECT DISTINCT paymentType FROM $storeOrderPaymentsTable WHERE paymentPosDate BETWEEN '$fromDate' AND '$toDate')")));
			for ($i=0; $i < count($fetchAccountTypes); $i++) { 
				array_push($accountTypes, "'" . $fetchAccountTypes[$i]["accountType"] . "'");
			}
      if (count($accountTypes) > 0) {
        $accountTypes = implode(",", $accountTypes);
        $customCondition = "AND value IN ($accountTypes)";
      }
		} else {
			$paymentSettingsTable = Constants::getTableName($storeID, "paymentSettings");
			$activeCondition = "";
			if ($onlyActive == 1) {
				$activeCondition = " WHERE isActive = 1";
			}
			$customCondition = "AND ID IN (SELECT accountTypeValueID FROM globalTypePaymentAccountIDMapping WHERE paymentTypeValueID IN (SELECT DISTINCT paymentTypeID FROM $paymentSettingsTable $activeCondition)) ";
		}
	}
	$allModesQuery = "SELECT ID, typeID, value, printName, description FROM globalTypeValues WHERE typeID = 1 AND isActive = 1 $customCondition ORDER BY typeID, ID";

	$allModes = convertToArray(DB::select(DB::raw($allModesQuery)));

	$allValues = array();
	$allPayAccModes = array();
	$payAccModeNames = array();
	for ($i = 0; $i < count($allModes); $i++) {
		$currentMode = $allModes[$i];
		$ID = $currentMode["ID"];
		$typeID = $currentMode["typeID"];
		$value = $currentMode["value"];
		$printName = $currentMode["printName"];
		$description = $currentMode["description"];
		array_push($allValues, $value);
		array_push($allPayAccModes, [
			"ID" => $ID,
			"value" => $value,
			"printName" => $printName,
			"typeID" => $typeID,
		]);
		array_push($payAccModeNames, $printName);
	}

	$response["allValues"] = $allValues;
	$response["allPayAccModes"] = $allPayAccModes;
	// $response["payAccModeNames"] = $payAccModeNames;
	return $response;
}

function getChainEmailConfig ($chainID, $storeID = null) {
  $chainSettingsTable = Constants::getGlobalTableName("chainSettings");
  $emailConfig = convertToArray(DB::select(DB::raw("SELECT emailDriver, emailHost, emailPort, emailFrom, emailEncryption, emailUsername, emailPassword FROM $chainSettingsTable WHERE chainRefID = ?"), [$chainID]));
  return $emailConfig[0];
}

function useChainInvoicing($chainID, $fromDate) {
	if (!isset($chainID) || !isset($fromDate)) {
		return false;
	}
	$chainSettingsTable = Constants::getGlobalTableName("chainSettings");
	$listOfChainsTable = Constants::getGlobalTableName("listOfChains");

	$useChainInvoicing = convertToArray(DB::select(DB::raw("SELECT recordChainLevelSalesInvoice, chainSalesInvoiceStartDate, DATE(startDateLocal) as startDateLocal FROM $chainSettingsTable A INNER JOIN $listOfChainsTable B ON A.chainRefID = B.chainID WHERE chainRefID = ? ;"), [$chainID]));
	if (empty($useChainInvoicing)) {
		return false;
	}
	if (count($useChainInvoicing) == 0) {
		return false;
	}
	// if chainSalesInvoiceStartDate == startDateLocal return true
	if ($useChainInvoicing[0]["chainSalesInvoiceStartDate"] == $useChainInvoicing[0]["startDateLocal"]) {
		return true;
	}
	$useChainInvoicing = $useChainInvoicing[0];
	$recordChainLevelSalesInvoice = $useChainInvoicing["recordChainLevelSalesInvoice"];
	$chainSalesInvoiceStartDate = $useChainInvoicing["chainSalesInvoiceStartDate"];
	if ($recordChainLevelSalesInvoice == 0) {
		return false;
	}
	if ($chainSalesInvoiceStartDate == null) {
		return false;
	}
	$chainSalesInvoiceStartDate = date("Y-m-d", strtotime($chainSalesInvoiceStartDate));
	if (date("Y-m-d", strtotime($fromDate)) < $chainSalesInvoiceStartDate) {
		return false;
	}
	return true;
}



function commonCurlRequestFunctionMQ($data, $chainID, $urri, $headerInfo, $method = "POST"){
  try{
    $JsonData = json_encode($data);
    $ch = curl_init($urri);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $JsonData);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headerInfo);
    $jsonResponse = curl_exec($ch);
    $response = json_decode($jsonResponse, true);
    return $response;
  } catch(\Exception $ex){
     Log::info($ex->getMessage());
  }
}

function generateChecksum($dataArray)
{
    ksort($dataArray);
    $strFormData="";
    $secretKey = env('APP_KEY');
    // convert formdata key and value to a single string variable
    if(!empty($dataArray) && count($dataArray) >0)
    {
        foreach($dataArray as $key => $val): 
            $strFormData .= $key . "=" . $val . "&"; 
        endforeach;
    }
    $strFormData = substr($strFormData, 0, -1);
    $checkSum = hash_hmac('sha256', $strFormData,$secretKey);
    if(!empty($checkSum))
    {
        $status['status']   = true;
        $status['request']  = $dataArray;
        $status['checksum'] = $checkSum;
    } 
    return $status;
}

function getExcelColFromNumber($num)
{
    $numeric = $num % 26;
    $letter = chr(65 + $numeric);
    $num2 = intval($num / 26);
    if ($num2 > 0) {
        return getExcelColFromNumber($num2 - 1) . $letter;
    } else {
        return $letter;
    }
}

function getStockTransactionTypes($chainID = null)
  {

        if($chainID == null){
          $transactionTypes["transactionType"] = array("STOCK_REQUISITION","STOCK_TRANSACTION","STOCK_CONSUMPTION","STOCK_YIELD_WASTAGE","STOCK_REQUEST","STOCK_PURCHASE","SALES_ORDER","STOCK_DISCREPANCY");
        }else{
          $stockLedgerSummaryTable = Constants::getChainInventoryTableName($chainID,"stockLedgerSummary");
          $transactionTypes = convertToArray(DB::table($stockLedgerSummaryTable)->select('transactionType')->distinct()->get());
        }
        
        $transactionCounts = count($transactionTypes);
        
        if($transactionCounts > 1){
            $transactionTypes = array_column($transactionTypes,'transactionType');
        }else{
            $transactionTypes = $transactionTypes["transactionType"];
        }
        return $transactionTypes;
  }

/**
 * The function "checkCRMEligibility" checks the eligibility of a customer for CRM
 * activities based on the chain ID, store ID, and customer ID provided.
 * 
 * @param chainID The chainID parameter represents the ID of a chain
 * @param storeID The storeID parameter is used to specify the ID of a particular store. It is an
 * optional parameter, so if it is not provided or set to null, the function will not check for
 * store-specific settings.
 * @param customerID The customerID parameter is used to specify the ID of a customer. It is an
 * optional parameter that can be used to check if the customer is eligible for CRM activities.
 * 
 * @return an array with three keys: "enablePointsEarning", "enablePointsRedemption", and "enableCRM".
 * The values of these keys indicate whether points earning, points redemption, and CRM are enabled or
 * disabled.
 */
function checkCRMEligibility($chainID, $storeID = null, $customerID = null, $storeSettings = [])
{
	$status = array();
	$status["enablePointsEarning"] = 1;
	$status["enablePointsRedemption"] = 1;
	$status["enableCRM"] = 1;
	if ($chainID <= 0) {
		$status["enablePointsEarning"] = 0;
		$status["enablePointsRedemption"] = 0;
		$status["enableCRM"] = 0;
		return $status;
	}
	if (isset($storeID) && $storeID > 0) {
		if (!empty($storeSettings)) {
			$status["enablePointsEarning"] = $storeSettings["enablePointsEarning"];
			$status["enablePointsRedemption"] = $storeSettings["enablePointsRedemption"];
		}else{
			// Check for store
			$listOfStoresTable = Constants::getGlobalTableName('listOfStores');
			$storeData = convertToArray(DB::table($listOfStoresTable)->where('storeID', $storeID)->select('enablePointsEarning', 'enablePointsRedemption')->get());
			$status["enablePointsEarning"] = $storeData[0]["enablePointsEarning"];
			$status["enablePointsRedemption"] = $storeData[0]["enablePointsRedemption"]; 
		}
	}

	if (isset($customerID) && $customerID > 0) {
		// Check for customer
		$customersTable = Constants::getChainTableName($chainID, "customers");
		$checkCustomer = convertToArray(DB::table($customersTable)->where("customerID", $customerID)->select('enableCRM')->get());
		$status["enableCRM"] = $checkCustomer[0]["enableCRM"];
		if ($checkCustomer[0]["enableCRM"] == 0) {
			$status["enablePointsEarning"] = 0;
			$status["enablePointsRedemption"] = 0;
		}
	}

	return $status;
}

/**
 * Retrieves all chain settings for a given chain ID
 * 
 * @param int|string $chainID The ID of the chain to get settings for
 * @param array|null $columns Array of column names to select, or null to select all columns
 * @return array An array containing the chain settings, or empty array if no settings found or error occurs
 * 
 * @throws \Throwable If there is an error executing the database query
 */
function getAllChainSettings($chainID, $columns = null) {
	if ($chainID == null || $chainID == "") {
		return [];
	}
	$chainSettingsTable = Constants::getGlobalTableName("chainSettings");
	$listOfChainsTable = Constants::getGlobalTableName("listOfChains");
	if ($columns == null || $columns == "" || empty($columns)) {
		$columns = "*";
	} else {
		$columns = implode(",", $columns);
	}
	try {
		$allSettings = convertToArray(DB::select(DB::raw("SELECT $columns FROM $listOfChainsTable AS A INNER JOIN $chainSettingsTable AS B ON A.chainID = B.chainRefID WHERE A.chainID = ?"), [$chainID]));    
	} catch (\Throwable $th) {		
    Log::info("Error in getting chain settings: " . $th->getMessage());
		return [];
	}
	if (empty($allSettings)) {
		return [];
	}
	return $allSettings[0];
}


/**
 * This function checks if a user has the required permissions in a chain.
 * It returns a map of user permissions where the permission name is the key and the value is a boolean indicating whether the user has the permission or not.
 *
 * @param int $userID The ID of the user.
 * @param int $chainID The ID of the chain.
 * @param array $permissions An array of required permissions. For example: ["SHOW_COST_PRICE", "ACCESS_DASHBOARD"]
 * @return array $userPermissionsMap A map of user permissions. For example: ["SHOW_COST_PRICE" => true, "ACCESS_DASHBOARD" => false]
 */
function checkUserPermission($userID, $chainID, $permissions) {

	// Check if the user ID, chain ID, or permissions array is invalid
	if ($userID <= 0 || $chainID <= 0 || empty($permissions)) {
		return []; // Return an empty array if any of the parameters is invalid
	}

	// Get the table name for user permissions mapping in the specified chain
	$chainUserPermissionTable = Constants::getChainTableName($chainID, "usersPermissionsMapping");

	// Get the user permissions for the specified user in the specified chain
	$userPermissions = convertToArray(DB::table($chainUserPermissionTable)->where("userID", $userID)->where("isActive", 1)->get());
	
	// Save all user permissions in an array
	$userPermissionsList = [];
	for ($i = 0; $i < count($userPermissions); $i++) {
		$userPermissionsList[] = $userPermissions[$i]["permissionID"];
	}

	// Get all the global permissions
	$globalPermissionsTable = Constants::getGlobalTableName("permissions");
	$globalPermissions = convertToArray(DB::table($globalPermissionsTable)->get());

	// Check if the user has all the required permissions
	$userPermissionsMap = [];
	for ($i = 0; $i < count($globalPermissions); $i++) {
		// Check if the global permission name is in the list of required permissions
		if (in_array($globalPermissions[$i]["permissionName"], $permissions)) {
			// Check if the global permission ID is in the list of user permissions
			if (in_array($globalPermissions[$i]["permissionID"], $userPermissionsList)) {
				$userPermissionsMap[$globalPermissions[$i]["permissionName"]] = true; // The user has the permission
			} else {
				$userPermissionsMap[$globalPermissions[$i]["permissionName"]] = false; // The user does not have the permission
			}
		}
	}

	return $userPermissionsMap;
}

/**
 * Create a new table schema based on an existing table
 *
 * @param string $oldTableName The name of the existing table
 * @param string $newTableName The name of the new table
 * @return string The new table schema
 */
function createTableSchema($oldTableName, $newTableName) {

	// Get the structure of the existing table
	$getOldStructure = convertToArray(DB::select(DB::raw("SHOW CREATE TABLE $oldTableName")));

	// Get the script for the existing table
	$oldScript = $getOldStructure[0]["Create Table"];

	// Replace the old table name with the new table name
	$newScript = str_replace($oldTableName, $newTableName, $oldScript);

	// Replace CREATE TABLE with CREATE TABLE IF NOT EXISTS
	$newScript = str_replace("CREATE TABLE", "CREATE TABLE IF NOT EXISTS", $newScript);

	// Remove AUTO_INCREMENT and DEFAULT_GENERATED_VALUE
	// because they are not needed when creating a new table
	// from an existing table
	$newScript = preg_replace("/AUTO_INCREMENT=[0-9]*/", "", $newScript);
	$newScript = preg_replace("/DEFAULT_GENERATED_VALUE=[0-9]*/", "", $newScript);

	// Remove ENGINE and COLLATE because they are not needed
	// when creating a new table from an existing table
	// The new table will have the same engine and collate
	// as the existing table
	$newScript = preg_replace("/ENGINE=InnoDB/", "", $newScript);
	// $newScript = preg_replace("/COLLATE=utf8mb4_0900_ai_ci/", "", $newScript); // maybe keep this?

	// add ; at last of the query if it's not there
	if (substr($newScript, -1) != ";") {
		$newScript .= ";";
	}

	return $newScript;
}

  
function fetchDepartmentEnableUserAccessControl($chainID,$dataArray)
{
      $status = [];
      if(!isset($chainID) || empty($chainID))
      {
          $status['status'] = false; 
          $status['message'] = 'ChainID is missing in the url'; 
          return $status;
      }
      $userID   = $dataArray['userID'];
      if(!isset($userID) || empty($userID))
      {
          $status['status'] = false; 
          $status['message'] = 'userID field is manadatory'; 
          return $status;
      } 
      $departmentsTable = Constants::getChainLevelProductsName($chainID,"departments");
      $departmentList   = convertToArray(DB::Table($departmentsTable)->select('departmentID')->where("enableUserAccessControl",0)->where('isActive',1)->get());
      $departmentUserConfigTable  =  Constants::getChainLevelProductsName($chainID,"departmentUserConfig");
      $departmentUserConfigList   =  convertToArray(DB::Table($departmentUserConfigTable)->select('departmentID')->where("userID",$userID)->where('isActive',1)->groupBy('departmentID')->get());
      if(!empty($departmentList))
      {
          $mergedArray                =  array_merge($departmentList, $departmentUserConfigList);
          $departmentIDs              =  array_column($mergedArray, 'departmentID');
          $uniqueDepartmentIDs        =  array_unique($departmentIDs);
          array_push($uniqueDepartmentIDs, -1); 
          $status['departmentIDs']    =  array_values($uniqueDepartmentIDs); 
      }
      else 
      {
          $mergedArray                =  array_merge($departmentUserConfigList);
          $departmentIDs              =  array_column($mergedArray, 'departmentID');
          $uniqueDepartmentIDs        =  array_unique($departmentIDs);
          array_push($uniqueDepartmentIDs, -1); 
          if(!empty($uniqueDepartmentIDs))
          {
              $status['departmentIDs']    =  array_values($uniqueDepartmentIDs); 
          }
          else 
          {
              $status['departmentIDs'] ='-1';
          }
      }
      return $status;
}
/**
 * Function to build a regular expression pattern based on allowed characters.
 *
 * @param string $allowedAlphabets Allowed alphabets. Use 'ALL' to allow all alphabets, or specify a string of allowed characters.
 * @param string $allowedNumbers Allowed numbers. Use 'ALL' to allow all digits, or specify a string of allowed characters.
 * @param string $allowedSymbols Allowed symbols. Use 'ALL' to allow all symbols, or specify a string of allowed characters.
 * 
 * @return string The constructed regular expression pattern.
 */
// Function to build regex
function buildRegex($allowedAlphabets, $allowedNumbers, $allowedSymbols)
{
	if ($allowedAlphabets == "" && $allowedNumbers == "" && $allowedSymbols == "") {
		return "/^$/"; // Allow all characters
	}
	$allowedPattern = [];

	// Allowed alphabets
	if ($allowedAlphabets == 'ALL') {
		$allowedPattern[] = 'A-Za-z'; // Allow all alphabets
	} elseif ($allowedAlphabets) {
		$allowedPattern[] = preg_quote($allowedAlphabets, '/'); // Escape special characters
	}

	// Allowed numbers
	if ($allowedNumbers == 'ALL') {
		$allowedPattern[] = '0-9'; // Allow all digits
	} elseif ($allowedNumbers) {
		$allowedPattern[] = preg_quote($allowedNumbers, '/'); // Escape special characters
	}

	// Allowed symbols
	if ($allowedSymbols == 'ALL') {
		// Allow all symbols, including punctuation, except underscores (which are alphanumeric in regex)
		$allowedPattern[] = '\W'; // \W allows all non-word characters (symbols, punctuation, etc.)
	} elseif ($allowedSymbols) {
		$allowedPattern[] = preg_quote($allowedSymbols, '/'); // Escape special characters
	}

	// Combine all allowed patterns
	$regex = "/^[" . implode('', $allowedPattern) . "]+$/"; // Ensure one or more allowed characters are matched

	return $regex;
}

function getProductTypes () {
	return ['FRAME','LENS','CONTACT_LENS','OTHER','COMBO','SERVICE','MEMBERSHIP','MODIFIER','RESERVATION','BUNDLE','VOUCHER', 'INVENTORY_ASSET'];
}



function moveKeyAfter(array $array, string $keyToMove, string $afterKey) {
  $rearrangedArray = [];
  
  foreach ($array as $item) {
      $newItem = [];
      
      foreach ($item as $key => $value) {
          $newItem[$key] = $value;  // Add key-value to new array
          
          if ($key === $afterKey && isset($item[$keyToMove])) {
              $newItem[$keyToMove] = $item[$keyToMove];  // Insert keyToMove after afterKey
              unset($item[$keyToMove]);  // Remove old key from item
          }
      }
      
      $rearrangedArray[] = $newItem;
  }
  
  return $rearrangedArray;
}
// Function to get sequence for stock operations for IRN for a store
// Checks if store has a custom order prefix > chain has a custom order prefix
// Checks if chain has a fiscalYearStart
// Check last transactionID for which IRN was generated
// Returns sequence number
function getSequenceForIRN($storeID, $chainID, $transactionType = "STOCK_TRANSFER", $sourceType = "STORE", $lock = true) {
	if ($storeID == null || $storeID == "" || $chainID == null || $chainID == "") {
		return null;
	}
	$listOfChainsTable = Constants::getGlobalTableName("listOfChains");
	$listOfStoresTable = Constants::getGlobalTableName("listOfStores");

	$chainData = getAllChainSettings($chainID, ["fiscalYearStart"]);
	if (empty($chainData)) {
		return null;
	}
	$storeData = convertToArray(DB::table($listOfStoresTable)->where("storeID", $storeID)->select("customStoreOrderPrefix")->get());
	if (empty($storeData)) {
		return null;
	}
	$orderPrefix = "";
	if ($storeData[0]["customStoreOrderPrefix"] != null) {
		$orderPrefix = $storeData[0]["customStoreOrderPrefix"];
	} else {
		$orderPrefix = $storeID;
	}

	$fiscalYearStart = $chainData["fiscalYearStart"];
	$dateCheck = "";
	if ($fiscalYearStart != null && $fiscalYearStart != "") {
		$fiscalStartDate = getFiscalStartDate($fiscalYearStart) . " 00:00:00";
		$dateCheck = " AND transactionTimeLocal >= '" . $fiscalStartDate . "'";
	}
  // if datecheck has 1970-01-01 00:00:00 ignore it
  if (preg_match("/1970-01-01 00:00:00/", $dateCheck)) {
    $dateCheck = "";
  }


	$sequenceNumber = 1;

	// Get Prefix from stock transaction type as well
	$orderPrefix .= "S";
	$parts = explode("_", $transactionType);
	// Skip the first part (STOCK) and use first character of each remaining part
	for($i = 1; $i < count($parts); $i++) {
		$orderPrefix .= substr($parts[$i], 0, 1);
	}
	
	// If no characters were added (e.g., transaction type doesn't have underscores)
	if(strlen($orderPrefix) <= 1) {
		// Default to "SO" for Other
		$orderPrefix = "SO";
	}

	// get last transactionID 
	$stockTransactionSummaryTable = Constants::getChainInventoryTableName($chainID, "stockTransactionSummary");
	$lastTransactionID = [];
	try {
		$lastTransactionID = DB::transaction(function () use ($lock, $stockTransactionSummaryTable, $storeID, $sourceType, $dateCheck, $transactionType) {
			$lockForUpdate = "";
			if ($lock) {
				$lockForUpdate = " FOR UPDATE";
			}
			
			$query = "SELECT transactionID, eInvoiceNumber FROM $stockTransactionSummaryTable 
					WHERE sourceID = ?
					AND sourceType = ?
					$dateCheck 
					AND transactionType = ?
					ORDER BY transactionID DESC LIMIT 1 $lockForUpdate";
					
			return convertToArray(DB::select(DB::raw($query), [$storeID, $sourceType, $transactionType]));
		});
	} catch (\Exception $e) {
		// Log the error
		// Log::error("Error fetching last transaction ID: " . $e->getMessage());
		$lastTransactionID = [];
	}

	if (!empty($lastTransactionID) && count($lastTransactionID) > 0) {
		// get last transactionID
		$lastTransactionData = $lastTransactionID[0];
		if (!empty($lastTransactionData["eInvoiceNumber"])) {
			// eInvoiceNumber > PREFIX + "-" + SEQUENCE_NUMBER
			$parts = explode("-", $lastTransactionData["eInvoiceNumber"]);
			if (count($parts) > 1) {
				$sequenceNumber = intval($parts[count($parts) - 1]) + 1;
			}
		}
	}

	$result = $orderPrefix . "-" . $sequenceNumber;
	return $result;
}

function getFiscalStartDate($fiscalYearStart) {
	// Based on the date provided as MMDD format (e.g. 0401 for April 1st)
	// Returns the fiscal year start date in Y-m-d format

	// Extract month and day from input
	$fiscalMonth = substr($fiscalYearStart, 0, 2);
	$fiscalDay = substr($fiscalYearStart, 2, 2);

	// Get current date information
	$currentYear = date("Y");
	$currentMonth = date("m");
	$currentDay = date("d");

	// Determine if we're in current or previous fiscal year
	if (
		$currentMonth < $fiscalMonth || 
		($currentMonth == $fiscalMonth && $currentDay < $fiscalDay)
	) {
		// We haven't reached the fiscal start date in current year yet
		// So we're in the previous fiscal year
		$fiscalYear = $currentYear - 1;
	} else {
		// We have passed the fiscal start date in current year
		// So we're in the current fiscal year
		$fiscalYear = $currentYear;
	}

	// Return the fiscal year start date
	return date("Y-m-d", strtotime($fiscalYear . "-" . $fiscalMonth . "-" . $fiscalDay));
}


function allowNewPermission()
{

	$none_permissions = [
		"ENABLE_EXCHANGE",
		"ALLOW_REFUND_WITHOUT_INVOICE",
		"ENABLE_REPRINT_KOT",
		"ACCESS_BATCH_REOPEN",
		"ALLOW_BATCH_CLOSE_FOR_OTHER_USER",
	];
	$manage_catalogue_permissions = [
		"VIEW_PRODUCT",
		"MANAGE_PRODUCT",
		"VIEW_PRODUCT_GROUP",
		"MANAGE_PRODUCT_GROUP",
		"VIEW_STORE_PRODUCT",
		"MANAGE_STORE_PRODUCT",
		"VIEW_WAREHOUSE_PRODUCT",
		"MANAGE_WAREHOUSE_PRODUCT",
		"VIEW_COMBO",
		"MANAGE_COMBO",
		"VIEW_MEMBERSHIP",
		"MANAGE_MEMBERSHIP",
		"VIEW_VOUCHER",
		"MANAGE_VOUCHER",
		"VIEW_GIFT_VOUCHER",
		"MANAGE_GIFT_VOUCHER",
		"MANAGE_INVENTORY_ASSET",
		"MANAGE_COUNTER",
		"VIEW_PRODUCT_GROUP",
		"MANAGE_PRODUCT_GROUP",
		"VIEW_PRODUCT",
		"MANAGE_PRODUCT",
		"VIEW_SUB_CATEGORY",
		"MANAGE_SUB_CATEGORY",
		"VIEW_CATEGORY",
		"MANAGE_CATEGORY",
		"VIEW_BRAND",
		"MANAGE_BRAND",
		"VIEW_COMBO",
		"MANAGE_COMBO",
		"VIEW_VOUCHER",
		"MANAGE_VOUCHER",
		"VIEW_GIFT_VOUCHER_TRACKER",
		"VIEW_INCOME_HEAD",
		"MANAGE_INCOME_HEAD",
		"VIEW_MEASUREMENT_UNITS",
		"MANAGE_MEASUREMENT_UNITS",
		"VIEW_MEMBERSHIP",
		"MANAGE_MEMBERSHIP",
		"VIEW_STORE_PRODUCT",
		"MANAGE_STORE_PRODUCT",
		"MANAGE_TABLE",
		"MANAGE_FLOOR",
		"MANAGER_ROOMS",
		"MANAGER_TABLE",
		"VIEW_MODIFIER",
		"MANAGE_MODIFIER",
		"VIEW_MODIFIER_GROUP",
		"MANAGE_MODIFIER_GROUP",
		"MANAGE_MANUFACTURER",
		"MANAGE_SERVICE_GROUP",
		"VIEW_SERVICE",
		"MANAGE_SERVICE",
		"VIEW_MANUFACTURER",
		"VIEW_SERVICE_GROUP",
		"VIEW_PRODUCT_BUNDLE",
		"MANAGE_PRODUCT_BUNDLE",
		"MANAGE_ESTORE_CREDITS",
		"VIEW_APPOINTMENT",
		"MANAGE_PRICELIST",
		"VIEW_GIFT_VOUCHER_TRACKER",
		"MANAGE_SOURCE_TAX",
		"VIEW_SOURCE_TAX",
		"MANAGE_FLOOR",
		"MANAGE_ROOMS",
		"MANAGE_PRICELIST_APPROVAL", 
		"VIEW_CATEGORY",
		"VIEW_DEPARTMENT",
		"MANAGE_CATEGORY",
		"VIEW_SUB_CATEGORY",
		"MANAGE_SUB_CATEGORY",
		"VIEW_BRAND",
		"MANAGE_BRAND",
		"VIEW_INCOME_HEAD",
		"MANAGE_INCOME_HEAD",
		"VIEW_SERVICE_DEPARTMENT",
		"MANAGE_SERVICE_DEPARTMENT",
		"VIEW_PRICELIST_FILTER",
		"VIEW_PRICELIST",
		"VIEW_PRICELIST_FILTER_GROUP",
		"MANAGE_PRICELIST_FILTER_GROUP",
	];
	$manage_chain_info_permissions = [
		"VIEW_CHAIN_INFO",
		"MANAGE_STORE_INFO",
		"VIEW_STORE_INFO",
		"MANAGE_COUNTER",
		"VIEW_REGION",
		"MANAGE_REGION",
		"VIEW_REMARK",
		"MANAGE_REMARK",
		"ENABLE_CUSTOMIZE_RECEIPT",
		"VIEW_APPLICATION_DEVICE_SETTINGS",
		"MANAGE_APPLICATION_DEVICE_SETTINGS",
		"VIEW_ROLES",
		"MANAGE_ROLES",
		"VIEW_WAREHOUSE",
		"MANAGE_WAREHOUSES",
		"VIEW_CUSTOM_ATTRIBUTE",
		"MANAGE_CUSTOM_ATTRIBUTE",
		"VIEW_SYSTEM_ATTRIBUTE",
		"MANAGE_SYSTEM_ATTRIBUTE",
		"MANAGE_SALES_TARGET",
		"MANAGE_SALES_ASSESMENT",
		"VIEW_CHANNEL",
		"MANAGE_CHANNEL",
		"VIEW_SMS_CREDITS",
		"VIEW_WHATSAPP_CREDITS",
		"MANAGE_LICENSES",
		"MANAGE_FLOOR",
		"MANAGE_ROOMS",
		"MANAGE_PRICELIST_APPROVAL",
		"MANAGE_DEPARTMENT",
		"MANAGE_PRICELIST_FILTER",
		"VIEW_CATEGORY",
		"VIEW_DEPARTMENT",
		"MANAGE_CATEGORY",
		"VIEW_SUB_CATEGORY",
		"MANAGE_SUB_CATEGORY",
		"VIEW_BRAND",
		"MANAGE_BRAND",
		"VIEW_INCOME_HEAD",
		"MANAGE_INCOME_HEAD",
		"VIEW_SERVICE_DEPARTMENT",
		"MANAGE_SERVICE_DEPARTMENT",
		"VIEW_PRICELIST_FILTER",
		"VIEW_PRICELIST",
		"VIEW_PRICELIST_FILTER_GROUP",
		"MANAGE_PRICELIST_FILTER_GROUP",
		"VIEW_CHAIN_PAYMENT_SETTING",
		"MANAGE_CHAIN_PAYMENT_SETTING",
		"MANAGE_STORE_PAYMENT_SETTING",
		"VIEW_PAYMENT_TYPES",
		"MANAGE_WEBHOOK",
		"VIEW_MEASUREMENT_UNITS",
		"MANAGE_MEASUREMENT_UNITS",
		"VIEW_BILLME_SETTINGS",
		"MANAGE_BILLME_SETTINGS",
		"VIEW_DEVICE_DATA_SYNC",
		"MANAGE_DEVICE_DATA_SYNC",
		"MANAGE_TAX_FILING",
		"MANAGE_LICENSE_PAYMENT",
		"MANAGE_LICENSE_EXTENSION",
		"VIEW_SERVER_ERROR",
		"MANAGE_LICENSE_TRANSFER",
		"MANAGE_LICENSE_RATE",
		"ACCESS_QB_APP",
		"MANAGE_SMS_CREDITS",
		"MANAGE_WHATSAPP_CREDITS",
		"VIEW_ESTORE_CREDITS",
		"TEST_PERMISSION",
		"TEST_PERMISSIONS",
		"APPROVE_PROMOTION",
		"VIEW_CHAIN_LIST",
		"VIEW_LIST_CHAIN",
		"VIEW_DEVICES",
		"MANAGE_DEPARTMENT",
		"MANAGE_PRICELIST_FILTER",
  
	];
	$manage_users = [
		"VIEW_USERS",
		"EDIT_USER",
		"EDIT_USER_PERMISSION",
		"DELETE_USER",
		"VIEW_STAFF_DEPARTMENT",
		"MANAGE_STAFF_DEPARTMENT",
		"VIEW_EMPLOYEE_MEMBERSHIP",
		"MANAGE_EMPLOYEE_MEMBERSHIP",
		"ISSUE_EMPLOYEE_WALLET",
		"RECHARGE_EMPLOYEE_WALLET",
		"REFUND_EMPLOYEE_WALLET",
		"BLOCK_EMPLOYEE_WALLET",
		"TRANSFER_EMPLOYEE_WALLET",
		"UNBLOCK_EMPLOYEE_WALLET",
		"MANAGE_EMPLOYEE_WALLET",
		"VIEW_USER_PERMISSION",
		"ALLOW_EMPLOYEE_TERMINATION",
		"ENABLE_RFID_LOGIN",
	];
	$view_reports_permissions = [
		"VIEW_VEHICLE_TRACKER",
		"VIEW_SALES_TARGET",
		"VIEW_NET_PROFIT",
		"VIEW_KOT_VOID_DETAIL",
		"VIEW_PROFORMA_INVOICE",
		"VIEW_DAILY_SALES_REPORT",
		"VIEW_SALES_INVOICE",
		"VIEW_DAILY_PAYMENT_BREAKUP",
		"VIEW_SALES_INVOICE_DISCOUNT",
		"VIEW_DAILY_ORDER_COUNT",
		"VIEW_SALES_INVOICE_PAYMENT",
		"VIEW_SALES_INVOICE_TAX",
		"VIEW_REFUND_INVOICE",
		"VIEW_PURCHASE_ORDER",
		"VIEW_STORE_WISE_SALES",
		"VIEW_EMPLOYEE_WISE_SALES",
		"VIEW_EMPLOYEE_WISE_PRODUCT_SALES",
		"VIEW_STORE_WISE_PRODUCT_SALES",
		"VIEW_REGION_WISE_SALES",
		"VIEW_PRODUCT_WISE_SALES",
		"VIEW_CATEGORY_WISE_SALES",
		"VIEW_BRAND_WISE_SALES",
		"VIEW_INCOME_HEAD_WISE_SALES",
		"VIEW_DEVICE_WISE_SALES",
		"VIEW_CUSTOMER_WISE_SALES",
		"VIEW_REAL_TIME_PULSE",
		"VIEW_ENTITY_WISE_SALES",
		"VIEW_LOCATION_WISE_SALES",
		"VIEW_COUNTER_WISE_SALES",
		"VIEW_PROFIT_MARGIN",
		"VIEW_VENDOR_TAX_INPUT",
		"VIEW_VENDOR_PURCHASE",
		"VIEW_EWAY_BILL",
		"VIEW_PURCHASE_RETURN",
		"VIEW_CREDIT_NOTE_HISTORY",
		"VIEW_MESSAGE_HISTORY_REPORT",
		"VIEW_BUNDLE_WISE_SALES",
		"VIEW_INACTIVE_CUSTOMER",
		"VIEW_CUSTOMER_KHATA_REPORT",
		"VIEW_ATTENDANCE_REPORT",
		"VIEW_POS_DAY_INFO",
		"VIEW_CASHIER_SUMMARY",
		"VIEW_MEMBERSHIP_TRACKER",
		"VIEW_ONLINE_ORDER",
		"VIEW_ONLINE_ORDER_PAYMENT",
		"VIEW_HSN_WISE_TAX_BREAKUP",
		"VIEW_DISCOUNT_EXPENSE",
		"VIEW_DISCOUNTED_PRODUCT",
		"VIEW_SUB_CATEGORY_WISE_SALES",
		"VIEW_BATCH_WISE_SALES",
		"VIEW_STORE_WISE_HOURLY_SALES",
		"VIEW_FINE_CHALLAN_REPORT",
		"VIEW_CASH_MANAGEMENT_HISTORY",
		"VIEW_API_CALL_LOG",
		"VIEW_CURRENCY_VALIDATION",
		"VIEW_TENDER_WISE_COLLECTION",
		"VIEW_RESERVATION_TRACKER",
		"VIEW_PRODUCT_LOG",
		"VIEW_DEVICE_LOG",
		"VIEW_UNICOMMERCE_API_LOGS",
		"VIEW_UNICOMMERCE_SALE_ORDERS",
		"VIEW_NAVISION",
		"VIEW_UNICOMMERCE_RETURN_ORDERS",
		"VIEW_MLOYAL_LEDGER",
		"VIEW_MLOYAL_API_LOGS",
		"ENABLE_BILLING_ACCESS",
		"VIEW_EASYECOM_ORDER_TRACKER",
		"VIEW_EASYECOM_GLOBAL_LOGS",
		"VIEW_EASYECOM_SETTINGS",
		"VIEW_BILLME_API_LOGS",
		"VIEW_RESERVATION_SLOT_TRACKER",
		"VIEW_CUSTOMER_LOYALTY",
		"VIEW_STORE_LOYALTY",
		"VIEW_SYSTEM_CHANGE_LOG",
		"GENERATE_REPORTS",
		"VIEW_RETURN_ORDER",
		"MANAGE_RETURN_ORDER",
		"APPROVE_RETURN_ORDER",
		"VIEW_MISC_SALES_INVOICE",
		"VIEW_DEVICE_LOG",
		"VIEW_ERP_REVENUE_DATA",
		"APPROVE_ERP_REVENUE_DATA",
		"VIEW_ERP_CUSTOMER_DATA",
		"APPROVE_ERP_CUSTOMER_DATA",
		"VIEW_ERP_STOCK_DATA",
		"UPLOAD_ERP_STOCK_DATA",
		"MANAGE_CASH_VARIANCE_SETTLEMENT",
		"VIEW_DEPARTMENT_WISE_SALES",
		"VIEW_MANUFACTURER_WISE_SALES",
		"VIEW_ACTIVITY_LOG",
		"VIEW_VEHICLE_TRACKER",
		"VIEW_YEARLY_REPORT",
		"VIEW_UNFULFILLED_STOCK_TRANSFER",
		"VIEW_PRODUCT_WISE_ONLINE_SALES",
		"VIEW_SECURITY_DEPOSIT",
		"VIEW_CASH_FLOW_SUMMARY",
		"VIEW_TENDER_DECLARATION",
		"VIEW_TAX_TRACKER",
		"VIEW_ELR_SALES",
		"VIEW_EMPLOYEE_WISE_SALES",
		"VIEW_EMPLOYEE_WISE_PRODUCT_SALES",
		"VIEW_DISCOUNT_PERFORMANCE_REPORT"
	];
	$manage_inventory_permissions = [
		"VIEW_STOCK_VALIDATION",
		"ENABLE_STOCK_VALIDATION",
		"VIEW_STOCK_TRANSFER",
		"VIEW_INVENTORY_BATCHES",
		"VIEW_VENDOR_INVOICE",
		"ENABLE_VENDOR_MANAGEMENT",
		"VIEW_PURCHASE_ORDER",
		"MANAGE_PURCHASE_ORDER",
		"MANAGE_STOCK_REQUISITION",
		"ENABLE_STOCK_TRANSFER",
		"ENABLE_STOCK_IN",
		"ENABLE_STOCK_OUT",
		"MANAGE_INVENTORY_BATCHES",
		"VIEW_STOCK_IN",
		"VIEW_STOCK_WASTAGE",
		"VIEW_STOCK_REQUISITION",
		"MANAGE_STOCK_APPROVAL",
		"VIEW_STOCK_TRANSFER_RULE",
		"MANAGE_STOCK_TRANSFER_RULE",
		"VIEW_STOCK_LEVEL",
		"VIEW_STORE_WISE_STOCK_LEVEL",
		"VIEW_PRODUCT_GROUP_STOCK_LEVEL",
		"VIEW_STOCK_AUDIT",
		"VIEW_STOCK_OPERATIONS",
		"VIEW_STOCK_REQUISITION",
		"VIEW_STOCK_AUDIT_ANALYSIS",
		"VIEW_STOCK_MOVEMENT",
		"VIEW_UNFULFILLED_STOCK_REQUEST",
		"VIEW_PRODUCT_AGEING",
		"VIEW_LOW_STOCK_PRODUCT",
		"VIEW_STOCK_LEDGER",
		"VIEW_INVOICE_SETTLEMENT",
		"VIEW_PROFORMA_INVOICE",
		"MANAGE_PROFORMA_INVOICE",
		"ENABLE_DELIVERY_CHALLAN",
		"MANAGE_DELIVERY_CHALLAN",
		"VIEW_UNFULFILLED_STOCK_TRANSFER",
		"VIEW_VENDOR_BILL",
		"MANAGE_VENDOR_BILL",
		"MANAGE_VENDOR_PAYMENTS"  
	];
	$manage_charges_permissions = [
		"VIEW_TAX",
		"MANAGE_TAX",
		"VIEW_CHARGES",
	];
	$manage_discounts_permissions = [
		"VIEW_DISCOUNT",
		"VIEW_PRODUCT_DISCOUNT_REPORT",
		"VIEW_COUPON_REDEMPTION",
	];
	$manage_customers_permissions = [
		"MANAGE_LOYALTY",
		"VIEW_CREDIT_NOTE",
		"MANAGE_CREDIT_NOTE_REFUND",
		"VIEW_CREDIT_NOTE_REDEMPTION_CONFIG",
		"MANAGE_CREDIT_NOTE_REDEMPTION_CONFIG",
		"VIEW_CREDIT_NOTE_REFUND_CONFIG",
		"MANAGE_CREDIT_NOTE_REFUND_CONFIG"  ,
		"VIEW_CUSTOMER_LEDGER",
	];
	$access_dashboard_permissions = [
		"VIEW_WEBHOOK",
		"VIEW_API_TRACKER",
		"ENABLE_BILLING_ACCESS",
		"VIEW_INTEGRATIONS_DASHBOARD",
		"VIEW_UNICOMMERCE_CATALOG",
		"VIEW_UNICOMMERCE_SETTINGS",
		"VIEW_EASYECOM_CATALOG",
		"MANAGE_UNICOMMERCE_CATALOG",
		"MANAGE_UNICOMMERCE_SETTINGS",
		"MANAGE_EASYECOM_CATALOG",
		"MANAGE_EASYECOM_SETTINGS",
		"VIEW_EASYREWARDS_API_LOGS",
		"VIEW_EASYREWARDS_CUSTOMER_LEDGER",
		"VIEW_EASYREWARDS_WIZARD",
		"VIEW_VAYANA_SETTINGS",
		"MANAGE_VAYANA_SETTINGS",
		"VIEW_VAYANA_API_LOGS",
		"VIEW_WONDERLA_API_LOGS",
		"VIEW_WONDERLA_ERP_APPROVAL",
		"MANAGE_WONDERLA_ERP_APPROVAL",
		"VIEW_WONDERLA_REVENUE_SUMMARY",
		"VIEW_WONDERLA_REGION_CONFIG",
		"MANAGE_WONDERLA_REGION_CONFIG",
		"VIEW_WONDERLA_STORE_CONFIG",
		"MANAGE_WONDERLA_STORE_CONFIG",
	];
	$wallet_permissions = [
		"ISSUE_WALLET",
		"RECHARGE_WALLET",
		"REFUND_WALLET",
		"BLOCK_WALLET",
		"TRANSFER_WALLET",
		"UNBLOCK_WALLET",
		"RESET_WALLET",
		"VIEW_WALLET_TYPE",
		"MANAGE_WALLET_TYPE",
		"VIEW_WALLET_BALANCE_REPORT",
		"VIEW_WALLET_MAPPING_BALANCE",
		"VIEW_WALLET_ACTIVITY_TRACKER",
		"VIEW_WALLET_REQUEST_TRACKER",
		"VIEW_WALLET_DAILY_PAYMENT_BREAKUP",
		"VIEW_COUNTER_WISE_WALLET_TRACKER",
		"ACCESS_MONEY_TRANSFER"
	];
	$salesorder_permissions = [
		"GENERATE_SALES_ORDER",
		"INVOICE_SALES_ORDER",
		"VIEW_SALES_ORDER",
		"GENERATE_BULK_SALES_ORDER",
		"VIEW_SALES_QUOTATION",
		"GENERATE_SALES_QUOTATION",
		"APPROVE_SALES_QUOTATION",
		"ENABLE_REFUND_ORDER",
		"ALLOW_CHANGE_PAYMENT",
		"MANAGE_SALES_ORDER_WRITE_OFF",
		"VIEW_STOCK_FULFILLMENT",
		"MANAGE_STOCK_FULFILLMENT",
    "VIEW_SALES_ORDER_PRODUCT_TRACKER",
    "VIEW_ENTITY_SALES_ANALYSIS",
    "VIEW_BATCH_TRACKER_REPORT",
    "CUSTOMER_BALANCE_TRANSFER",
    "VIEW_DSC_REPORT",
    "MANAGE_PARTY_TYPE",
		"MANAGE_SALES_CONTROL_CONFIGURATION",
		"MANAGE_WALLET_MANAGEMENT",
		"MANAGE_CUSTOMER_GROUP",
		"MANAGE_EMPLOYMENT_STATUS",
		"MANAGE_BANK_ACCOUNT",
		"MANAGE_PARTY_BALANCE_TRANSFER",
    "VIEW_PARTY_MAPPING",
		"VIEW_PARTY_PAYMENTS",
		"MANAGE_PARTY_PAYMENTS",
		"MANAGE_PARTY_MAPPING",
		"VIEW_PARTY_BALANCE_TRANSFER",
		"VIEW_PARTY_TYPE",
		"MANAGE_ADVANCED_CREDIT_LIMIT",
		"MANAGE_ADVANCED_CREDIT_LIMIT_APPROVAL",
    "MANAGE_SO_FULFILLMENT_EXTENSION",
    "VIEW_LOCATION_WISE_SALES_SO",
		"VIEW_ENTITY_WISE_SALES_SO",
		"VIEW_COUNTER_WISE_DAILY_PAYMENT_BREAKUP_SO",
		"VIEW_STORE_WISE_DAILY_PAYMENT_BREAKUP_SO",
    "MANAGE_CREDIT_LIMIT",
		"MANAGE_CREDITLIMIT_APPROVAL",
    "VIEW_CREDIT_SETTLEMENT",
    "MANAGE_CREDIT_SETTLEMENT",
		"VIEW_CUSTOMER_GROUP",
		"MANAGE_CUSTOMER_ADVANCE_PAYMENT",
		"VIEW_PARTY_MASTER",
		"MANAGE_PARTY_MASTER",
	];

	// remove duplicate permissions and null values after array_unique
	$none_permissions = array_filter($none_permissions, function($value) {
		return $value !== null;
	});
	$manage_catalogue_permissions = array_filter($manage_catalogue_permissions, function($value) {
		return $value !== null;
	});
	$manage_chain_info_permissions = array_filter($manage_chain_info_permissions, function($value) {
		return $value !== null;
	});
	$manage_users = array_filter($manage_users, function($value) {
		return $value !== null;
	});
	$view_reports_permissions = array_filter($view_reports_permissions, function($value) {
		return $value !== null;
	});
	$manage_inventory_permissions = array_filter($manage_inventory_permissions, function($value) {
		return $value !== null;
	});
	$manage_charges_permissions = array_filter($manage_charges_permissions, function($value) {
		return $value !== null;
	});
	$manage_discounts_permissions = array_filter($manage_discounts_permissions, function($value) {
		return $value !== null;
	});
	$manage_customers_permissions = array_filter($manage_customers_permissions, function($value) {
		return $value !== null;
	});
	$access_dashboard_permissions = array_filter($access_dashboard_permissions, function($value) {
		return $value !== null;
	});
	// $wallet_permissions = array_filter($wallet_permissions, function($value) {
	// 	return $value !== null;
	// });
	// $salesorder_permissions = array_filter($salesorder_permissions, function($value) {
	// 	return $value !== null;
	// });

	$permissions = [
		'none' => $none_permissions,
		'MANAGE_CATALOGUE' => $manage_catalogue_permissions,
		'MANAGE_CHAIN_INFO' => $manage_chain_info_permissions,
		'MANAGE_USERS' => $manage_users,
		'VIEW_REPORTS' => $view_reports_permissions,
		'MANAGE_INVENTORY' => $manage_inventory_permissions,
		'MANAGE_CHARGES' => $manage_charges_permissions,
		'MANAGE_DISCOUNTS' => $manage_discounts_permissions,
		'MANAGE_CUSTOMERS' => $manage_customers_permissions,
		'ACCESS_DASHBOARD' => $access_dashboard_permissions
	];

	return $permissions;
}
