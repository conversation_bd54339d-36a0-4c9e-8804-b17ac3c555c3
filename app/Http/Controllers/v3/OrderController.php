<?php

namespace App\Http\Controllers\v3;

use App\Http\Controllers\Controller;
use App\Helpers\OrderValidatorNonDB;
use App\Helpers\OrderValidatorDB;
use App\Helpers\ResponseFormatter;
use App\Services\IdempotencyManager;
use App\Services\SettingsManager;
use App\Services\DatabaseManager;
use App\Validators\Order\ProductValidator;
use App\Validators\Order\ProformaInvoiceValidator;
use App\Validators\Order\SalesOrderValidator;
use App\Validators\Integration\UnicommerceValidator;
use App\Validators\Integration\EasyEcomValidator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\v3\Order;
use App\Services\JobDispatcher;
use App\TableRegistry;
use Illuminate\Support\Facades\DB;

class OrderController extends Controller
{
    /**
     * Database manager instance
     */
    private $dbManager;

    /**
     * Settings manager instance
     */
    private $settingsManager;

    /**
     * Idempotency manager instance
     */
    private $idempotencyManager;

    /**
     * Product validator instance
     */
    private $productValidator;

    /**
     * Proforma Invoice validator instance
     */
    private $proformaInvoiceValidator;

    /**
     * Sales Order validator instance
     */
    private $salesOrderValidator;

    /**
     * Unicommerce validator instance
     */
    private $unicommerceValidator;

    /**
     * EasyEcom validator instance
     */
    private $easyEcomValidator;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        IdempotencyManager $idempotencyManager,
        SettingsManager $settingsManager,
        DatabaseManager $dbManager
    ) {
        // Checks if the token is valid
        $this->middleware('oauth');

        // Enable query logging for performance tracking
        DB::enableQueryLog();

        // Initialize services
        $this->dbManager = $dbManager ?? new DatabaseManager();
        $this->settingsManager = $settingsManager ?? new SettingsManager($this->dbManager);
        $this->idempotencyManager = $idempotencyManager ?? new IdempotencyManager($this->dbManager);

        // Initialize validators
        $this->productValidator = new ProductValidator($this->dbManager);
        $this->proformaInvoiceValidator = new ProformaInvoiceValidator($this->dbManager);
        $this->salesOrderValidator = new SalesOrderValidator($this->dbManager);
        $this->unicommerceValidator = new UnicommerceValidator($this->dbManager);
        $this->easyEcomValidator = new EasyEcomValidator($this->dbManager);
    }

    /**
     * Process an order (POST /v3/merchant/{storeID}/order)
     *
     * @param Request $request The HTTP request
     * @param int $storeID The store ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function postOrder(Request $request, $storeID)
        {
        $uniqId = uniqid();
        // Log the request
        Log::info("v3/order API request received for storeID: $storeID " . $uniqId);

        // Get request content
        $result = $request->getContent();

        // Validate JSON
        if (isJson($result) == FALSE) {
            return response()->json(
                ResponseFormatter::error("Invalid JSON data", "INVALID_JSON"),
                422,
                [],
                JSON_NUMERIC_CHECK
            );
        }

        // Parse JSON
        $data = convertToArray(json_decode($result));

        // Check for empty data
        if (empty($data)) {
            return response()->json(
                ResponseFormatter::error("Empty request data", "EMPTY_REQUEST"),
                422,
                [],
                JSON_NUMERIC_CHECK
            );
        }

        // Add storeID to data
        $data['storeID'] = $storeID;
        // use chainID from request if available else use from header
        $chainID = $data['chainID'] ?? $request->header('chainID');
        // add apkVersion to data
        $data['apkVersion'] = $request->header("apkVersion") ?? null;

        // Use orderID as idempotency key
        $idempotencyKey = $data['orderID'];
        if ($idempotencyKey) {
            $data['idempotencyKey'] = $idempotencyKey;

            // Process idempotency key using the IdempotencyManager
            $idempotencyResult = $this->idempotencyManager->process($idempotencyKey, $chainID, [
                'grossBill' => $data['grossBill'] ?? 0
            ]);

            // If this is not a new request, return the cached response
            if (!$idempotencyResult['new']) {
                if (isset($idempotencyResult['cached']) && $idempotencyResult['cached']) {
                    // Log::info("Idempotent request detected: $idempotencyKey");

                    return response()->json(
                        $idempotencyResult['response'],
                        $idempotencyResult['statusCode'],
                        [],
                        JSON_NUMERIC_CHECK
                    );
                } else if (isset($idempotencyResult['code'])) {
                    // Need to process if retryAfter is not set
                    if (isset($idempotencyResult['retryAfter'])) {
                        // Request is still processing
                        $response = ResponseFormatter::error(
                            $idempotencyResult['message'],
                            "IDEMPOTENCY_ERROR"
                        );

                        $headers = [];
                        if (isset($idempotencyResult['retryAfter'])) {
                            $headers['Retry-After'] = $idempotencyResult['retryAfter'];
                        }

                        return response()->json(
                            $response,
                            $idempotencyResult['code'],
                            $headers,
                            JSON_NUMERIC_CHECK
                        );
                    }
                }
            }

            // If we get here, it's a new request and we can proceed
        }

        // Perform basic non-DB validations || Schema validation
        $basicParamsValidation = OrderValidatorNonDB::validateAll($data);
        if ($basicParamsValidation['status'] === false) {
            $response = ResponseFormatter::validationError($basicParamsValidation['message']);

            // Update idempotency record if exists
            if ($idempotencyKey) {
                $this->idempotencyManager->updateResponse($idempotencyKey, $chainID, 422, $response);
            }

            return response()->json($response, 422, [], JSON_NUMERIC_CHECK);
        }

        // Get chain and store settings for validation using the SettingsManager
        $settingColumns = [
            "enableCrossStoreCreditNoteRedemption",
            "enablePartialRedemptionOfCreditNote",
            "enableModifier",
            "recordChainLevelSalesInvoice",
            "enableTokenGeneration",
            "creditNoteExpiryDays",
            "enableCreditNoteExpiry",
            "merchantIntegrationModule",
            "enableDMS",
            "enableChannelManagement",
            "enableTenderDeclarationOnInvoice",
            "mandateTenderDeclarationOnInvoice",
            "enableStockLedger",
            "enableQBWalletManagement",
            "generateInvoiceNumberFromServer",
            "enableOnlineBillingOnly",
            "industry",
            "enableIRN",
            "enableGST",
            "recordCustomerInfoOnOrder",
            "timezone",
            "enableChainOrderAccounting",
            "enableEcomReservation",
            "enableProductBundle",
            "enableCurrencyConversion",
            "brandName",
            "automateProductGroupCreation",
            "enableRRAIntegration",
            "enableTaxFiling",
            "enableSourceTax",
            "enableSalesControl",
            "enableGeneralLedger",
            "enableRMSIntegration",
            "enablePartyMaster",
            "chainGUID",
            "enableSalesOrder",
            "enableAccounting",
            "mandateTaxApplication",
            "autoFixOrderID",
            "enableBillMeIntegration",
            "customOrderPrefix",
            "fiscalYearStart",
            "isReviewerEnabled",
            "GsWAUserID",
            "GsWAPassword",
            "enableGiftVoucher",
            "enableDLFGVIntegration",
            "useDealerPriceAsAverage",
            "enableOrderReturnWithoutInvoice",
            "enableSourceDealerPrice",
            "enableAdvancedPriceList",
            "enableEasyEcomIntegration",
            "enableUnicommerceIntegration",
            "minAPKVersion",
            "chainMunicipalType",
            "parkingModule",
            "enableStockAlert",
            "enableCounterManagement",
            "enableInventoryAssetManagement",
            "loyaltyID",
            "purchaseAmount",
            "minimumOrderValueToEarnPoints",
            "pointsExpiryDays"
        ];

        $storeColumns = [
            "enableMunicipalIntegration",
            "municipalType",
            "storeGUID",
            "customStoreOrderPrefix",
            "NCCustomStoreOrderPrefix",
            "isInventoryManaged",
            "enableStoreStockAlert"
        ];

        // Get both chain and store settings in a single call
        $settings = $this->settingsManager->getChainAndStoreSettings(
            $chainID,
            $storeID,
            $settingColumns,
            $storeColumns
        );

        $chainSettings = $settings['chain'];
        $storeDetail = $settings['store'];


        if ($chainSettings['enableOnlineBillingOnly'] == 1) {
            // Validate proforma invoice using ProformaInvoiceValidator
            $piValidationResult = $this->proformaInvoiceValidator->validateAll(
                $data,
                $chainSettings,
                $chainID
            );

            if (!$piValidationResult->isValid()) {
                $error = $piValidationResult->getFirstError();
                $response = ResponseFormatter::error(
                    $error['message'],
                    $error['code'],
                    $error['context'] ?? null
                );

                // Update idempotency record if exists
                if ($idempotencyKey) {
                    $this->idempotencyManager->updateResponse($idempotencyKey, $chainID, 422, $response);
                }

                return response()->json($response, 422, [], JSON_NUMERIC_CHECK);
            }
        }

        if ($chainSettings['enableSalesOrder']) {
            // Validate sales order using SalesOrderValidator
            $soValidationResult = $this->salesOrderValidator->validateAll(
                $data,
                $chainSettings,
                $chainID,
                $storeID
            );

            if (!$soValidationResult->isValid()) {
                $error = $soValidationResult->getFirstError();
                $response = ResponseFormatter::error(
                    $error['message'],
                    $error['code'],
                    $error['context'] ?? null
                );

                // Update idempotency record if exists
                if ($idempotencyKey) {
                    $this->idempotencyManager->updateResponse($idempotencyKey, $chainID, 422, $response);
                }

                return response()->json($response, 422, [], JSON_NUMERIC_CHECK);
            }
        }

        if ($chainSettings['enableUnicommerceIntegration']) {
            // Validate Unicommerce integration requirements
            $unicommerceValidationResult = $this->unicommerceValidator->validateAll(
                $data,
                $settings,
                $chainID,
                $storeID
            );

            if (!$unicommerceValidationResult->isValid()) {
                $error = $unicommerceValidationResult->getFirstError();
                $response = ResponseFormatter::error(
                    $error['message'],
                    $error['code'],
                    $error['context'] ?? null
                );

                // Update idempotency record if exists
                if ($idempotencyKey) {
                    $this->idempotencyManager->updateResponse($idempotencyKey, $chainID, 422, $response);
                }

                return response()->json($response, 422, [], JSON_NUMERIC_CHECK);
            }
        }

        if ($chainSettings['enableEasyEcomIntegration']) {
            // Validate EasyEcom integration requirements
            $easyEcomValidationResult = $this->easyEcomValidator->validateAll(
                $data,
                $settings,
                $chainID,
                $storeID
            );

            if (!$easyEcomValidationResult->isValid()) {
                $error = $easyEcomValidationResult->getFirstError();
                $response = ResponseFormatter::error(
                    $error['message'],
                    $error['code'],
                    $error['context'] ?? null
                );

                // Update idempotency record if exists
                if ($idempotencyKey) {
                    $this->idempotencyManager->updateResponse($idempotencyKey, $chainID, 422, $response);
                }

                return response()->json($response, 422, [], JSON_NUMERIC_CHECK);
            }
        }

        // Perform remaining DB validations
        $dbValidation = OrderValidatorDB::validateAll($data, $storeID, $chainID, $settings);
        if ($dbValidation['status'] === false) {
            $response = ResponseFormatter::error(
                $dbValidation['message'],
                isset($dbValidation['errorCode']) ? $dbValidation['errorCode'] : "VALIDATION_ERROR"
            );

            // Update idempotency record if exists
            if ($idempotencyKey) {
                $this->idempotencyManager->updateResponse($idempotencyKey, $chainID, 422, $response);
            }

            return response()->json($response, 422, [], JSON_NUMERIC_CHECK);
        }

        // Validate products using new ProductValidator
        $productValidationResult = $this->productValidator->validateAll(
            $data['productsList'] ?? [],
            $chainSettings,
            $storeID,
            $chainID,
            $settings,
            $data
        );

        if (!$productValidationResult->isValid()) {
            $error = $productValidationResult->getFirstError();
            $response = ResponseFormatter::error(
                $error['message'],
                $error['code']
            );

            // Update idempotency record if exists
            if ($idempotencyKey) {
                $this->idempotencyManager->updateResponse($idempotencyKey, $chainID, 422, $response);
            }

            return response()->json($response, 422, [], JSON_NUMERIC_CHECK);
        }

        // Get normalized products with default values filled
        $data['productsList'] = $productValidationResult->getModifiedData();

        // Start performance monitoring
        $startTime = microtime(true);
        $queryCount = DB::getQueryLog() ? count(DB::getQueryLog()) : 0;

        Log::info("v3/OrderController::postOrder - Starting order processing", [
            'storeID' => $storeID,
            'orderID' => $data['orderID'] ?? 'unknown'
        ]);

        // Process the order using the Order model instance
        $orderModel = new Order();
        $order = $orderModel->postOrder($storeID, $data, $settings);

        Log::info("v3/OrderController::postOrder - Order processing completed", [
            'storeID' => $storeID,
            'orderID' => $data['orderID'] ?? 'unknown',
            'status' => isset($order['orderCreationReport']['status']) ? $order['orderCreationReport']['status'] : 'unknown'
        ]);

        // Calculate performance metrics
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 4);
        $endQueryCount = DB::getQueryLog() ? count(DB::getQueryLog()) : 0;
        $totalQueries = $endQueryCount - $queryCount;

        // Add performance metrics to the response
        $order['performance'] = [
            'executionTime' => $executionTime,
            'queryCount' => $totalQueries,
        ];

        // Dispatch all necessary jobs using the centralized JobDispatcher
        // Dipatch only if order was successful
        if (isset($order["orderCreationReport"]) && isset($order["orderCreationReport"]["status"]) && $order["orderCreationReport"]["status"] === "Created") {
            $order = JobDispatcher::dispatchPostOrderJobs($data, $order, $storeID, $chainSettings, $storeDetail);
        }

        // Return response
        // Check if the order has a status field, if not, assume failure
        // Determine status code based on response format
        if (isset($order["orderCreationReport"]) && isset($order["orderCreationReport"]["status"]) && $order["orderCreationReport"]["status"] === "Created") {
            $statusCode = 201;
        } else if (isset($order["orderCreationReport"]["status"])) {
            $statusCode = $order["orderCreationReport"]["status"] === true ? 201 : 422;
        } else {
            // Default to failure if we can't determine status
            $statusCode = 422;
        }

        // Update idempotency record if exists
        if ($idempotencyKey) {
            // Use the IdempotencyManager to update the response
            $this->idempotencyManager->updateResponse($idempotencyKey, $chainID, $statusCode, $order);
        }

        Log::info("v3/order API request completed for storeID: $storeID " . $uniqId);
        return response()->json($order, $statusCode, [], JSON_NUMERIC_CHECK);
    }
}
