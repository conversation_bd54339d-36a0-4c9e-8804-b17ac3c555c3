<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Log;

/**
 * ResponseFormatter - Standardized response formatting for API responses
 * 
 * This class provides methods to format API responses in a consistent way.
 */
class ResponseFormatter
{
    /**
     * Format a success response
     * 
     * @param array $data The response data
     * @param string $message The success message
     * @return array The formatted response
     */
    public static function success($data = [], $message = "Operation successful")
    {
        $response = [
            "status" => true,
            "message" => $message
        ];

        if (!empty($data)) {
            $response = array_merge($response, $data);
        }

        return $response;
    }

    /**
     * Format an error response
     * 
     * @param string $message The error message
     * @param string $errorCode The error code
     * @param array $data Additional data to include in the response
     * @return array The formatted response
     */
    public static function error($message, $errorCode = null, $data = [])
    {
        $response = [
            "status" => false,
            "message" => $message
        ];

        if ($errorCode) {
            $response["errorCode"] = $errorCode;
        }

        if (!empty($data)) {
            $response = array_merge($response, $data);
        }

        // Log the error
        Log::error("API Error: " . $message . (isset($errorCode) ? " (Code: $errorCode)" : ""), $data);

        return $response;
    }

    /**
     * Format a validation error response
     * 
     * @param string $message The validation error message
     * @param array $errors The validation errors
     * @return array The formatted response
     */
    public static function validationError($message, $errors = [])
    {
        $response = [
            "status" => false,
            "message" => $message,
            "errorCode" => "VALIDATION_ERROR"
        ];

        if (!empty($errors)) {
            $response["errors"] = $errors;
        }

        // Log the validation error
        Log::warning("API Validation Error: " . $message, $errors);

        return $response;
    }

    /**
     * Format an idempotency response (when the same request is received multiple times)
     * 
     * @param array $originalResponse The original response
     * @return array The formatted response
     */
    public static function idempotency($originalResponse)
    {
        $response = $originalResponse;
        $response["idempotent"] = true;
        
        return $response;
    }
}
