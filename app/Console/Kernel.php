<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
	/**
	 * The Artisan commands provided by your application.
	 *
	 * @var array
	 */
	protected $commands = [
		Commands\Inspire::class,
		'App\Console\Commands\dailySales',
		'App\Console\Commands\UpdateLicenseStatus',
		'App\Console\Commands\ServerStats',
		'App\Console\Commands\SendBirthdayMessage',
		'App\Console\Commands\SendAnniversaryMessage',
		'App\Console\Commands\SendPointsExpiryMessage',
		'App\Console\Commands\SendAutoMailer',
		'App\Console\Commands\SendStockAlert',
		'App\Console\Commands\SendDiwaliWishes',
		'App\Console\Commands\CheckNavisionData',
		'App\Console\Commands\PushCatalogueToUrbPiper',
		'App\Console\Commands\SendVoidOrderNotification',
		'App\Console\Commands\importOrderDetailToTally',
		'App\Console\Commands\checkOrderExpiryDuration',
		'App\Console\Commands\autoSlotGeneration',
		'App\Console\Commands\DailySalesTracker',
		'App\Console\Commands\FixDailySalesTracker',
		'App\Console\Commands\automateLicensePayment',
		'App\Console\Commands\PushCatalogueToUrbPiperBulk',
		'App\Console\Commands\globalStatsDaily',
		'App\Console\Commands\globalPartnerStatsDaily',
		'App\Console\Commands\testcmd',
		'App\Console\Commands\ManageElectronicProductCommand',
		'App\Console\Commands\PushADSRSalesData',
		'App\Console\Commands\bulkMysquareOrderPush',
		'App\Console\Commands\globalCustomerKhataTransactions',
		'App\Console\Commands\thirdPartyInvoiceGeneration',
		'App\Console\Commands\pushUnsyncNavisionData',
		'App\Console\Commands\DeactivateNonTransactingChains',
		'App\Console\Commands\trackCatalogueUpdatesCommand',
		'App\Console\Commands\captureRazorpayPayments',
		'App\Console\Commands\SendStoreSalesSummaryIEMS',
		'App\Console\Commands\ImportCatalogueDLF',
		'App\Console\Commands\DLFSalesData',
		'App\Console\Commands\checkLoyaltyPointExpiry',
		'App\Console\Commands\checkFailedUPPCLPayment',
		'App\Console\Commands\CleanupErrorLogs',
		'App\Console\Commands\SendDailyQBDigestData',
		'App\Console\Commands\fetchQBDailyDigestDataCommand',
		'App\Console\Commands\fetchMuguDailyDigestDataCommand',
		'App\Console\Commands\fetchSSDailyDigestDataCommand',
		'App\Console\Commands\sendBSESReportFile',
		'App\Console\Commands\PushTTDRMSData',
		'App\Console\Commands\OfflineEasyEcomCron',
		'App\Console\Commands\sendTTDStagingData',
		'App\Console\Commands\fixStockLevelData',
		'App\Console\Commands\updateProductModifierIDJob',
		'App\Console\Commands\sendExecutedQueries',
		'App\Console\Commands\sendPreviousDayTTDStagingData',
		'App\Console\Commands\postAdvancePaymentDLF',
		'App\Console\Commands\executeAllQueries',
		'App\Console\Commands\exportSalesDataDLFBrands',
		'App\Console\Commands\SendCreditNoteExpiryEmailNotification',
		'App\Console\Commands\ecomPendingPaymentStatusCheck',
		'App\Console\Commands\exportAttributes',
		'App\Console\Commands\generateDLFSalesExportScript',
		'App\Console\Commands\BhawarCatalogCron',
		'App\Console\Commands\dispatchUPAgriUnsyncOrders',
		'App\Console\Commands\unicommerceShippingPackage',
		'App\Console\Commands\CheckQueueKeys',
		'App\Console\Commands\SalesControlScheduler',
		'App\Console\Commands\updateFulfillment',
		'App\Console\Commands\bulkSalesOrder',
		'App\Console\Commands\DumpTickets',
		'App\Console\Commands\CheckWalletBalanceExpiry',
		'App\Console\Commands\MysquarePushUnsyncOrders',
		'App\Console\Commands\executeWonderlaERP',
		'App\Console\Commands\MysquarePushUnsyncOrders',
		'App\Console\Commands\CheckFiscalYearChange',
		'App\Console\Commands\UpdateSchema',		
		'App\Console\Commands\TestWeightedAverage',
		'App\Console\Commands\AurionProCropMaster',
		'App\Console\Commands\CleanupIdempotencyKeys'
	];

	/**
	 * Define the application's command schedule.
	 *
	 * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
	 * @return void
	 */
	protected function schedule(Schedule $schedule)
	{
		// $schedule->command('inspire')->hourly();
		$schedule->command('serverStats')->hourly()->when(function () {
			$now = date('Y-m-d H:i:s');
			$currentTime = date_create($now);
			$hour = $currentTime->format('H');
			if ($hour > 15 && $hour < 17) {
				return true;
			}
		});

		// if (env("DUMP_ALL", false) == true) {
		// 	// Run at 15 and 45 minutes past each hour
		// 	$schedule->command('catalogue:process-updates')->cron('15,45 * * * *');
		// }
		// Added to crontab file:
		// 15,45 * * * * cd /var/www/API && php artisan catalogue:process-updates >> /var/log/catalogue-updates.log 2>&1

		// Run error logs cleanup daily at midnight
		$schedule->command('errorlogs:cleanup')->dailyAt('00:00');

		// Check fiscal year changes daily at 11 PM to ensure all updates happen before midnight
		// $schedule->command('fiscal:check-changes')->dailyAt('23:00');

		$irdEnabled = false;
		if (env("IRD_ENABLED") != null && env("IRD_ENABLED") == true) {
			$irdEnabled = true;
		}

		$schedule->command('checkQueueKeys')->everyTenMinutes();
		
		$schedule->command('dump:tickets')->dailyAt('04:45');

		// $schedule->command('unicommerceShippingPackage')->everyThirtyMinutes();
		// $schedule->command('UnicommerceDispatch')->hourly();
		//$schedule->command('unicommerceShippingPackage')->cron('15 * * * *');
		//$schedule->command('OfflineEasyEcomCron')->everyThirtyMinutes();
		// $schedule->command('OfflineEasyEcomCron')->cron('10 * * * *');


		//$schedule->command('bhawarCatalogCron')->everyThirtyMinutes();
		if (env("APP_ENV") == "test") {
			$schedule->command('BhawarCatalogCron')->dailyAt('22:00');
		} else if (env("DUMP_ALL", false) == false) {
			$schedule->command('BhawarCatalogCron')->cron('15 * * * *');
		}

		$schedule->command('sendAutoMailer')->everyFiveMinutes();
		$schedule->command('sendStockAlert')->dailyAt('23:00');
		$schedule->command('DailySalesTracker')->dailyAt('02:00');
		$schedule->command('updateLicenseStatus')->dailyAt('02:30');
		$schedule->command('sendBirthdayMessage')->dailyAt('09:00');
		$schedule->command('sendAnniversaryMessage')->dailyAt('10:00');
		$schedule->command('sendPointsExpiryMessage')->dailyAt('11:00');
		$schedule->command('sendExecutedQueries')->dailyAt('23:00');
		$schedule->command('executeAllQueries')->dailyAt('23:55');
		$schedule->command('exportSalesDataDLFBrands')->dailyAt('01:15');
		//$schedule->command('updateFulfillment')->dailyAt('00:01');
		$schedule->command('bulkSalesOrder')->dailyAt('00:05');
		// $schedule->command('PushCatalogueToUrbPiper')->everyMinute();
		if (env("APP_ENV") == "test") {
			$schedule->command('checkOrderExpiryDuration')->cron('0 0/3 * * * *');
		} else {
			$schedule->command('checkOrderExpiryDuration')->hourly();
		}
		// $schedule->command('autoSlotGeneration')->dailyAt('01:30');
		// $schedule->command('PushCatalogueToUrbPiperBulk')->everyFiveMinutes();

		if ($irdEnabled) {
			// Add IRD Specific commands
		} else {
			$schedule->command('thirdPartyInvoiceGeneration')->dailyAt('00:30');
			$schedule->command('ImportCatalogueDLF')->dailyAt('01:30');
			$schedule->command('fetchQBDailyDigestDataCommand')->dailyAt('04:00');
			$schedule->command('fetchMuguDailyDigestDataCommand')->dailyAt('04:15');
			$schedule->command('fetchSSDailyDigestDataCommand')->dailyAt('04:30');
			$schedule->command('SendDailyQBDigestData')->dailyAt('08:00');
			$schedule->command('automateLicensePayment')->dailyAt('01:40');
			// $schedule->command('CheckNavisionData')->everyTenMinutes();
			// $schedule->command('globalStatsDaily')->dailyAt('02:15');
			// $schedule->command('globalPartnerStatsDaily')->dailyAt('02:30');
			$schedule->command('pushUnsyncNavisionData')->dailyAt('00:30');
			$schedule->command('captureRazorpayPayments')->dailyAt('00:45');
			$schedule->command('SendStoreSalesSummaryIEMS')->dailyAt('22:00');
			$schedule->command('pushUnsyncNavisionData')->dailyAt('05:30');
			$schedule->command('DLFSalesData')->dailyAt('00:05');
			$schedule->command('sendBSESReportFile')->dailyAt('00:10');
			
			
			$schedule->command('fixStockLevelData')->dailyAt('03:05');
			$env = env('APP_ENV');
			if($env != 'test'){
				$schedule->command('PushTTDRMSData')->dailyAt('03:00');
				$schedule->command('PushTTDRMSData')->dailyAt('10:30');
				$schedule->command('PushTTDRMSData')->dailyAt('13:30');
				$schedule->command('PushTTDRMSData')->dailyAt('20:00');
			}
			
			
			$schedule->command('sendTTDStagingData')->dailyAt('00:30');
			$schedule->command('sendPreviousDayTTDStagingData')->dailyAt('10:00');
			$schedule->command('sendTTDStagingData')->dailyAt('13:00');
			$schedule->command('sendTTDStagingData')->dailyAt('19:30');
			$schedule->command('sendCreditNoteExpiryNotify:cron')->dailyAt('04:30');
			// $schedule->command('dispatchUPAgriUnsyncOrders')->dailyAt('23:30');
			$schedule->command('dispatchUPAgriUnsyncOrders')->dailyAt('05:00');
			$schedule->command('MysquarePushUnsyncOrders')->dailyAt('05:30');
			$schedule->command('AurionProCropMaster')->dailyAt('02:30');
			//$schedule->command('dispatchUPAgriUnsyncOrders')->dailyAt('02:15');
			//$schedule->command('ecomPendingPaymentStatusCheck')->everyTenMinutes();
			$schedule->command('SalesControlScheduler')->dailyAt('00:15');
			$schedule->command('CheckWalletBalanceExpiry')->dailyAt('23:00');
		}

		$schedule->command('cleanup:idempotency-keys')->dailyAt('05:30');
	}
}
