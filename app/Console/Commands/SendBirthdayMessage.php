<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Foundation\Inspiring;
use DB;
use Constants;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;

class SendBirthdayMessage extends Command 
{

    protected $signature = 'sendBirthdayMessage';

    protected $description = 'Command to send birthday message';    
    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the command.
     *
     * @return void
     */
    public function handle()
    {
        $globalCustomersLoyaltyInfoTable = Constants::getGlobalTableName('globalCustomersLoyaltyInfo');
        $listOfChainsTable = Constants::getGlobalTableName('listOfChains');
        $crmSettingsTable = Constants::getGlobalTableName('crmSettings');
        $globalChainMessageCreditsTable = Constants::getGlobalTableName('globalChainMessageCredits');
        date_default_timezone_set("UTC");
        $currentTimeUTC = date("Y-m-d H:i:s");
        $currentMonthUTC = intval(explode("-", date("Y-m-d"))[1]);
        if ($currentMonthUTC == 1) {
            $previousMonth = 12;
        } else {
            $previousMonth = $currentMonthUTC - 1;
        }
        if ($currentMonthUTC == 12) {
            $nextMonth = 1;
        } else {
            $nextMonth = $currentMonthUTC + 1;
        }

        $getChainSettings = convertToArray(DB::select(DB::raw("SELECT DISTINCT A.chainID, A.sendSMSOnBirthday, A.birthdayText,B.loyalty, B.timezone,pointsExpiryDays,birthdayPoints FROM $crmSettingsTable AS A
            INNER JOIN $listOfChainsTable AS B ON A.chainID = B.chainID
            WHERE A.isActive = 1 AND B.isActive = 1 AND (birthdayPoints > 0 OR (birthdayText != '' AND sendSMSOnBirthday = 1))")));

        // $getLoyaltyData = convertToArray(DB::table($globalCustomersLoyaltyInfoTable)->where('isActive',1)->select('chainID','DOB','phone','timezone','chainCustomerID')->get());
        $getLoyaltyData = convertToArray(DB::select(DB::raw("SELECT chainID, DOB, phone, timezone, chainCustomerID 
            FROM $globalCustomersLoyaltyInfoTable 
            WHERE isActive = 1 AND DAY(DOB) > 0 AND MONTH(DOB) IN ($previousMonth, $currentMonthUTC, $nextMonth)
            GROUP BY chainID, phone;")));
        $loyalCount = count($getLoyaltyData);
        for ($i=0; $i < $loyalCount; $i++) { 
            $chainID = $getLoyaltyData[$i]['chainID'];
            $DOB = $getLoyaltyData[$i]['DOB'];
            $phone = $getLoyaltyData[$i]['phone'];
            $timezone = $getLoyaltyData[$i]['timezone'];
            $chainCustomerID = $getLoyaltyData[$i]['chainCustomerID'];


            $chainKey = array_search($chainID, array_column($getChainSettings, 'chainID'));
            $birthdayMessage = $getChainSettings[$chainKey]['birthdayText'];

            $loyalty = $getChainSettings[$chainKey]['loyalty'];

            if ($loyalty != 225) {
                continue;
            }

            date_default_timezone_set($timezone);
            $today = date("Y-m-d");
            $currentTimeLocal = date("Y-m-d H:i:s");
            
            $todayNew = explode("-", $today);
            $currentDate = $todayNew[2];
            $currentMonth = $todayNew[1];

            $DOBNew = explode("-", $DOB);
            $DOBDate = $DOBNew[2];
            $DOBMonth = $DOBNew[1];

            if ($currentDate == $DOBDate && $currentMonth == $DOBMonth) {
                // Add logging statements to track execution flow
                Log::info("Checking for chainCustomerID: $chainCustomerID at ".time()." for chainID: $chainID and DOB: $DOB and phone: $phone and timezone: $timezone ");
                // $sentMessagesHistoryTable = Constants::getChainTableName($chainID,'sentMessagesHistory');
                // $listOfStoresTable = Constants::getGlobalTableName('listOfStores');
                // $client = new \GuzzleHttp\Client(['headers' => ['Accept' => 'application/json']]);
                // $client->setDefaultOption('headers', array('Accept' => 'application/json'));


                // $msgStatus = array();

                // $senderID = "BUSTER";




                // $mobile = $phone;

                // $dcs = 0;

                // $message = $birthdayMessage;

                // $creditsConsumed = ceil(strlen($message)/160);

                // $checkCredits = convertToArray(DB::table($globalChainMessageCreditsTable)->where('chainID', $chainID)->get());
                // if (empty($checkCredits)) {
                //     continue;
                // }

                // $creditsLeft = $checkCredits[0]['creditsLeft'];
                // $creditsConsumedTotal = $checkCredits[0]['creditsConsumed'];

                // if ($creditsLeft < $creditsConsumed) {
                //     continue;
                    
                // }

                // $finalCreditsLeft = $creditsLeft - $creditsConsumed;
                // $finalCreditsConsumend = $creditsConsumedTotal + $creditsConsumed;

                // $msgUrl = "https://sms.teleosms.com/api/mt/SendSMS?APIKey=".env('TOP10SMS_API_KEY')."&senderid=".env('TOP10SMS_SENDERID')."&channel=Trans&DCS=0&flashsms=0&number=91".$mobile."&text=".$message."&route=2&peid=1201159187823864253";
                // $client = new \GuzzleHttp\Client();
                // $res = $client->request('GET', $msgUrl);
                // $response = json_decode($res->getBody(),true);
                // if (!isset($response["ErrorCode"])) {
                //     $status["status"] = false;
                //     $status["message"] = $response["message"];

                //     return $status;
                // }
                // // if ($response["ErrorCode"] == "000") {
                //     $messageID = $response["MessageData"][0]["MessageId"];
                //     $insertArray = array();
                //     $updateArray = array();

                //     $insertArray["storeID"] = 0;
                //     $insertArray["orderID"] = "-";            
                //     $insertArray["message"] = $message;
                //     $insertArray["messageType"] = "BIRTHDAY";
                //     $insertArray["customerID"] = $chainCustomerID;
                //     $insertArray["phoneCode"] = 91;
                //     $insertArray["phone"] = $mobile;
                //     $insertArray["creditsConsumed"] = $creditsConsumed;
                //     $insertArray["messageID"] = $messageID;
                //     $insertArray["remarks"] = $response["ErrorMessage"];

                //     $insertArray["messageDeliveredTimeLocal"] = $currentTimeLocal;
                //     $insertArray["messageDeliveredTimeUTC"] = $currentTimeUTC;
                //     $insertArray["timezone"] = $timezone;

                //     $updateArray["chainID"] = $chainID;
                //     $updateArray["creditsConsumed"] = $finalCreditsConsumend;
                //     $updateArray["creditsLeft"] = $finalCreditsLeft;

                    // loyality point ledger
                    $customersTable = Constants::getChainTableName($chainID,"customers");
                    $customersTable = Constants::getChainTableName($chainID, "customers");
                    $loyaltyPointsLedgerTable = Constants::getChainTableName($chainID,"loyaltyPointsLedger");
                    $getCustomerDetail = convertToArray(DB::table($customersTable)->select('availablePoints', 'pointsCollected')->where('customerID',$chainCustomerID)->where('enableCRM', 1)->get());
                    if(empty($getCustomerDetail)){
                        $status['status'] = false;
                        $status['message'] = "invalid Customer";
                        return $status;
                    }
                    $availablePoints = $getCustomerDetail[0]['availablePoints'];
                    $pointsCollected = $getCustomerDetail[0]['pointsCollected'];
                    if($getChainSettings[$chainKey]['pointsExpiryDays'] > 0){
                        $pointExpiryDays = $getChainSettings[$chainKey]['pointsExpiryDays'];
                    } else{
                        $pointExpiryDays = 0;
                    }
                    $pointExpiryDate = date("Y-m-d H:i:s",strtotime("+".$pointExpiryDays."days"));
                    if ($pointExpiryDays == 0) {
                        $pointExpiryDate = null;
                    }
                    $availablePoints = $availablePoints + $getChainSettings[$chainKey]['birthdayPoints'];
                    $pointsCollected = $pointsCollected + $getChainSettings[$chainKey]['birthdayPoints'];
                    $loyaltyPointLedger = array();
                    $loyaltyPointLedger['customerID'] = $chainCustomerID;
                    $loyaltyPointLedger['storeID'] = 0;
                    $loyaltyPointLedger['transactionType'] = 'EARNED';
                    $loyaltyPointLedger['transactionSubType'] = 'BIRTHDAY';
                    $loyaltyPointLedger['transactionPoints'] = $getChainSettings[$chainKey]['birthdayPoints'];
                    $loyaltyPointLedger['customerBalance'] = $availablePoints;
                    $loyaltyPointLedger['transactionRemarks'] = "Point earned on birthday";
                    $loyaltyPointLedger['creationTimeLocal'] = $currentTimeLocal;
                    $loyaltyPointLedger['creationTimeUTC'] = $currentTimeUTC;
                    $loyaltyPointLedger['lastUpdatedAtLocal'] = $currentTimeLocal;
                    $loyaltyPointLedger['lastUpdatedAtUTC'] = $currentTimeUTC;
                    $loyaltyPointLedger['pointsExpiryDate'] = $pointExpiryDate;
                    $loyaltyPointLedger['timezone'] = $timezone;

                    DB::transaction(function()use($loyaltyPointsLedgerTable, $loyaltyPointLedger, $customersTable, $chainCustomerID, $pointsCollected) {
                        //DB::table($sentMessagesHistoryTable)->insert($insertArray);

                        //DB::table($globalChainMessageCreditsTable)->where('chainID', $updateArray["chainID"])->update($updateArray);

                        // Logs
                        Log::info("Updating customer points for chainCustomerID: $chainCustomerID at ".time()." for chainID: $loyaltyPointsLedgerTable and pointsCollected: $pointsCollected and availablePoints: ".$loyaltyPointLedger['customerBalance']);

                        DB::table($customersTable)
                            ->where('customerID', $chainCustomerID)
                            ->update(['pointsCollected' => $pointsCollected, 'availablePoints' => $loyaltyPointLedger['customerBalance']]);
                        DB::table($loyaltyPointsLedgerTable)->insert($loyaltyPointLedger);
                    });
                // }else{
                //     $messageID = NULL;
                // } 

                
            }

        }
    }
}
