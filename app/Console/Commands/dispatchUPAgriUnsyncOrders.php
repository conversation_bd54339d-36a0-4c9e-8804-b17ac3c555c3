<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Foundation\Inspiring;
use DB;
use Constants;
use App\SendMailer AS Mailer;
use Excel;
use App\Jobs\UPAgriUnsyncOrderDispatchJob;
use App\Agriculture as Agriculture;

class dispatchUPAgriUnsyncOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dispatchUPAgriUnsyncOrders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to dispatch UP Agri unsync order';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {

        $listOfChainsTable = Constants::getGlobalTableName('listOfChains');

        // fetch the UP agri chainID 
        $fetchChainDetail = convertToArray(DB::table($listOfChainsTable)->select('chainID')->where('chainMunicipalType','UP_AGRICULTURE')->where('isActive', 1)->get());
        if(!empty($fetchChainDetail)){
            for ($cid = 0; $cid < count($fetchChainDetail); $cid++) {
                $chainID = $fetchChainDetail[$cid]['chainID'];
                if ($chainID == 57521) {
                    // ignore
                    continue;
                }
                $invoiceSummaryTable = Constants::getChainTableName($chainID, 'invoiceSummary');
                // fetch the unsync orders 
                $fetchUnsyncOrders = convertToArray(DB::table($invoiceSummaryTable)->select('storeID','orderID')->where('posDate','>=','2024-04-01')->where('UPAgriOrderSyncStatus',0)->get());
                $count = count($fetchUnsyncOrders);
                echo "unsynced orders for chain ".$chainID.":  ".$count;
                $queueNumber = queueNumber($chainID);
                for ($i=0; $i < $count; $i++) { 
                    $tempArray = array();
                    $tempArray['orderID'] = $fetchUnsyncOrders[$i]['orderID'];
                    $tempArray['storeID'] = $fetchUnsyncOrders[$i]['storeID'];
                    //Agriculture::UPAgriDispatchSalesInvoiceByOrderID($tempArray, $chainID);
                    $job = (new UPAgriUnsyncOrderDispatchJob($tempArray, $chainID))->onQueue($queueNumber);
                    dispatch($job);
                }
                echo "dispatched jobs for chain: ".$chainID;
            }
        }

        return;
        
    }
       
}