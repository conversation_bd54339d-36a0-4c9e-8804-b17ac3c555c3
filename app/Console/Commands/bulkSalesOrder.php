<?php
namespace App\Console\Commands;


use Illuminate\Console\Command;
use DB;
use Constants;
use App\SalesOrder AS SalesOrder;
use Illuminate\Support\Facades\Log as Log;
class bulkSalesOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bulkSalesOrder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to create all the sales orders present in bulkSalesOrderSummary table';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle(){
        
        if(env('APP_ENV') == 'production' || env('APP_ENV') == 'staging'){
            $chainIDs = [];
        } else{
            $chainIDs = [3025,3448,3363,3112,3380,10];
        }

        try {

            for($i=0;$i<count($chainIDs);$i++){

                $chainID = $chainIDs[$i];

                try{
                    Log::info("process bulk sales order request".json_encode($chainID));
                    $response = SalesOrder::processBulkSalesOrder($chainID);
                    Log::info("process bulk sales order response".json_encode($response));

                } catch (\Exception $e) {
                    Log::info("bulkSalesOrderSchedular Error:" . $e->getMessage());
                    echo $e->getMessage();//
                }

            }    
        } catch (\Exception $e) {
            Log::info("bulkSalesOrderSchedular Error:" . $e->getMessage());
            echo $e->getMessage();//
        }

    }
}