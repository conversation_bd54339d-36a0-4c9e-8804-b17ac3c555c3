<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Foundation\Inspiring;
use DB;
use Constants;
use App\SendMailer AS Mailer;
use Excel;
use App\stockist\parseOrders as ParseOrder;
use App\thirdParty as thirdParty;
use App\OnlineOrder as OnlineOrder;
use App\OnlineReservation as OnlineReservation;
use App\SalesControl;
use Illuminate\Support\Facades\Log AS Log;

class SalesControlScheduler extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'SalesControlScheduler';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to automate sales config creation';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $chainSettings = Constants::getGlobalTableName('chainSettings');
        $chainIDs = convertToArray(DB::table($chainSettings)->where('enableSalesControl', 1)->select('chainRefID')->get());
        $chainCount = count($chainIDs);
        for($i=0; $i<$chainCount; $i++){
            $chainID = $chainIDs[$i]['chainRefID'];
            Log::info($chainID);
            $createConfigs = SalesControl::salesControlSchedulerScript($chainID);

        }

    }
}