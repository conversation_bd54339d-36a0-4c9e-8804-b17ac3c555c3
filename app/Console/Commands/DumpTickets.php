<?php

namespace App\Console\Commands;

use App\SendMailer as Mailer;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Ticket;

class DumpTickets extends Command
{
	protected $signature = 'dump:tickets';
	protected $description = 'Dump tickets from Wonderla system';

	public function __construct()
	{
		parent::__construct();
	}

	public function handle()
	{
		try {
			Log::info("Initiating ticket dump from WL_SOCKET_URL");
			$wlChainID = env('WL_CHAINID', 3380);
			$dumpAllTickets = Ticket::dumpActiveTicketsToRedis($wlChainID);
			Log::info("Ticket dump completed: " . json_encode($dumpAllTickets));

			$this->sendMail("<EMAIL>", "Ticket dump completed for " . $wlChainID, json_encode($dumpAllTickets), null, null);

			// $socketURL = env('WL_SOCKET_URL');
			// $curl = curl_init();

			// curl_setopt_array($curl, array(
			//     CURLOPT_URL => $socketURL."/dumptickets/".env('WL_CHAINID', 3380),
			//     CURLOPT_RETURNTRANSFER => true,
			//     CURLOPT_ENCODING => "",
			//     CURLOPT_MAXREDIRS => 10,
			//     CURLOPT_TIMEOUT => 90,
			//     CURLOPT_FOLLOWLOCATION => true,
			//     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			//     CURLOPT_CUSTOMREQUEST => "GET"
			// ));

			// $response = curl_exec($curl);
			// $err = curl_error($curl);

			// if ($err) {
			//     Log::error("Curl Error during ticket dump: " . $err);
			//     $this->error("Failed to dump tickets: " . $err);
			// } else {
			//     $response = json_decode($response, true);
			//     Log::info("Response from ticket dump: " . json_encode($response));
			//     $this->info("Tickets dumped successfully");
			// }

			// curl_close($curl);

		} catch (\Exception $e) {
			Log::error("Error during ticket dump: " . $e->getMessage());
			$this->error("Error dumping tickets: " . $e->getMessage());
		}
	}

	public function failed()
	{
		$email = "<EMAIL>";
		$client = array();
		$subject = "Error in mysquare push unsync order";
		$body = "";
		$body .= json_encode($this->dataArray);
		$body .= json_decode($this->chainID);
		$cc = null;
		$bcc = null;
		$this->sendMail($email, $subject, $body, $cc, $bcc);
		return response()->json($client, 201, [], JSON_NUMERIC_CHECK);

	}

	private function sendMail($email, $subject, $body, $cc, $bcc)
	{
		$sendMail = Mailer::mailResponse($email, $subject, $body, $cc, $bcc);
		if ($sendMail["returnCode"] == 200) {
			$client["mailStatus"] = 1;
			$client["mailMessage"] = "Mail sent successfully";
		} else {
			$client["mailStatus"] = 0;
			$client["mailMessage"] = "Error in sending mail";

		}
		return response()->json($client, 201, [], JSON_NUMERIC_CHECK);
	}
}
