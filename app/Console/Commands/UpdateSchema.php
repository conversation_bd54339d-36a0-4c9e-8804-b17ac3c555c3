<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Foundation\Inspiring;
use DB;
use Constants;
use App\Admin AS Admin;

class UpdateSchema extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'schema:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to update schema in an environment';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $globalSchemaTrackerTable = Constants::getGlobalTableName('globalSchemaTracker');
        $listOfChainsTable = Constants::getGlobalTableName('listOfChains');
        $chainSettingsTable = Constants::getGlobalTableName('chainSettings');
        $listOfStoresTable = Constants::getGlobalTableName('listOfStores');
        $listOfWarehousesTable = Constants::getGlobalTableName('listOfWarehouses');
        
        // $globalSchemaTracker = convertToArray(DB::table($globalSchemaTrackerTable)->where('Query',NULL)->get());   
        // $updateSchemaTracker = array();
        
        // if(!empty($globalSchemaTracker)){
        //     $refChainID = 1;
        //     for($i= 0;$i<count($globalSchemaTracker);$i++){
        //         $schemaId = $globalSchemaTracker[$i]['ID'];
        //         $schemaName = $globalSchemaTracker[$i]['Table_name'];
        //         $schemaType = $globalSchemaTracker[$i]['Type'];
        //         $operationType = $globalSchemaTracker[$i]['Operation_type'];
        //         $schemaStatus = $globalSchemaTracker[$i]['Status'];
                
        //         switch($schemaType){
        //             case 'GENERAL':
        //                 $schemaTableName = Constants::getChainTableName($refChainID,$schemaName);                        
        //                 break;
        //             case 'PRODUCT':
        //                 $schemaTableName = Constants::getChainLevelProductsName($refChainID,$schemaName);
        //                 break;
        //             case 'INVENTORY':
        //                 $schemaTableName = Constants::getChainLevelInventoryName($refChainID,$schemaName);
        //                 break;
        //             case "GLOBAL":
        //                 $schemaTableName = Constants::getGlobalTableName($schemaName);
        //                 break;
        //             default:
        //                 $schemaTableName = '';
        //                 break;
        //         }

        //         if($operationType == 'CREATE'){   
        //             $tempUpdateSchemaTracker = array();
        //             // $tempUpdateSchemaTracker['Status'] = 'COMPLETED';
        //             $tempUpdateSchemaTracker['ID'] = $schemaId;
        //             date_default_timezone_set('Asia/Kolkata');
        //             $tempUpdateSchemaTracker['creationTime'] = date('Y-m-d H:i:s');
        //             echo'schema name = '.$schemaName.' and Schema table name = '.$schemaTableName.'\n';
    
        //             $getTableStructure = convertToArray(DB::select(DB::raw("SHOW CREATE TABLE $schemaTableName")));
        //             $tempUpdateSchemaTracker['Query'] = $getTableStructure[0]['Create Table'];
        //             array_push($updateSchemaTracker,$tempUpdateSchemaTracker);

        //         }

                


                
        //     }
        // }


        // DB::transaction(function() use ($updateSchemaTracker,$globalSchemaTrackerTable){
            
        //     for ($i=0; $i < count($updateSchemaTracker); $i++) { 
        //         DB::table($globalSchemaTrackerTable)->where('ID',$updateSchemaTracker[$i]['ID'])->update($updateSchemaTracker[$i]);
        //     }
        // });

        // print_r($updateSchemaTracker);

        $fetchPendinQueries = convertToArray(DB::table($globalSchemaTrackerTable)->where('Status','PENDING')->get());

        if(!empty($fetchPendinQueries)){
            for ($i=0; $i < count($fetchPendinQueries); $i++) {
                $query = $fetchPendinQueries[$i]['Query'];
                if($fetchPendinQueries[$i]['Operation_type'] == 'CREATE'){                    

                    // replace CREATE TABLE currencyConversions with CREATE TABLE IF NOT EXISTS currencyConversions
                    $query = str_replace("CREATE TABLE", "CREATE TABLE IF NOT EXISTS", $query);
                    // Also, remove AUTO_INCREMENT=XYZ
                    $query = preg_replace('/AUTO_INCREMENT=[0-9]*/', 'AUTO_INCREMENT=1', $query);
                    // remove COLLATE from the query                
                                        
                    $query = preg_replace('/COLLATE=utf8mb4_0900_ai_ci/', '', $query);
                    $query = preg_replace('/COLLATE=UTF8MB4_0900_AI_CI/', '', $query);
                    echo "query = ".$query;
                    echo "\n";                    

                    // fetch query configuration
                    // ALTER TABLE queueBuster.globalSchemaTracker ADD entityLevel ENUM('GLOBAL','CHAIN','STORE','WAREHOUSE') AFTER Table_name, ADD executeForAllEntities TINYINT(1) DEFAULT 0 AFTER Operation_type, ADD industry VARCHAR(20) DEFAULT 'ALL' AFTER executeForAllEntities, ADD entitySetting VARCHAR(100) DEFAULT NULL AFTER industry, ADD entitySettingValue VARCHAR(256) DEFAULT NULL AFTER entitySetting;

                }elseif($fetchPendinQueries[$i]['Operation_type'] == 'UPDATE'){
                    

                    echo 'query = '.$query;
                    echo '\n';

                }elseif($fetchPendinQueries[$i]['Operation_type'] == 'DELETE'){
                    $query = str_replace("DROP TABLE", "DROP TABLE IF  EXISTS", $query);
                }

                $entityLevel = $fetchPendinQueries[$i]['entityLevel'];
                $executeForAllEntities = $fetchPendinQueries[$i]['executeForAllEntities'];
                $industry = $fetchPendinQueries[$i]['industry'];
                $entitySetting = $fetchPendinQueries[$i]['entitySetting'];
                $entitySettingValue = $fetchPendinQueries[$i]['entitySettingValue'];
                $schemaRouteArray = array();
                $schemaRouteArray['route'] = '/';
                $schemaRouteArray['chainID'] = 'ALL';
                $schemaRouteArray['storeID'] = 'ALL';
                $schemaRouteArray['warehouseID'] = 'ALL';

                switch ($entityLevel) {
                    case 'GLOBAL':

                        DB::statement($query);
                        break;
                    case 'CHAIN':
                        $schemaRouteArray['level'] = 'CHAIN';
                        $schemaRouteArray['query'] = $query;
                        if($executeForAllEntities == 1){
                            
                            
                            
                        }else{
                            // check for industry value

                            $industryCondition = '';
                            if($industry != 'ALL'){
                                $industryCondition = " AND industry = '$industry' ";
                            }

                            $settingCondition = '';
                            if($entitySetting != 'ALL' || $entitySetting != NULL){
                                $settingCondition = " AND $entitySetting = '$entitySettingValue' ";
                            }

                            $fetchEligibleEntities = convertToArray(DB::select(DB::raw("SELECT A.chainID FROM $listOfChainsTable AS A 
                            INNER JOIN $chainSettingsTable AS B ON A.chainID = B.chainRefID WHERE A.isActive = 1 AND A.isDeactivated = 0 
                            $industryCondition $settingCondition")));
                            if(!empty($fetchEligibleEntities)){
                                $schemaRouteArray['chainID'] = implode(',',array_column($fetchEligibleEntities,'chainID'));
                            }
                            
                            
                        }
                        
                        break;
                        case 'STORE':
                            $schemaRouteArray['level'] = 'STORE';
                            $schemaRouteArray['query'] = $query;
                            if($executeForAllEntities == 1){
                                $schemaRouteArray['storeID'] = 'ALL';
                            }else{
                                // check for industry value
                                $industryCondition = '';
                                if($industry!= 'ALL'){
                                    $industryCondition = ' AND B.industry = '.$industry;
                                }
                                $settingCondition = '';
                                if($entitySetting!= 'ALL' || $entitySetting!= NULL){
                                    $settingCondition = " AND $entitySetting = '$entitySettingValue' ";
                                }
                                $fetchEligibleEntities = convertToArray(DB::select(DB::raw("SELECT A.storeID FROM $listOfStoresTable AS A 
                                INNER JOIN $listOfChainsTable AS B ON A.chainID = B.chainID 
                                INNER JOIN $chainSettingsTable AS C ON B.chainID = C.chainRefID
                                WHERE A.isActive = 1 AND A.isDeactivated = 0 AND B.isDeactivated $industryCondition $settingCondition")));
                                if(!empty($fetchEligibleEntities)){
                                    $schemaRouteArray['storeID'] = implode(',',array_column($fetchEligibleEntities,'storeID'));
                                }
                            }
                            break;
                            case 'WAREHOUSE':
                                $schemaRouteArray['level'] = 'WAREHOUSE';
                                $schemaRouteArray['query'] = $query;
                                if($executeForAllEntities == 1){
                                    $schemaRouteArray['warehouseID'] = 'ALL';
                                }else{
                                    // check for industry value
                                    $industryCondition = '';
                                    if($industry!= 'ALL'){
                                        $industryCondition ='AND B.industry = '.$industry;
                                    }
                                    $settingCondition = '';
                                    if($entitySetting!= 'ALL' || $entitySetting!= NULL){
                                        $settingCondition = ' AND $entitySetting = '.$entitySettingValue;
                                    }
                                    $fetchEligibleEntities = convertToArray(DB::select(DB::raw("SELECT A.warehouseID FROM $listOfWarehousesTable AS A                                        
                                    INNER JOIN $listOfChainsTable AS B ON A.chainID = B.chainID
                                    INNER JOIN $chainSettingsTable AS C ON C.chainID = B.chainRefID
                                    WHERE A.isActive = 1 AND A.isDeactivated = 0 AND B.isDeactivated = 0 $industryCondition $settingCondition")));
                                }
                                break;
                            default:
                                # code...
                                break;
                }

                if(($executeForAllEntities == 1 || count($fetchEligibleEntities) > 0) && $entityLevel != 'GLOBAL'){
                    print_r($schemaRouteArray);
                    echo "\n";
                    // create schema routes
                    $executeEntityQueries  = Admin::createSchemaRoutes($schemaRouteArray);
                    if($executeEntityQueries['status'] == false){
                        print_r($executeEntityQueries);
                        exit;
                    }
                }

                // DB::statement($query);


                $tempUpdateSchemaTracker = array();
                $tempUpdateSchemaTracker['Status'] = 'COMPLETED';
                // $tempUpdateSchemaTracker['ID'] = $fetchPendinQueries[$i]['ID'];
                date_default_timezone_set('Asia/Kolkata');
                $tempUpdateSchemaTracker['executionTime'] = date('Y-m-d H:i:s');

                DB::table($globalSchemaTrackerTable)->where('ID',$fetchPendinQueries[$i]['ID'])->update($tempUpdateSchemaTracker);

            }
        }

    }
}

?>
