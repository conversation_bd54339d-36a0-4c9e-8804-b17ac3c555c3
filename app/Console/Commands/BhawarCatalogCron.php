<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Foundation\Inspiring;
use DB;
use Constants;

use App\Jobs\BhawarCatalogSyncCronJob;

class BhawarCatalogCron extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'BhawarCatalogCron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'fetch catalog from bhawar and update in our system';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
            $chainID = 3047;
            $listOfChainsTable =  Constants::getGlobalTableName("listOfChains");
            $chainSettings     =  Constants::getGlobalTableName("chainSettings");
            $listOfStoresTable =  Constants::getGlobalTableName("listOfStores");
            
            $chainList  =  convertToArray(DB::table("$listOfChainsTable AS lc")
                            ->leftJoin("$listOfStoresTable AS ls", "ls.chainID", "=", "lc.chainID")
                            ->select("lc.chainID", "ls.storeID")
                            ->get());
            if(isset($chainList) && !empty($chainList))
            {
                $requestArray =[];
                $countChain = count($chainList);
                for ($i=0; $i < $countChain; $i++) 
                { 
                    $chainID = $chainList[$i]['chainID'];
                    $storeID = $chainList[$i]['storeID'];

                    $queueNumber = queueNumber($chainID);
                    $requestArray['storeID'] = $storeID;
                    $job = (new BhawarCatalogSyncCronJob($chainID,$requestArray))->onQueue($queueNumber);
                    dispatch($job);
                }
            }

            // if(isset($chainList) && !empty($chainList))
            // {
            //     $requestArray =[];
            //     $countChain = count($chainList);
            //     for ($i=0; $i < $countChain; $i++) 
            //     { 
            //         $chainID = $chainList[$i]['chainID'];
            //         $storeID = $chainList[$i]['storeID'];

            //         $queueNumber = queueNumber($chainID);
            //         $requestArray['storeID'] = $storeID;
            //         // $job = (new BhawarCatalogPriceSyncCronJob($chainID,$requestArray))->onQueue($queueNumber);
            //         dispatch($job);
            //     }
            // }

    }
}
