<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Constants;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CleanupIdempotencyKeys extends Command
{
	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'cleanup:idempotency-keys';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Deletes idempotency keys older than 7 days from global tables. Also, deletes all 422 records daily at 05:30';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Execute the console command.
	 *
	 * @return int
	 */
	public function handle()
	{
		$cutoffDate = Carbon::now()->subDays(7);
		$tables = [Constants::getGlobalTableName('globalInvoiceIdempotencyKeys'), Constants::getGlobalTableName('globalTransactionIdempotencyKeys')];
		$totalDeleted = 0;

		Log::info('Starting idempotency key cleanup task.');
		$this->info('Starting idempotency key cleanup task...');

		foreach ($tables as $table) {
			try {
				// Assuming a 'created_at' column exists. Change if needed.
				$deletedCount = DB::table($table)
					->where('createdAt', '<=', $cutoffDate)
					->delete();

				$totalDeleted += $deletedCount;
				Log::info("Deleted {$deletedCount} records older than {$cutoffDate} from {$table}.");
				$this->info("Deleted {$deletedCount} records older than {$cutoffDate} from {$table}.");

				// delete all 422 records
				$deletedCount422 = DB::table($table)
					->where('statusCode', '422')
					->delete();

				$totalDeleted += $deletedCount422;
				Log::info("Deleted {$deletedCount422} records with status code 422 from {$table}.");
				$this->info("Deleted {$deletedCount422} records with status code 422 from {$table}.");

			} catch (\Exception $e) {
				Log::error("Error deleting from table {$table}: " . $e->getMessage());
				$this->error("Error deleting from table {$table}. Check logs.");
				// Decide if you want to stop or continue with other tables
				// return 1; // Indicate failure
			}
		}

		Log::info("Idempotency key cleanup task finished. Total records deleted: {$totalDeleted}.");
		$this->info("Idempotency key cleanup task finished. Total records deleted: {$totalDeleted}.");
		return 0; // Indicate success
	}
}