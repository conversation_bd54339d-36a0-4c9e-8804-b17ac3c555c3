<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use App\Scripts;

class TrackCatalogueUpdatesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'catalogue:process-updates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process catalogue update files from exports/dbupdate directory';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting to process catalogue update files...');
        
        // Get all batch JSON files in the exports/dbupdate directory
        // $files = Storage::disk('exports/dbupdate')->files();
        $files = storage_path('exports/dbupdate');
        $files = scandir($files);
        $files = array_filter($files, function($file) {
            return strpos($file, 'catalogue_batch_') === 0 && substr($file, -5) === '.json';
        });

        if (empty($files)) {
            $this->info('No batches to process.');
            return 0;
        }

        foreach ($files as $file) {
            try {
                $this->info("Processing batch: {$file}");
                
                // Read batch contents
                // $content = Storage::disk('exports/dbupdate')->get($file);
                $content = file_get_contents(storage_path('exports/dbupdate/' . $file));
                $batch = json_decode($content, true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $this->error("Invalid JSON in batch {$file}");
                    // Storage::disk('exports/dbupdate')->delete($file);
                    unlink(storage_path('exports/dbupdate/' . $file));
                    continue;
                }

                if (!isset($batch['chainID']) || !isset($batch['updates']) || !is_array($batch['updates'])) {
                    $this->error("Invalid batch format in {$file}");
                    // Storage::disk('exports/dbupdate')->delete($file);
                    unlink(storage_path('exports/dbupdate/' . $file));
                    continue;
                }

                $chainID = $batch['chainID'];
                $successCount = 0;
                $failureCount = 0;

                // Process all updates in the batch
                foreach ($batch['updates'] as $data) {
                    try {
                        $result = Scripts::trackCatalogUpdates($data, $chainID);
                        if ($result['status']) {
                            $successCount++;
                        } else {
                            $failureCount++;
                            $this->error("Error processing update in batch {$file}: " . ($result['message'] ?? 'Unknown error'));
                        }
                    } catch (\Exception $e) {
                        $failureCount++;
                        $this->error("Exception processing update in batch {$file}: " . $e->getMessage());
                    }
                }

                // Delete the batch file after processing
                // Storage::disk('exports/dbupdate')->delete($file);
                unlink(storage_path('exports/dbupdate/' . $file));
                $this->info("Batch {$file} processed. Success: {$successCount}, Failures: {$failureCount}");
                
            } catch (\Exception $e) {
                $this->error("Error processing file {$file}: " . $e->getMessage());
            }
        }

        $this->info('Finished processing catalogue update files.');
        return 0;
    }
}
