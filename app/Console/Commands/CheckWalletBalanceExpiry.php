<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Foundation\Inspiring;
use DB;
use Constants;
use App\Jobs\WalletBalanceExpiryJob;


class CheckWalletBalanceExpiry extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'CheckWalletBalanceExpiry';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to check wallet balance expiry';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // get all chains which have QB wallet management enabled
        $chainSettingsTable = Constants::getGlobalTableName("chainSettings");
        $chainSettingsData = convertToArray(DB::table($chainSettingsTable)->where("enableQBWalletManagement", 1)->select("chainRefID")->get());
        $count = count($chainSettingsData);

        // loop through each chain and dispatch the job
        for ($j=0; $j < $count; $j++) {
            $chainID = $chainSettingsData[$j]["chainRefID"];
            $queueNumber = queueNumber($chainID);
            $dataJob = (new WalletBalanceExpiryJob($chainID))
                            ->onQueue($queueNumber)
                            ->delay(10);
            dispatch($dataJob);
        }
    }
}
