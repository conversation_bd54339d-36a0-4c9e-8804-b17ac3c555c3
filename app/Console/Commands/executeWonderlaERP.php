<?php
namespace App\Console\Commands;


use App\Jobs\postWonderlaERPJob;
use DB;
use App\Order;
use Constants;
use App\JsonToExcel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class executeWonderlaERP extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    //protected $signature = 'postAdvancePaymentDLF:{value1}{value2}';
    protected $signature = 'executeWonderlaERP {argument1} {argument2}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to push all the data to Wonderla ERP';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle(){

        $chainID = $this->argument('argument1');
        $posDate = $this->argument('argument2');

        self::postWonderlaERPTracker($chainID,$posDate);
    }

    public static function postWonderlaERPTracker($chainID, $posDate)
    {
       
       $wonderlaIntegrationTrackerTable = Constants::getChainTableName($chainID,"wonderlaIntegrationTracker");

       $wonderlaIntegrationTrackerDetail = convertToArray(DB::table($wonderlaIntegrationTrackerTable)->where('posDate', $posDate)->where('isSynced',0)->get());

       for($i = 0;$i<count($wonderlaIntegrationTrackerDetail);$i++){

            //will remove this check
            if($wonderlaIntegrationTrackerDetail[$i]['sourceOperationType'] != 'ORDER'){
                continue;
            }

            $postERPArray = [];
            $postERPArray['sourceOperationID'] = $wonderlaIntegrationTrackerDetail[$i]['sourceOperationID'];
            $postERPArray['sourceOperationType'] = $wonderlaIntegrationTrackerDetail[$i]['sourceOperationType'];

            $queueNumber = queueNumber($chainID);
            $job = (new postWonderlaERPJob($postERPArray,$chainID))->onQueue("$queueNumber")->delay(5);
            Log::info("dispatching postWonderlaERPJob for chainID = ".$chainID." and sourceOperationID = ".$postERPArray['sourceOperationID']." and sourceOperationType: ".$postERPArray['sourceOperationType']);
            dispatch($job);
       }

    }


}