<?php
namespace App\Console\Commands;


use Illuminate\Console\Command;
use DB;
use Constants;
use App\SalesOrder AS SalesOrder;
use Illuminate\Support\Facades\Log as Log;
class updateFulfillment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'updateFulfillment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to fulfill all the expired UniqueReferenceIDs present in salesOrderFulfillmentTracker table';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle(){
        self::updateFulfillment();
    }

    public static function updateFulfillment(){

        if(env('APP_ENV') == 'production' || env('APP_ENV') == 'staging'){
            $chainIDs = [];
        } else{
            $chainIDs = [3025,3448,3363,3112];
        }

        try {

            for($i=0;$i<count($chainIDs);$i++){

                $chainID = $chainIDs[$i];

                $salesOrderFulfillmentTrackerTable = Constants::getChainTableName($chainID, 'salesOrderFulfillmentTracker');
                $salesOrderSummaryTable = Constants::getChainTableName($chainID, 'salesOrderSummary');
                $currentDate = date('Y-m-d');

                $salesOrderFulfillmentTrackerDetail = convertToArray(DB::table($salesOrderFulfillmentTrackerTable)->where('isFulfilled', 0)->whereRaw('expiryDate < ? OR visitDate < ?', [$currentDate, $currentDate])->get());

                $uniueSalesOrderIDs = array_unique(array_column($salesOrderFulfillmentTrackerDetail, 'salesOrderID'));

                $salesOrderSummaryDetail = convertToArray(DB::table($salesOrderSummaryTable)->whereIn('salesOrderID', $uniueSalesOrderIDs)->get());

                $salesOrderMapArray = array();

                for($i = 0;$i<count($salesOrderSummaryDetail);$i++){
                    $salesOrderMapArray[$salesOrderSummaryDetail[$i]['salesOrderID']] = $salesOrderSummaryDetail[$i];
                }


                for($i = 0;$i<count($salesOrderFulfillmentTrackerDetail);$i++){

                    if($salesOrderFulfillmentTrackerDetail[$i]['isFulfilled'] == 0){

                        $salesOrderID = $salesOrderFulfillmentTrackerDetail[$i]['salesOrderID'];

                        $fulfillmentArray = array();
                        $fulfillmentArray['channelID'] = $salesOrderMapArray[$salesOrderID]['channelID'];
                        $fulfillmentArray['isUnique'] = 1;
                        $fulfillmentArray['salesOrderID'] = $salesOrderID;
                        $fulfillmentArray['storeID'] = $salesOrderFulfillmentTrackerDetail[$i]['storeID'];
                        $fulfillmentArray['timezone'] = $salesOrderMapArray[$salesOrderID]['timezone'];
                        $fulfillmentArray['isSystemGenerated'] = 1;
                        $fulfillmentArray['fulfillmentTimeLocal'] = date('Y-m-d H:i:s');
                        $fulfillmentArray['posDate'] = date('Y-m-d');
                        $fulfillmentArray['customerID'] = $salesOrderMapArray[$salesOrderID]['customerID'];
                        $fulfillmentArray['deviceID'] = -1;
                        $fulfillmentArray['billingUsername'] = "SCHEDULAR";
                        $fulfillmentArray['fulfilledQuantity'] = $salesOrderFulfillmentTrackerDetail[$i]['quantity'];
                        $fulfillmentArray['grossFulfillmentValue'] = $salesOrderFulfillmentTrackerDetail[$i]['productValue'];


                        $productsList = array();
                        $productsList[0]['uniqueReferenceID'] = $salesOrderFulfillmentTrackerDetail[$i]['uniqueReferenceID'];
                        $productsList[0]['productID'] = $salesOrderFulfillmentTrackerDetail[$i]['productID'];
                        $productsList[0]['salesOrderSubID'] = $salesOrderFulfillmentTrackerDetail[$i]['salesOrderSubID'];
                        $productsList[0]['batchVariantID'] = $salesOrderFulfillmentTrackerDetail[$i]['batchVariantID'];
                        $productsList[0]['quantity'] = $salesOrderFulfillmentTrackerDetail[$i]['quantity'];
                        $productsList[0]['productValue'] = $salesOrderFulfillmentTrackerDetail[$i]['productValue'];
                        $fulfillmentArray['productsList'] = $productsList;
                        
                        try{
                            $fulfillmentArray['response'] = SalesOrder::updateFulfillmentTracker($fulfillmentArray, $chainID);
                        }catch(\Exception $e){
                            echo $e->getMessage();
                            $fulfillmentArray['response'] = $e->getMessage();
                        }
                        Log::info("Fulfillment response: ".json_encode($fulfillmentArray['response']));
                        DB::table($salesOrderFulfillmentTrackerTable)->where('uniqueReferenceID', $salesOrderFulfillmentTrackerDetail[$i]['uniqueReferenceID'])->update(['response' => json_encode($fulfillmentArray['response'])]);
                    }
                    
                }
            }    
        } catch (\Exception $e) {
            echo $e->getMessage();//
        }
    }
}