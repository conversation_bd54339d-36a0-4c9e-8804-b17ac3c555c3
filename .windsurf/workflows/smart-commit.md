---
description: Automatically generate a commit message from staged files and commit them
---

# Smart Commit Workflow

This workflow automatically generates a commit message based on the files staged for commit and then commits those files after user confirmation.

## Steps

1. Check which files are staged for commit:
```bash
git diff --name-only --cached
```

2. Generate a summary of changes in the staged files:
```bash
git diff --cached --stat
```

3. View the actual changes in the staged files for more details:
```bash
git diff --cached
```

4. Automatically generate a commit message based on the staged files:
```bash
# Auto-generate title based on most common directory changed
most_changed_dir=$(git diff --cached --name-only | xargs dirname 2>/dev/null | sort | uniq -c | sort -nr | head -n1 | awk '{print $2}')
file_count=$(git diff --cached --name-only | wc -l | tr -d ' ')

# Determine file types for better categorization
php_files=$(git diff --cached --name-only | grep -c "\.php$")
js_files=$(git diff --cached --name-only | grep -c "\.js$")
css_files=$(git diff --cached --name-only | grep -c "\.css$")

# Determine commit type based on file types and changes
if [ $php_files -gt 0 ] && [ $(git diff --cached | grep -c "^+\s*function") -gt 0 ]; then
  commit_type="feat"
elif [ $php_files -gt 0 ] && [ $(git diff --cached | grep -c "^+\s*class") -gt 0 ]; then
  commit_type="feat"
elif [ $php_files -gt 0 ] && [ $(git diff --cached | grep -c "^+\s*public function test") -gt 0 ]; then
  commit_type="test"
elif [ $(git diff --cached | grep -c "^+\s*\(fix\|bug\|issue\)") -gt 0 ]; then
  commit_type="fix"
elif [ $(git diff --cached --name-only | grep -c "composer.json") -gt 0 ]; then
  commit_type="deps"
elif [ $(git diff --cached --name-only | grep -c "package.json") -gt 0 ]; then
  commit_type="deps"
elif [ $(git diff --cached --name-only | grep -c "\.md$") -gt 0 ]; then
  commit_type="docs"
elif [ $js_files -gt 0 ] || [ $css_files -gt 0 ]; then
  commit_type="ui"
else
  commit_type="chore"
fi

# Create commit title
if [ -n "$most_changed_dir" ] && [ "$most_changed_dir" != "." ]; then
  dir_name=$(basename $most_changed_dir)
  commit_title="$commit_type: Update $dir_name ($file_count files)"
else
  # Fallback if no common directory
  first_file=$(git diff --cached --name-only | head -n1 | xargs basename)
  commit_title="$commit_type: Update $first_file"
  if [ "$file_count" -gt 1 ]; then
    commit_title="$commit_title and $(($file_count-1)) other files"
  fi
fi

# Auto-generate body with list of changed files and stats
commit_body="Files changed:\n\n$(git diff --cached --name-only | sed 's/^/- /')"

# Add stats if there are multiple files
if [ "$file_count" -gt 1 ]; then
  commit_body="$commit_body\n\nStats:\n- PHP files: $php_files\n- JS files: $js_files\n- CSS files: $css_files"
fi

# Create temporary file for the commit message
commit_msg_file=$(mktemp)
echo "$commit_title" > "$commit_msg_file"
echo "" >> "$commit_msg_file"
echo -e "$commit_body" >> "$commit_msg_file"

# Show the generated commit message
echo "\n---- Generated Commit Message ----"
cat "$commit_msg_file"
echo "--------------------------------"

echo "\nProceed with this commit message? (y/n)"
read proceed

if [ "$proceed" = "y" ] || [ "$proceed" = "Y" ]; then
  git commit -F "$commit_msg_file"
  echo "Commit successful!"
else
  echo "Commit aborted. Your files are still staged."
fi

# Clean up
rm "$commit_msg_file"
```

5. If you want an even simpler one-line approach, use this command instead:
```bash
git commit -m "$(git diff --cached --name-only | grep -q "\.php$" && echo "feat" || echo "chore"): Update $(basename $(git diff --cached --name-only | head -n1)) ($(git diff --cached --name-only | wc -l | tr -d ' ') files)" -m "$(git diff --cached --name-only | sed 's/^/- /' | tr '\n' '\r' | sed 's/\r/\n/g')"
```

## Notes

- This workflow automatically creates meaningful commit messages based on what's actually being changed
- The auto-generated message intelligently categorizes changes based on file types and content
- The commit type prefix follows conventional commit standards (feat, fix, docs, etc.)
- The workflow is optimized for Laravel/PHP projects but works for any codebase
- Only user confirmation is required before finalizing the commit
