name: PHPScan

on:
 pull_request:
   branches: [ qbapi-dev ]

jobs:
  phpstan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
            fetch-depth: ${{ github.event_name == 'pull_request' && 2 || 0 }}
      - name: Get changed files
        id: changed-files
        run: |
            if ${{ github.event_name == 'pull_request' }}; then
                echo "changed_files=$(git diff --name-only -r HEAD^1 HEAD | xargs)" >> $GITHUB_OUTPUT
            else
                echo "changed_files=$(git diff --name-only ${{ github.event.before }} ${{ github.event.after }} | xargs)" >> $GITHUB_OUTPUT
            fi
      - name: List changed files
        run: |
            for file in ${{ steps.changed-files.outputs.changed_files }}; do
                echo "$file was changed"
            done
      - name: Bug Analysis
        uses: php-actions/phpstan@v3
        with:
          path: ${{ steps.changed-files.outputs.changed_files }}
          php_version: 7.0
          error_format: pretty<PERSON><PERSON>
