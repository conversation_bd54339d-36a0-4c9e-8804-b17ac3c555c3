// babel
const axios = require('axios');
const orders = [
    "OR26-101247-58","OR26-101592-41","OR26-106772-43","OR26-107871-166","OR26-108048-70","OR26-108474-73","OR26-102619-133","OR26-104989-84","OR26-107871-179","OR26-100332-54","OR26-110307-118","OR26-101068-60","OR26-100588-136","OR26-104119-163","OR26-104119-164","OR26-100588-158","OR26-101068-78","OR26-75908-251","OR26-102619-329","OR26-101137-151","OR26-101592-124","OR26-103140-196","OR26-108048-206","OR26-75909-215","OR26-80128-255","OR26-99837-150","OR26-99837-153","REF262580128-4","OR26-109019-266","OR26-100588-269","OR26-104524-82","OR26-104524-83","OR26-102531-349","OR26-102619-370","OR26-110772-210","OR26-75909-243","OR26-110453-151","OR26-75908-373","REF262575908-7","OR26-101137-160","OR26-103021-94","OR26-103140-216","OR26-75909-253","OR26-104448-245","OR26-100130-148","OR26-100130-150","OR26-109019-302","OR26-109019-303","OR26-109019-304","OR26-100840-353","OR26-110772-262","OR26-112992-57","OR26-112992-60","OR26-112992-61","OR26-112992-71","OR26-112992-79","OR26-100494-359","OR26-75909-272","OR26-75909-279","OR26-75909-280","OR26-75909-284","OR26-110453-175","OR26-110453-178","OR26-106772-146","OR26-106772-148","OR26-108474-259","OR26-110731-210","OR26-103144-126","OR26-101068-122","OR26-101592-143","OR26-104119-317","OR26-104119-328","OR26-103355-208","OR26-80128-304","OR26-103140-263","OR26-104989-265","OR26-110839-7","OR26-109295-124","OR26-102531-380","OR26-103140-287","OR26-103140-281","OR26-99214-524","OR26-113208-62","REF2625110307-23","OR26-**********","OR26-**********","OR26-100075-5","OR26-**********","OR26-**********","OR26-**********","OR26-**********","REF2625102619-26","OR26-**********","OR26-**********","OR26-**********","OR26-**********","OR26-75908-801","OR26-**********","OR26-**********","OR26-**********"
];

for (let index = 0; index < orders.length; index++) {
    const orderID = orders[index];
    
    let data = JSON.stringify({
        "orderID": orderID,
        "receipt_type": "tax_invoice",
        "type": "creation"
    });
    
    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://api.queuebuster.co/API/public/v2/merchant/48195/repushRecordsToBillme',
        headers: { 
        'Authorization': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJxYi5hcHBhcmVsIiwiaXNzIjoiaHR0cHM6XC9cL3F1ZXVlYnVzdGVyLmNvIiwiYXVkIjoiaHR0cHM6XC9cL3F1ZXVlYnVzdGVyLmNvIiwiaWF0IjoxNzQ4OTQ2ODM4LCJleHAiOjE3NDg5NTQwMzgsImV4dHJhcGFyYW0iOiIxMDI2ZGYyOWI3ODI1NmVkYWRlMWU5MzgyMWU3MzgwN2JlNGMzMTRjIn0.T0-aewA2L0-B1RRyCEneWuYrIqEoCdh5SfwItCew_yI', 
        'Content-Type': 'application/json'
        },
        data : data
    };
    
    axios.request(config)
    .then((response) => {
        console.log(orderID + " " + JSON.stringify(response.data));
    })
    .catch((error) => {
        console.log(orderID + " " + error);
    });
     
}